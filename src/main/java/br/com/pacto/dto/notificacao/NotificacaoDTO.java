package br.com.pacto.dto.notificacao;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * DTO de notificações para o novo sistema de Treino.
 *
 * <AUTHOR>
 * @since 12/07/2018
 */
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ApiModel(description = "Dados de uma notificação recebida pelo professor no TreinoWeb.")
public class NotificacaoDTO {

    @ApiModelProperty(value = "Código único identificador da notificação", example = "4123")
    private String id;

    @ApiModelProperty(value = "Tipo da notificação. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- 0 - INICIOU_TREINO (Iniciou o treino)" +
            "- 1 - AUMENTOU_CARGA (Aumentou carga)" +
            "- 2 - DIMINUIU_CARGA (Diminuiu carga)" +
            "- 3 - CONCLUIU_TREINO (Concluiu treino)" +
            "- 4 - CHAMAR_ATENCAO_PROFESSOR (Chamou professor)" +
            "- 5 - CONCLUIU_SERIE (Concluiu uma série)" +
            "- 6 - EDITOU_SERIE (Editou uma série)" +
            "- 7 - ALUNO_CHEGOU (Chegou)" +
            "- 8 - ALUNO_SOLICITA_REAGENDA (Deseja reagendar)" +
            "- 9 - AGENDA (Lembrete agendamento)" +
            "- 10 - AGENDAMENTO_CONFIRMADO (Agendamento confirmado)" +
            "- 11 - AGENDAMENTO_CANCELADO (Agendamento cancelado)" +
            "- 12 - SOLICITAR_RENOVACAO (Solicitar renovação de treino)" +
            "- 13 - LEMBRAR_ALUNO_COMPROMISSO (Lembrete Compromisso)" +
            "- 14 - AGENDAMENTO_NOVO (Novo compromisso)" +
            "- 15 - AGENDAMENTO_ALTERADO (Compromisso alterado)" +
            "- 16 - ALUNO_EM_RISCO (Aluno chegou e possui índice de risco)" +
            "- 17  - ALUNO_AGENDOU (Aluno reagendou)" +
            "- 18  - CONTATO_CRM (Contato CRM)" +
            "- 19  - FEED_APP_LIKE (Like)" +
            "- 20  - FEED_APP_COMENTARIO (Comentário)" +
            "- 21  - DICAS_NUTRI_RESPOSTA (Resposta Comentário)" +
            "- 21  - ALUNO_DA_FILA_ENTROU_NA_AULA (A vaga para sua aula foi liberada)" +
            "- 22 - AGENDAMENTO_LOCACAO_CANCELADO (Agendamento da locação cancelado)", example = "AUMENTOU_CARGA")
    private String type;

    @ApiModelProperty(value = "URL da imagem associada à notificação", example = "https://academia.com/imagens/notificacao.png")
    private String imageUri;

    @ApiModelProperty(value = "Indica se a notificação já foi visualizada", example = "false")
    private Boolean seen;

    @ApiModelProperty(value = "Dados adicionais associados à notificação, geralmente em formato JSON", example = "{\"entidadeId\": 123, \"entidade\": \"treino\"}")
    private String payload;

    @ApiModelProperty(value = "Nível de gravidade da notificação", example = "GRAVE")
    private String gravidade;

    @ApiModelProperty(value = "Timestamp de quando a notificação foi registrada (Timestamp em milissegundos)", example = "1717957200000")
    private Long dataRegistro;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public Boolean getSeen() {
        return seen;
    }

    public void setSeen(Boolean seen) {
        this.seen = seen;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getGravidade() {
        return gravidade;
    }

    public void setGravidade(String gravidade) {
        this.gravidade = gravidade;
    }

    public Long getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Long dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

}
