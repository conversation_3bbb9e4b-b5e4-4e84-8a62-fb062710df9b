package br.com.pacto.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados básicos de um colaborador do sistema ZW (TreinoWeb), contendo identificação, nome e imagem")
public class ColaboradorDTO {

    @ApiModelProperty(value = "ID único do colaborador no sistema ZW", example = "1001")
    Integer id;

    @ApiModelProperty(value = "Nome completo do colaborador", example = "<PERSON>")
    String nome;

    @ApiModelProperty(value = "URI da imagem/foto do colaborador", example = "https://sistema.exemplo.com/imagens/colaborador1001.jpg")
    private String imageUri;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

}
