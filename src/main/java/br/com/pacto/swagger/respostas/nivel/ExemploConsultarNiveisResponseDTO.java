package br.com.pacto.swagger.respostas.nivel;

import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.swagger.respostas.ExemploPaginadorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representação da resposta de status 200 para consulta paginada de níveis encapsulada em EnvelopeRespostaDTO")
public class ExemploConsultarNiveisResponseDTO extends ExemploPaginadorDTO {

    @ApiModelProperty(value = "Lista paginada contendo os níveis encontrados conforme os filtros especificados")
    private List<NivelResponseTO> content;

    public List<NivelResponseTO> getContent() {
        return content;
    }

    public void setContent(List<NivelResponseTO> content) {
        this.content = content;
    }
}
