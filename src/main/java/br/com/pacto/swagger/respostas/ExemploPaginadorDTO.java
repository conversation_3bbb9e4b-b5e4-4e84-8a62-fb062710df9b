package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Classe base para exemplos de resposta paginada encapsulada em EnvelopeRespostaDTO
 * Contém os atributos de paginação que são incluídos quando há PaginadorDTO na resposta
 */
@ApiModel(description = "Estrutura base para respostas paginadas encapsuladas em EnvelopeRespostaDTO")
public class ExemploPaginadorDTO {

    @ApiModelProperty(value = "Número total de elementos encontrados na consulta", example = "150")
    private Long totalElements;

    @ApiModelProperty(value = "Número total de páginas disponíveis", example = "15")
    private Long totalPages;

    @ApiModelProperty(value = "Indica se esta é a primeira página", example = "true")
    private Boolean first;

    @ApiModelProperty(value = "Indica se esta é a última página", example = "false")
    private Boolean last;

    @ApiModelProperty(value = "Número de elementos retornados nesta página", example = "10")
    private Long numberOfElements;

    @ApiModelProperty(value = "Tamanho da página (quantidade máxima de elementos por página)", example = "10")
    private Long size;

    @ApiModelProperty(value = "Número da página atual (baseado em zero)", example = "0")
    private Long number;

    public Long getTotalElements() {
        return totalElements;
    }

    public void setTotalElements(Long totalElements) {
        this.totalElements = totalElements;
    }

    public Long getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Long totalPages) {
        this.totalPages = totalPages;
    }

    public Boolean getFirst() {
        return first;
    }

    public void setFirst(Boolean first) {
        this.first = first;
    }

    public Boolean getLast() {
        return last;
    }

    public void setLast(Boolean last) {
        this.last = last;
    }

    public Long getNumberOfElements() {
        return numberOfElements;
    }

    public void setNumberOfElements(Long numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getNumber() {
        return number;
    }

    public void setNumber(Long number) {
        this.number = number;
    }
}
