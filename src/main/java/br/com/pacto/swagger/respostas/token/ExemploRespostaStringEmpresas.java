package br.com.pacto.swagger.respostas.token;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Representação da resposta de status 200 para obtenção de códigos das empresas")
public class ExemploRespostaStringEmpresas {

    @ApiModelProperty(value = "String contendo os códigos das empresas vinculadas ao usuário, separados por vírgula", 
                     example = "123,456,789")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
