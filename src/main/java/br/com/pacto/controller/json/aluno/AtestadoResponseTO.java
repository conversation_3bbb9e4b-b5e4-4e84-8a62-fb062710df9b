package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import servicos.integracao.zw.json.AtestadoClienteJSON;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do atestado médico associado ao aluno.")
public class AtestadoResponseTO {

    @ApiModelProperty(value = "Identificador do atestado", example = "42")
    private Integer id;

    @ApiModelProperty(value = "Descrição ou motivo do atestado", example = "Atestado por tendinite no ombro")
    private String descricao;

    @ApiModelProperty(value = "Data de início de validade do atestado", example = "2024-05-01T00:00:00.000Z")
    private Date dataInicio;

    @ApiModelProperty(value = "Data de término de validade do atestado", example = "2024-05-15T00:00:00.000Z")
    private Date dataFinal;

    @ApiModelProperty(value = "Observações adicionais sobre o atestado", example = "Atestado entregue pessoalmente e assinado pelo ortopedista.")
    private String observacao;

    @ApiModelProperty(value = "Indica se o atestado está relacionado ao PAR-Q", example = "true")
    private Boolean parq;

    @ApiModelProperty(value = "Nome do arquivo digitalizado do atestado", example = "atestado_joao_2024.pdf")
    private String nomeArquivo;

    @ApiModelProperty(value = "URL para download ou visualização do anexo", example = "https://academia.com/anexos/atestado_joao_2024.pdf")
    private String urlAnexo;


    public AtestadoResponseTO (AtestadoClienteJSON atestadoClienteJSON){
        this.id = atestadoClienteJSON.getCodAtestado();
        this.dataInicio = atestadoClienteJSON.getDataInicio();
        this.dataFinal = atestadoClienteJSON.getDataFinal() ;
        this.descricao = atestadoClienteJSON.getDescricao();
        this.observacao = atestadoClienteJSON.getObservacao();
        this.parq = atestadoClienteJSON.getParq();
        if(atestadoClienteJSON.getExtensao() != null && !atestadoClienteJSON.getExtensao().isEmpty()) {
            this.nomeArquivo = atestadoClienteJSON.getNomeArquivoGerado();
        }
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getParq() {
        return parq;
    }

    public void setParq(Boolean parq) {
        this.parq = parq;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getUrlAnexo() {
        return urlAnexo;
    }

    public void setUrlAnexo(String urlAnexo) {
        this.urlAnexo = urlAnexo;
    }
}
