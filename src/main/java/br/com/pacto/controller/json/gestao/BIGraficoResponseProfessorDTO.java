package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
@ApiModel(description = "Dados básicos de um professor incluído na consulta de gráfico BI")
public class BIGraficoResponseProfessorDTO {
    @ApiModelProperty(value = "Identificador único do professor", example = "123")
    private String id;

    @ApiModelProperty(value = "Nome completo do professor", example = "<PERSON>")
    private String nome;

     public BIGraficoResponseProfessorDTO( ){

     }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
