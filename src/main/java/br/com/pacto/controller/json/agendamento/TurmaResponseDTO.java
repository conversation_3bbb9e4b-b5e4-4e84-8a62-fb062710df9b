package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.aulaDia.MapaEquipamentoAparelhoDTO;
import br.com.pacto.controller.json.modalidade.ModalidadeSimplesDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by paulo 08/08/2019
 */
@ApiModel(description = "Dados completos de uma turma incluindo alunos, professor, modalidade e configurações")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurmaResponseDTO {

    @ApiModelProperty(value = "Código identificador único do horário da turma", example = "15")
    private Integer horarioTurmaId;

    @ApiModelProperty(value = "Data da aula no formato dd/MM/yyyy", example = "20/01/2024")
    private String dia;

    @ApiModelProperty(value = "Nome da turma ou aula", example = "Musculação Iniciantes")
    private String nome;

    @ApiModelProperty(value = "Indica se a aula está com capacidade máxima", example = "false")
    private Boolean aulaCheia;

    @ApiModelProperty(value = "Horário de início da aula no formato HH:mm", example = "08:00")
    private String horarioInicio;

    @ApiModelProperty(value = "Horário de término da aula no formato HH:mm", example = "09:00")
    private String horarioFim;

    @ApiModelProperty(value = "Dados do ambiente onde a aula será realizada")
    private AmbienteResponseTO ambiente;

    @ApiModelProperty(value = "Dados da modalidade da aula")
    private ModalidadeSimplesDTO modalidade;

    @ApiModelProperty(value = "Dados do professor responsável pela aula")
    private ColaboradorSimplesTO professor;

    @ApiModelProperty(value = "Dados do professor substituto (se houver)")
    private ColaboradorSimplesTO professorSubstituto;

    @ApiModelProperty(value = "Dados do nível da turma")
    private NivelResponseTO nivel;

    @ApiModelProperty(value = "Capacidade máxima de alunos na turma", example = "20")
    private Integer capacidade;

    @ApiModelProperty(value = "Número atual de alunos matriculados na turma", example = "15")
    private Integer numeroAlunos;

    @ApiModelProperty(value = "Indica se permite aula experimental", example = "true")
    private Boolean permitirAulaExperimental;

    @ApiModelProperty(value = "Lista de alunos matriculados na turma")
    private List<AlunoTurmaDTO> alunos = new ArrayList<>();

    @ApiModelProperty(value = "Indica se permite alunos de outras unidades", example = "false")
    private Boolean permiteAlunoOutraEmpresa;

    @ApiModelProperty(value = "Indica se a turma está bloqueada para novas matrículas", example = "false")
    private Boolean bloqueado = Boolean.FALSE;

    @ApiModelProperty(value = "Indica se deve atualizar a agenda após alterações", example = "false")
    private Boolean atualizarAgenda = Boolean.FALSE;

    @ApiModelProperty(value = "Indica se permite fixar alunos na turma", example = "false")
    private Boolean permiteFixar = Boolean.FALSE;

    @ApiModelProperty(value = "Indica se é uma aula coletiva", example = "true")
    private Boolean aulaColetiva = Boolean.FALSE;

    @ApiModelProperty(value = "Indica se bloqueia matrículas acima do limite", example = "false")
    private Boolean bloquearMatriculasAcimaLimite = Boolean.FALSE;

    @ApiModelProperty(value = "Mapa de equipamentos da turma em formato JSON", example = "{\"equipamentos\": [\"A1\", \"A2\"]}")
    private String mapaEquipamentos;

    @ApiModelProperty(value = "Lista de equipamentos ocupados", example = "A1,A3,B2")
    private String equipamentosOcupados;

    @ApiModelProperty(value = "Lista detalhada do mapeamento de equipamentos e aparelhos")
    private List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho;

    public TurmaResponseDTO() {
    }

    public Integer getHorarioTurmaId() {
        return horarioTurmaId;
    }

    public void setHorarioTurmaId(Integer horarioTurmaId) {
        this.horarioTurmaId = horarioTurmaId;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAulaCheia() {
        return aulaCheia;
    }

    public void setAulaCheia(Boolean aulaCheia) {
        this.aulaCheia = aulaCheia;
    }

    public String getHorarioInicio() {
        return horarioInicio;
    }

    public void setHorarioInicio(String horarioInicio) {
        this.horarioInicio = horarioInicio;
    }

    public String getHorarioFim() {
        return horarioFim;
    }

    public void setHorarioFim(String horarioFim) {
        this.horarioFim = horarioFim;
    }

    public AmbienteResponseTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteResponseTO ambiente) {
        this.ambiente = ambiente;
    }

    public ModalidadeSimplesDTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeSimplesDTO modalidade) {
        this.modalidade = modalidade;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public ColaboradorSimplesTO getProfessorSubstituto() {
        return professorSubstituto;
    }

    public void setProfessorSubstituto(ColaboradorSimplesTO professorSubstituto) {
        this.professorSubstituto = professorSubstituto;
    }

    public NivelResponseTO getNivel() {
        return nivel;
    }

    public void setNivel(NivelResponseTO nivel) {
        this.nivel = nivel;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getNumeroAlunos() {
        return numeroAlunos;
    }

    public void setNumeroAlunos(Integer numeroAlunos) {
        this.numeroAlunos = numeroAlunos;
    }

    public Boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(Boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public List<AlunoTurmaDTO> getAlunos() {
        if (alunos == null) {
            alunos = new ArrayList<>();
        }
        return alunos;
    }

    public void setAlunos(List<AlunoTurmaDTO> alunos) {
        if (getAlunos().size() > 0) {
            getAlunos().clear();
        }
        getAlunos().addAll(alunos);
    }

    public Boolean getPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(Boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public Boolean getBloqueado() {
        return bloqueado;
    }

    public void setBloqueado(Boolean bloqueado) {
        this.bloqueado = bloqueado;
    }

    public Boolean getAtualizarAgenda() {
        return atualizarAgenda;
    }

    public void setAtualizarAgenda(Boolean atualizarAgenda) {
        this.atualizarAgenda = atualizarAgenda;
    }

    public String getPeriodo(){
        return horarioInicio + "-" + horarioFim;
    }

    public Boolean getPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public Boolean getAulaColetiva() {
        return aulaColetiva;
    }

    public void setAulaColetiva(Boolean aulaColetiva) {
        this.aulaColetiva = aulaColetiva;
    }

    public Boolean getBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(Boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public String getEquipamentosOcupados() {
        return equipamentosOcupados;
    }

    public void setEquipamentosOcupados(String equipamentosOcupados) {
        this.equipamentosOcupados = equipamentosOcupados;
    }

    public List<MapaEquipamentoAparelhoDTO> getListaMapaEquipamentoAparelho() {
        return listaMapaEquipamentoAparelho;
    }

    public void setListaMapaEquipamentoAparelho(List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho) {
        this.listaMapaEquipamentoAparelho = listaMapaEquipamentoAparelho;
    }

}
