package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * Created paulo 07/11/2018
 */
@ApiModel(description = "Histórico de peso e massa gorda do aluno em diferentes datas.")
public class PesoXGorduraDTO {

    @ApiModelProperty(value = "Data da medição de peso e massa gorda")
    private Date data;

    @ApiModelProperty(value = "Peso do aluno na data registrada, em kg", example = "74.2")
    private Double peso;

    @ApiModelProperty(value = "Massa gorda do aluno na data registrada, em kg", example = "19.7")
    private Double massaGorda;

    public PesoXGorduraDTO(AvaliacaoFisica avaliacaoFisica) {
        this.data = avaliacaoFisica.getDataAvaliacao();
        this.peso = avaliacaoFisica.getPeso();
        this.massaGorda = avaliacaoFisica.getMassaGorda();
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }
}
