package br.com.pacto.controller.json.tvGestor.controlador;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.tvGestor.dto.FiltroTvGestorJSON;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.tvGestor.TvGestorService;
import br.com.pacto.swagger.respostas.tvGestor.ExemploRespostaBiSemanaAtual;
import br.com.pacto.swagger.respostas.tvGestor.ExemploRespostaBiPeriodo;
import br.com.pacto.swagger.respostas.tvGestor.ExemploRespostaListAlunoTvGestorPaginacao;
import br.com.pacto.swagger.respostas.tvGestor.ExemploRespostaListAlunoTvGestor;
import br.com.pacto.swagger.respostas.tvGestor.ExemploRespostaTvGestorFiltroResponse;
import io.swagger.annotations.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by erik-qo on 21/10/2019.
 */

@Controller
@RequestMapping("/psec/tv-gestor")
public class TvGestorController {

    @Autowired
    private TvGestorService tvGestorServiceIntegrado;
    @Autowired
    private SessaoService sessaoService;

    @ApiOperation(value = "Obter dados de Business Intelligence da semana atual para TV Gestor",
                  notes = "Retorna dados estatísticos de comparecimento e agendamentos da semana atual (7 dias) organizados por dia e hora para exibição na TV Gestor.",
                  tags = "TV Gestor")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Dados de BI da semana atual retornados com sucesso", response = ExemploRespostaBiSemanaAtual.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/bi-semana-atual", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biSemanaAtual(
            @ApiParam(value = "Lista de IDs dos professores para filtrar os dados", example = "[1, 2, 3]")
            @RequestParam(value = "professorIds", required = false) List<Integer> professorIds,
            @ApiParam(value = "Lista de IDs das modalidades para filtrar os dados", example = "[1, 2]")
            @RequestParam(value = "modalidadeIds", required = false) List<Integer> modalidadeIds,
            @ApiParam(value = "Lista de IDs dos ambientes para filtrar os dados", example = "[1, 2]")
            @RequestParam(value = "ambienteIds", required = false) List<Integer> ambienteIds,
            @ApiParam(value = "ID da empresa", required = true, example = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            FiltroTvGestorJSON filtro = new FiltroTvGestorJSON(modalidadeIds, professorIds, ambienteIds);
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.biSemana(chave, empresaId, filtro));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi da semana", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Obter dados de Business Intelligence do período para TV Gestor",
                  notes = "Retorna dados estatísticos de comparecimento e agendamentos dos últimos 30 dias organizados por dia para exibição na TV Gestor.",
                  tags = "TV Gestor")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Dados de BI do período retornados com sucesso", response = ExemploRespostaBiPeriodo.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/bi-periodo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biPeriodo(
            @ApiParam(value = "Lista de IDs dos professores para filtrar os dados", example = "[1, 2, 3]")
            @RequestParam(value = "professorIds", required = false) List<Integer> professorIds,
            @ApiParam(value = "Lista de IDs das modalidades para filtrar os dados", example = "[1, 2]")
            @RequestParam(value = "modalidadeIds", required = false) List<Integer> modalidadeIds,
            @ApiParam(value = "Lista de IDs dos ambientes para filtrar os dados", example = "[1, 2]")
            @RequestParam(value = "ambienteIds", required = false) List<Integer> ambienteIds,
            @ApiParam(value = "ID da empresa", required = true, example = "1")
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            FiltroTvGestorJSON filtro = new FiltroTvGestorJSON(modalidadeIds, professorIds, ambienteIds);
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.biPeriodo(chave, empresaId, filtro));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Listar alunos para TV Gestor com filtros e paginação",
                  notes = "Retorna lista paginada de alunos com informações específicas para exibição na TV Gestor, incluindo dados de turma e acesso.",
                  tags = "TV Gestor")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nomeAluno,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de alunos retornada com sucesso", response = ExemploRespostaListAlunoTvGestorPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunos(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno ou nome da turma.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nomeAluno\", \"nomeTurma\"]).\n" +
                    "- <strong>modalidadeIds:</strong> Filtra por uma ou mais modalidades (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>professorIds:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>ambienteIds:</strong> Filtra por um ou mais ambientes (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>data:</strong> Data específica para filtrar os alunos (timestamp em milissegundos ex: 1640995200000).\n" +
                    "- <strong>hora:</strong> Hora específica para filtrar os alunos (ex: 14).",
                    defaultValue = "{\"quicksearchValue\":\"João\", \"quicksearchFields\":[\"nomeAluno\"], \"modalidadeIds\":[1], \"professorIds\":[10], \"ambienteIds\":[1], \"data\":1640995200000, \"hora\":14}")
            @RequestParam(value = "filters") JSONObject filtros,
            @ApiParam(value = "ID da empresa", required = true, example = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO,
            HttpServletRequest request) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.getAlunosTvGestor(chave, empresaId, new FiltroTvGestorJSON(filtros), paginadorDTO, request), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Listar acessos de alunos para TV Gestor",
                  notes = "Retorna lista de alunos que acessaram a academia no dia atual com informações de turma e horário de acesso para exibição na TV Gestor.",
                  tags = "TV Gestor")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de acessos retornada com sucesso", response = ExemploRespostaListAlunoTvGestor.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/acessos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> acessos(
            @ApiParam(value = "Lista de IDs dos professores para filtrar os acessos", example = "[1, 2, 3]")
            @RequestParam(value = "professorIds", required = false) List<Integer> professorIds,
            @ApiParam(value = "Lista de IDs das modalidades para filtrar os acessos", example = "[1, 2]")
            @RequestParam(value = "modalidadeIds", required = false) List<Integer> modalidadeIds,
            @ApiParam(value = "Lista de IDs dos ambientes para filtrar os acessos", example = "[1, 2]")
            @RequestParam(value = "ambienteIds", required = false) List<Integer> ambienteIds,
            @ApiParam(value = "ID da empresa", required = true, example = "1")
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            String chave = sessaoService.getUsuarioAtual().getChave();
            FiltroTvGestorJSON filtro = new FiltroTvGestorJSON(modalidadeIds, professorIds, ambienteIds);
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.biAcessos(chave, empresaId, filtro, request));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Obter filtros salvos do usuário para TV Gestor",
                  notes = "Retorna os filtros de modalidades, ambientes e professores salvos nas configurações do usuário para uso na TV Gestor.",
                  tags = "TV Gestor")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Filtros do usuário retornados com sucesso", response = ExemploRespostaTvGestorFiltroResponse.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/filtros", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> filtros(
            @ApiParam(value = "ID da empresa", required = true, example = "1")
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.getFiltro(usuarioSimplesDTO.getChave(), empresaId, usuarioSimplesDTO.getId(), request));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Atualizar filtros do usuário para TV Gestor",
                  notes = "Salva os filtros de modalidades, ambientes e professores nas configurações do usuário para uso na TV Gestor.",
                  tags = "TV Gestor")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Filtros do usuário atualizados com sucesso", response = ExemploRespostaTvGestorFiltroResponse.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.TVGESTOR)
    @RequestMapping(value = "/filtros", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> filtros(
            @ApiParam(value = "Dados dos filtros a serem salvos", required = true)
            @RequestBody FiltroTvGestorJSON filtro,
            @ApiParam(value = "ID da empresa", required = true, example = "1")
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request) {
        try {
            UsuarioSimplesDTO usuarioSimplesDTO = sessaoService.getUsuarioAtual();
            return ResponseEntityFactory.ok(tvGestorServiceIntegrado.atualizarFiltros(usuarioSimplesDTO.getChave(), empresaId, usuarioSimplesDTO.getId(), filtro,request));
        } catch (ServiceException e) {
            Logger.getLogger(TvGestorController.class.getName()).log(Level.SEVERE, "Erro ao tentar gerar bi do periodo", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }
}
