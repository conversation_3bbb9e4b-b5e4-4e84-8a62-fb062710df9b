package br.com.pacto.controller.json.benchmark;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.benchmark.BenchmarkService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.benchmark.ExemploRespostaBenchmarkResponseTO;
import br.com.pacto.swagger.respostas.benchmark.ExemploRespostaListBenchmarkResponseTO;
import br.com.pacto.swagger.respostas.benchmark.ExemploRespostaListBenchmarkResponseTOPaginacao;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by joao moita on 28/09/2018.
 */
@Api(tags = "Benchmarks")
@Controller
@RequestMapping("/psec/benchmarks")
public class BenchmarkController {

    private final BenchmarkService benchmarkService;

    @Autowired
    public BenchmarkController(BenchmarkService benchmarkService) {
        Assert.notNull(benchmarkService, "O serviço de benchmark não foi injetado corretamente");
        this.benchmarkService = benchmarkService;
    }

    @ApiOperation(
            value = "Cadastrar novo benchmark",
            notes = "Cadastra um novo benchmark no sistema com informações como nome, tipo, exercícios, observações e mídias. " +
                    "Um benchmark é um treino padrão utilizado para medir e comparar o desempenho dos atletas.",
            tags = "Benchmarks"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBenchmarkResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroBenchmark(
            @ApiParam(value = "Dados completos do benchmark para cadastro", required = true)
            @RequestBody BenchmarkTO benchmarkTO) {
        try {
            return ResponseEntityFactory.ok(benchmarkService.cadastroBenchmark(benchmarkTO));
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Listar todos os benchmarks",
            notes = "Consulta todos os benchmarks cadastrados no sistema sem filtros ou paginação. " +
                    "Retorna uma lista completa com todos os benchmarks disponíveis.",
            tags = "Benchmarks"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListBenchmarkResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaTodosBenchmarks() {
        try {
            return ResponseEntityFactory.ok(benchmarkService.listarTodosBenchmarks());
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter detalhes de um benchmark",
            notes = "Consulta os detalhes completos de um benchmark específico através do seu ID. " +
                    "Retorna todas as informações do benchmark incluindo tipo, exercícios, observações e mídias.",
            tags = "Benchmarks"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBenchmarkResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesBenchmark(
            @ApiParam(value = "ID único do benchmark", defaultValue = "123", required = true)
            @PathVariable("id") Integer integer) {
        try {
            return ResponseEntityFactory.ok(benchmarkService.detalhesBenchmark(integer));
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar detalhes benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar benchmarks com filtros e paginação",
            notes = "Consulta benchmarks com suporte a filtros avançados e paginação. " +
                    "Permite filtrar por nome, tipo de benchmark e tipo de exercício. " +
                    "Retorna uma lista paginada com os benchmarks encontrados conforme os critérios especificados.",
            tags = "Benchmarks"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", dataType = "int", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", dataType = "int", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'nome,ASC'). " +
                    "<br/><b>Campos disponíveis:</b><br/>" +
                    "<b>nome</b> - Nome do benchmark<br/>" +
                    "<b>tipoBenchmark.nome</b> - Nome do tipo de benchmark<br/>" +
                    "<b>tipoWod</b> - Tipo de exercício",
                    dataType = "string", paramType = "query", defaultValue = "nome,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListBenchmarkResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaBenchmark(
            @ApiParam(value = "Filtros de pesquisa em formato JSON. " +
                    "<br/><b>Filtros disponíveis:</b><br/>" +
                    "<b>quicksearchValue:</b> Busca por nome do benchmark<br/>" +
                    "<b>tipoBenchmark:</b> Lista de IDs dos tipos de benchmark (ex: [1, 2, 3])<br/>" +
                    "<b>tipoExercicios:</b> Lista de tipos de exercício. Valores possíveis: 'FOR_TIME', 'FOR_REPS', 'FOR_WEIGHT', 'AMRAP' (ex: [\"FOR_TIME\", \"AMRAP\"])",
                    defaultValue = "{\"quicksearchValue\":\"Fran\",\"tipoBenchmark\":[1,2],\"tipoExercicios\":[\"FOR_TIME\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore
            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroBenchmarkJSON  filtroBenchmarkJSON = new FiltroBenchmarkJSON(filtros);

            return ResponseEntityFactory.ok(benchmarkService.listaBenchmark(filtroBenchmarkJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar benchmark existente",
            notes = "Atualiza as informações de um benchmark existente através do seu ID. " +
                    "Permite modificar nome, tipo, exercícios, observações e mídias do benchmark.",
            tags = "Benchmarks"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBenchmarkResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarBenchmark(
            @ApiParam(value = "ID único do benchmark a ser alterado", defaultValue = "123", required = true)
            @PathVariable(value = "id") Integer id,
            @ApiParam(value = "Dados atualizados do benchmark", required = true)
            @RequestBody BenchmarkTO benchmarkTO) {
        try {
            return ResponseEntityFactory.ok(benchmarkService.alterarBenchmark(id, benchmarkTO));
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar benchmark", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Remover benchmark",
            notes = "Remove um benchmark do sistema através do seu ID. " +
                    "Esta operação é irreversível e remove permanentemente o benchmark do banco de dados.",
            tags = "Benchmarks"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.BENCHMARK)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerBenchmark(
            @ApiParam(value = "ID único do benchmark a ser removido", defaultValue = "123", required = true)
            @PathVariable(value = "id") Integer id) {
        try {
            benchmarkService.removerBenchmark(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(BenchmarkController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover benchmark", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
