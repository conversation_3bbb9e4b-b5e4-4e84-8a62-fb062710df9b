package br.com.pacto.controller.json.turma;

import br.com.pacto.controller.json.empresa.EmpresaDTO;
import br.com.pacto.controller.json.modalidade.ModalidadeTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados completos da turma para resposta de requisições")
public class TurmaResponseDTO {

    @ApiModelProperty(value = "Código único identificador da turma", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da turma", example = "Turma de Spinning Avançado")
    private String nome;
    @ApiModelProperty(value = "Cor da turma em formato hexadecimal", example = "#FF5733")
    private String cor;
    @ApiModelProperty(value = "Identificador único da turma", example = "SPIN-ADV-001")
    private String identificador;
    @ApiModelProperty(value = "Modalidade vinculada à turma")
    private ModalidadeTO modalidade;
    @ApiModelProperty(value = "Empresa proprietária da turma")
    private EmpresaDTO empresa;
    @ApiModelProperty(value = "Idade mínima em anos para participar da turma", example = "16")
    private Integer idadeMinima;
    @ApiModelProperty(value = "Idade mínima em meses para participar da turma", example = "192")
    private Integer idadeMinimaMeses;
    @ApiModelProperty(value = "Idade máxima em anos para participar da turma", example = "65")
    private Integer idadeMaxima;
    @ApiModelProperty(value = "Idade máxima em meses para participar da turma", example = "780")
    private Integer idadeMaximaMeses;
    @ApiModelProperty(value = "Indica se há integração com SPIV", example = "true")
    private boolean integracaoSpiv;
    @ApiModelProperty(value = "Data inicial de vigência da turma", example = "01/01/2025")
    private String dataInicial;
    @ApiModelProperty(value = "Data final de vigência da turma", example = "31/12/2025")
    private String dataFinal;
    @ApiModelProperty(value = "Minutos de antecedência para desmarcar aula", example = "60")
    private Integer minutosAntecedenciaDesmarcarAula;
    @ApiModelProperty(value = "Minutos após início que ainda permite entrada no app", example = "15")
    private Integer minutosAposInicioApp;
    @ApiModelProperty(value = "Tipo de antecedência para marcar aula", example = "1")
    private Integer tipoAntecedenciaMarcarAula;
    @ApiModelProperty(value = "Minutos de antecedência para marcar aula", example = "30")
    private Integer minutosAntecedenciaMarcarAula;
    @ApiModelProperty(value = "Quantidade de níveis de ocupação", example = "3")
    private Integer qtdeNivelOcupacao;
    @ApiModelProperty(value = "Percentual de desconto para ocupação nível 1", example = "10.5")
    private Double percDescOcupacaoNivel1;
    @ApiModelProperty(value = "Percentual de desconto para ocupação nível 2", example = "15.0")
    private Double percDescOcupacaoNivel2;
    @ApiModelProperty(value = "Percentual de desconto para ocupação nível 3", example = "20.0")
    private Double percDescOcupacaoNivel3;
    @ApiModelProperty(value = "Percentual de desconto para ocupação nível 4", example = "25.0")
    private Double percDescOcupacaoNivel4;
    @ApiModelProperty(value = "Percentual de desconto para ocupação nível 5", example = "30.0")
    private Double percDescOcupacaoNivel5;
    @ApiModelProperty(value = "Indica se deve bloquear matrículas acima do limite", example = "true")
    private boolean bloquearMatriculasAcimaLimite;
    @ApiModelProperty(value = "Indica se permite desmarcar reposições", example = "false")
    private boolean permitirDesmarcarReposicoes;
    @ApiModelProperty(value = "Indica se permite aluno de outra empresa", example = "false")
    private boolean permiteAlunoOutraEmpresa;
    @ApiModelProperty(value = "Indica se a turma é monitorada", example = "true")
    private boolean monitorada;
    @ApiModelProperty(value = "Indica se deve bloquear reposição acima do limite", example = "true")
    private boolean bloquearReposicaoAcimaLimite;
    @ApiModelProperty(value = "Indica se permite aula experimental", example = "true")
    private boolean permitirAulaExperimental;
    @ApiModelProperty(value = "Indica se deve bloquear lotação futura", example = "false")
    private boolean bloquearLotacaoFutura;
    @ApiModelProperty(value = "URL do vídeo do YouTube da turma", example = "https://www.youtube.com/watch?v=abc123")
    private String urlVideoYoutube;
    @ApiModelProperty(value = "Lista de vídeos vinculados à turma")
    private List<TurmaVideoDTO> linkVideos;

    public TurmaResponseDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public ModalidadeTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeTO modalidade) {
        this.modalidade = modalidade;
    }

    public EmpresaDTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaDTO empresa) {
        this.empresa = empresa;
    }

    public Integer getIdadeMinima() {
        return idadeMinima;
    }

    public void setIdadeMinima(Integer idadeMinima) {
        this.idadeMinima = idadeMinima;
    }

    public Integer getIdadeMaxima() {
        return idadeMaxima;
    }

    public void setIdadeMaxima(Integer idadeMaxima) {
        this.idadeMaxima = idadeMaxima;
    }

    public boolean isIntegracaoSpiv() {
        return integracaoSpiv;
    }

    public void setIntegracaoSpiv(boolean integracaoSpiv) {
        this.integracaoSpiv = integracaoSpiv;
    }

    public String getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(String dataInicial) {
        this.dataInicial = dataInicial;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getMinutosAntecedenciaDesmarcarAula() {
        return minutosAntecedenciaDesmarcarAula;
    }

    public void setMinutosAntecedenciaDesmarcarAula(Integer minutosAntecedenciaDesmarcarAula) {
        this.minutosAntecedenciaDesmarcarAula = minutosAntecedenciaDesmarcarAula;
    }

    public Integer getMinutosAposInicioApp() {
        return minutosAposInicioApp;
    }

    public void setMinutosAposInicioApp(Integer minutosAposInicioApp) {
        this.minutosAposInicioApp = minutosAposInicioApp;
    }

    public Integer getTipoAntecedenciaMarcarAula() {
        return tipoAntecedenciaMarcarAula;
    }

    public void setTipoAntecedenciaMarcarAula(Integer tipoAntecedenciaMarcarAula) {
        this.tipoAntecedenciaMarcarAula = tipoAntecedenciaMarcarAula;
    }

    public Integer getMinutosAntecedenciaMarcarAula() {
        return minutosAntecedenciaMarcarAula;
    }

    public void setMinutosAntecedenciaMarcarAula(Integer minutosAntecedenciaMarcarAula) {
        this.minutosAntecedenciaMarcarAula = minutosAntecedenciaMarcarAula;
    }

    public Integer getQtdeNivelOcupacao() {
        return qtdeNivelOcupacao;
    }

    public void setQtdeNivelOcupacao(Integer qtdeNivelOcupacao) {
        this.qtdeNivelOcupacao = qtdeNivelOcupacao;
    }

    public Double getPercDescOcupacaoNivel1() {
        return percDescOcupacaoNivel1;
    }

    public void setPercDescOcupacaoNivel1(Double percDescOcupacaoNivel1) {
        this.percDescOcupacaoNivel1 = percDescOcupacaoNivel1;
    }

    public Double getPercDescOcupacaoNivel2() {
        return percDescOcupacaoNivel2;
    }

    public void setPercDescOcupacaoNivel2(Double percDescOcupacaoNivel2) {
        this.percDescOcupacaoNivel2 = percDescOcupacaoNivel2;
    }

    public Double getPercDescOcupacaoNivel3() {
        return percDescOcupacaoNivel3;
    }

    public void setPercDescOcupacaoNivel3(Double percDescOcupacaoNivel3) {
        this.percDescOcupacaoNivel3 = percDescOcupacaoNivel3;
    }

    public Double getPercDescOcupacaoNivel4() {
        return percDescOcupacaoNivel4;
    }

    public void setPercDescOcupacaoNivel4(Double percDescOcupacaoNivel4) {
        this.percDescOcupacaoNivel4 = percDescOcupacaoNivel4;
    }

    public Double getPercDescOcupacaoNivel5() {
        return percDescOcupacaoNivel5;
    }

    public void setPercDescOcupacaoNivel5(Double percDescOcupacaoNivel5) {
        this.percDescOcupacaoNivel5 = percDescOcupacaoNivel5;
    }

    public boolean isBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public boolean getBloquearMatriculasAcimaLimite() {
        return bloquearMatriculasAcimaLimite;
    }

    public void setBloquearMatriculasAcimaLimite(boolean bloquearMatriculasAcimaLimite) {
        this.bloquearMatriculasAcimaLimite = bloquearMatriculasAcimaLimite;
    }

    public boolean isPermitirDesmarcarReposicoes() {
        return permitirDesmarcarReposicoes;
    }

    public boolean getPermitirDesmarcarReposicoes() {
        return permitirDesmarcarReposicoes;
    }

    public void setPermitirDesmarcarReposicoes(boolean permitirDesmarcarReposicoes) {
        this.permitirDesmarcarReposicoes = permitirDesmarcarReposicoes;
    }

    public boolean isPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public boolean getPermiteAlunoOutraEmpresa() {
        return permiteAlunoOutraEmpresa;
    }

    public void setPermiteAlunoOutraEmpresa(boolean permiteAlunoOutraEmpresa) {
        this.permiteAlunoOutraEmpresa = permiteAlunoOutraEmpresa;
    }

    public boolean isMonitorada() {
        return monitorada;
    }

    public boolean getMonitorada() {
        return monitorada;
    }

    public void setMonitorada(boolean monitorada) {
        this.monitorada = monitorada;
    }

    public boolean isBloquearReposicaoAcimaLimite() {
        return bloquearReposicaoAcimaLimite;
    }

    public boolean getBloquearReposicaoAcimaLimite() {
        return bloquearReposicaoAcimaLimite;
    }

    public void setBloquearReposicaoAcimaLimite(boolean bloquearReposicaoAcimaLimite) {
        this.bloquearReposicaoAcimaLimite = bloquearReposicaoAcimaLimite;
    }

    public boolean isPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public boolean getPermitirAulaExperimental() {
        return permitirAulaExperimental;
    }

    public void setPermitirAulaExperimental(boolean permitirAulaExperimental) {
        this.permitirAulaExperimental = permitirAulaExperimental;
    }

    public boolean isBloquearLotacaoFutura() {
        return bloquearLotacaoFutura;
    }

    public boolean getBloquearLotacaoFutura() {
        return bloquearLotacaoFutura;
    }

    public void setBloquearLotacaoFutura(boolean bloquearLotacaoFutura) {
        this.bloquearLotacaoFutura = bloquearLotacaoFutura;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }
}
