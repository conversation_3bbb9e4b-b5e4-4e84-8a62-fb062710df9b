package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteObservacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Representa uma observação registrada para o aluno.")
public class AlunoObservacaoDTO {

    @ApiModelProperty(value = "Código da observação", example = "321")
    private Integer codigo;

    @ApiModelProperty(value = "ID do usuário que cadastrou a observação", example = "45")
    private Integer usuarioId;

    @ApiModelProperty(value = "ID do cliente/aluno vinculado à observação", example = "987")
    private Integer clienteId;

    @ApiModelProperty(value = "Indica se a observação é importante", example = "true")
    private Boolean importante;

    @ApiModelProperty(value = "Texto da observação", example = "Aluno relatou desconforto no joelho durante exercícios de agachamento.")
    private String observacao;

    @ApiModelProperty(value = "Data da observação (formato yyyyMMdd)", example = "20240515")
    private String data;

    @ApiModelProperty(value = "Indica se a observação está relacionada à avaliação física", example = "false")
    private Boolean avaliacaoFisica;

    @ApiModelProperty(value = "Anexo a ser encaminhado", example = "joelho-agachamento.pdf")
    private String anexo;

    @ApiModelProperty(value = "Arquivo de upload relacionado", example = "arquivoUpload")
    private String arquivoUpload;

    @ApiModelProperty(value = "Nome original do arquivo", example = "joelho-agachamento.pdf")
    private String nomeArquivo;

    @ApiModelProperty(value = "Formato do arquivo", example = "application/pdf")
    private String formatoArquivo;



    public AlunoObservacaoDTO(){

    }
    public AlunoObservacaoDTO (ClienteObservacao clienteObservacao){

        this.codigo = clienteObservacao.getCodigo();
        this.usuarioId = clienteObservacao.getUsuario_codigo();
        this.clienteId = clienteObservacao.getCliente().getCodigo();
        this.importante = clienteObservacao.getImportante();
        this.observacao = clienteObservacao.getObservacao();
        this.data = clienteObservacao.getDataObservacao().toString();
        this.avaliacaoFisica = clienteObservacao.getAvaliacaoFisica();
        this.anexo = clienteObservacao.getAnexoKey();

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getUsuarioId() {
        return usuarioId;
    }

    public void setUsuarioId(Integer usuarioId) {
        this.usuarioId = usuarioId;
    }

    public Integer getClienteId() {
        return clienteId;
    }

    public void setClienteId(Integer clienteId) {
        this.clienteId = clienteId;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public String getArquivoUpload() {
        return arquivoUpload;
    }

    public void setArquivoUpload(String arquivoUpload) {
        this.arquivoUpload = arquivoUpload;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getAnexo() {
        return anexo;
    }

    public void setAnexo(String anexo) {
        this.anexo = anexo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }
}
