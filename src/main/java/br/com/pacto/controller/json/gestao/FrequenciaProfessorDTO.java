package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "<PERSON><PERSON> de frequência por professor")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FrequenciaProfessorDTO {

    @ApiModelProperty(value = "Código identificador do professor", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do professor", example = "<PERSON>")
    private String nome = "";

    @ApiModelProperty(value = "URL da imagem/foto do professor", example = "https://exemplo.com/foto-professor.jpg")
    private String imageUri = "";

    @ApiModelProperty(value = "Percentual de frequência do professor", example = "85")
    private Integer frequencia;

    public FrequenciaProfessorDTO() {

    }
    public FrequenciaProfessorDTO(Integer id, String nome, String imageUri, Integer frequencia) {
        this.id = id;
        this.nome = nome;
        this.imageUri = imageUri;
        this.frequencia = frequencia;
    }



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public Integer getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(Integer frequencia) {
        this.frequencia = frequencia;
    }
}
