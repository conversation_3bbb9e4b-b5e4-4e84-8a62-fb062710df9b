package br.com.pacto.controller.json.aluno;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR> 16/01/2019
 */
@ApiModel(description = "Contém o detalhamento do treino do aluno, incluindo frequência, fichas e distribuição muscular.")
public class DetalheTreinoAlunoDTO {

    @ApiModelProperty(value = "Nome do aluno", example = "Carlos Alberto")
    private String nome;

    @ApiModelProperty(value = "Total de treinos previstos no período", example = "12")
    private Integer totalTreinoPrevisto;

    @ApiModelProperty(value = "Total de treinos realizados no período", example = "10")
    private Integer totalTreinoRealizado;

    @ApiModelProperty(value = "Frequência de comparecimento do aluno aos treinos", example = "83.3")
    private Double frequencia;

    @ApiModelProperty(value = "Frequência de treino às segundas-feiras")
    private PorcentagemDiaSemana segunda = new PorcentagemDiaSemana();

    @ApiModelProperty(value = "Frequência de treino às terças-feiras")
    private PorcentagemDiaSemana terca = new PorcentagemDiaSemana();

    @ApiModelProperty(value = "Frequência de treino às quartas-feiras")
    private PorcentagemDiaSemana quarta = new PorcentagemDiaSemana();

    @ApiModelProperty(value = "Frequência de treino às quintas-feiras")
    private PorcentagemDiaSemana quinta = new PorcentagemDiaSemana();

    @ApiModelProperty(value = "Frequência de treino às sextas-feiras")
    private PorcentagemDiaSemana sexta = new PorcentagemDiaSemana();

    @ApiModelProperty(value = "Frequência de treino aos sábados")
    private PorcentagemDiaSemana sabado = new PorcentagemDiaSemana();

    @ApiModelProperty(value = "Frequência de treino aos domingos")
    private PorcentagemDiaSemana domingo = new PorcentagemDiaSemana();

    @ApiModelProperty(value = "Última ficha de treino executada pelo aluno")
    private FichaExecutada ultimaFichaExecutada;

    @ApiModelProperty(value = "Ficha de treino atual em execução")
    private FichaExecutada fichaAtual;

    @ApiModelProperty(value = "Próxima ficha de treino programada")
    private FichaExecutada fichaProxima;

    @ApiModelProperty(value = "Distribuição muscular dos treinos do aluno")
    private List<DistribuicaoMusculoDTO> distribuicao;


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTotalTreinoPrevisto() {
        return totalTreinoPrevisto;
    }

    public void setTotalTreinoPrevisto(Integer totalTreinoPrevisto) {
        this.totalTreinoPrevisto = totalTreinoPrevisto;
    }

    public Integer getTotalTreinoRealizado() {
        return totalTreinoRealizado;
    }

    public void setTotalTreinoRealizado(Integer totalTreinoRealizado) {
        this.totalTreinoRealizado = totalTreinoRealizado;
    }

    public Double getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(Double frequencia) {
        this.frequencia = frequencia;
    }

    public PorcentagemDiaSemana getSegunda() {
        return segunda;
    }

    public void setSegunda(PorcentagemDiaSemana segunda) {
        this.segunda = segunda;
    }

    public PorcentagemDiaSemana getTerca() {
        return terca;
    }

    public void setTerca(PorcentagemDiaSemana terca) {
        this.terca = terca;
    }

    public PorcentagemDiaSemana getQuarta() {
        return quarta;
    }

    public void setQuarta(PorcentagemDiaSemana quarta) {
        this.quarta = quarta;
    }

    public PorcentagemDiaSemana getQuinta() {
        return quinta;
    }

    public void setQuinta(PorcentagemDiaSemana quinta) {
        this.quinta = quinta;
    }

    public PorcentagemDiaSemana getSexta() {
        return sexta;
    }

    public void setSexta(PorcentagemDiaSemana sexta) {
        this.sexta = sexta;
    }

    public PorcentagemDiaSemana getSabado() {
        return sabado;
    }

    public void setSabado(PorcentagemDiaSemana sabado) {
        this.sabado = sabado;
    }

    public PorcentagemDiaSemana getDomingo() {
        return domingo;
    }

    public void setDomingo(PorcentagemDiaSemana domingo) {
        this.domingo = domingo;
    }

    public FichaExecutada getUltimaFichaExecutada() {
        return ultimaFichaExecutada;
    }

    public void setUltimaFichaExecutada(FichaExecutada ultimaFichaExecutada) {
        this.ultimaFichaExecutada = ultimaFichaExecutada;
    }

    public FichaExecutada getFichaAtual() {
        return fichaAtual;
    }

    public void setFichaAtual(FichaExecutada fichaAtual) {
        this.fichaAtual = fichaAtual;
    }

    public List<DistribuicaoMusculoDTO> getDistribuicao() {
        return distribuicao;
    }

    public void setDistribuicao(List<DistribuicaoMusculoDTO> distribuicao) {
        this.distribuicao = distribuicao;
    }

    public FichaExecutada getFichaProxima() {
        return fichaProxima;
    }

    public void setFichaProxima(FichaExecutada fichaProxima) {
        this.fichaProxima = fichaProxima;
    }
}
