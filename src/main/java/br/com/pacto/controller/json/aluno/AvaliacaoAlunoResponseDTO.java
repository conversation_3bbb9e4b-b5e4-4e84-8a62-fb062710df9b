package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created paulo 06/11/2018
 */
@ApiModel(description = "Informações de avaliação física do aluno.")
public class AvaliacaoAlunoResponseDTO {

    @ApiModelProperty(value = "Código identificador da avaliação", example = "789")
    private Integer avaliacaoId;

    @ApiModelProperty(value = "Total de avaliações realizadas", example = "5")
    private Integer totalAvaliacoes;

    @ApiModelProperty(value = "Período em meses entre avaliações", example = "3")
    private Integer periodo;

    @ApiModelProperty(value = "Composição corporal do aluno")
    private ComposicaoCorporalDTO composicaoCorporal;

    @ApiModelProperty(value = "Índice de Massa Corporal (IMC)", example = "22.5")
    private String imc;

    @ApiModelProperty(value = "Altura do aluno em metros", example = "1.75")
    private String altura;

    @ApiModelProperty(value = "Peso do aluno em kg", example = "70.2")
    private String peso;

    @ApiModelProperty(value = "Circunferência abdominal em centímetros", example = "85.4")
    private Double circunferenciaAbdominal;

    @ApiModelProperty(value = "Percentual de massa magra", example = "42.3")
    private String percentualMassaMagra;

    @ApiModelProperty(value = "Percentual de gordura corporal", example = "17.8")
    private String percentualMassaGorda;

    @ApiModelProperty(value = "Data da última avaliação realizada", example = "2025-05-10")
    private Date ultimaAvaliacao;

    @ApiModelProperty(value = "Data prevista para a próxima avaliação", example = "2025-08-10")
    private Date proximaAvaliacao;

    @ApiModelProperty(value = "Histórico de peso e gordura corporal")
    private List<PesoXGorduraDTO> pesoXGordura;

    public AvaliacaoAlunoResponseDTO(AvaliacaoFisica avaliacaoFisica, int periodoAvaliacoes, int quantAvaliacoes, List<AvaliacaoFisica> listaAvaliacoes) {
        this.avaliacaoId = avaliacaoFisica.getCodigo();
        this.totalAvaliacoes = quantAvaliacoes;
        this.periodo = periodoAvaliacoes;
        this.composicaoCorporal = new ComposicaoCorporalDTO(avaliacaoFisica);
        this.imc = avaliacaoFisica.getImcApresentar();
        this.altura = avaliacaoFisica.getAlturaApresentar();
        this.peso = avaliacaoFisica.getPesoApresentar();
        this.circunferenciaAbdominal = avaliacaoFisica.getCircunferenciaAbdominal();
        this.percentualMassaMagra = avaliacaoFisica.getPercentualMassaMagraApresentar();
        this.percentualMassaGorda = avaliacaoFisica.getPercentualGorduraApresentar();
        this.ultimaAvaliacao = avaliacaoFisica.getDataAvaliacao();
        this.proximaAvaliacao = avaliacaoFisica.getDataProxima();
        this.pesoXGordura = listaAvaliacoes == null ? null : popularListaPesoXGordura(listaAvaliacoes);
    }

    private List<PesoXGorduraDTO> popularListaPesoXGordura(List<AvaliacaoFisica> avaliacoes) {
        List<PesoXGorduraDTO> listaReturn = new ArrayList<>();

        for (AvaliacaoFisica avaliacaoFisica : avaliacoes) {
            listaReturn.add(new PesoXGorduraDTO(avaliacaoFisica));
        }

        return listaReturn;
    }

    public Integer getAvaliacaoId() {
        return avaliacaoId;
    }

    public void setAvaliacaoId(Integer avaliacaoId) {
        this.avaliacaoId = avaliacaoId;
    }

    public Integer getTotalAvaliacoes() {
        return totalAvaliacoes;
    }

    public void setTotalAvaliacoes(Integer totalAvaliacoes) {
        this.totalAvaliacoes = totalAvaliacoes;
    }

    public Integer getPeriodo() {
        return periodo;
    }

    public void setPeriodo(Integer periodo) {
        this.periodo = periodo;
    }

    public ComposicaoCorporalDTO getComposicaoCorporal() {
        return composicaoCorporal;
    }

    public void setComposicaoCorporal(ComposicaoCorporalDTO composicaoCorporal) {
        this.composicaoCorporal = composicaoCorporal;
    }

    public String getImc() {
        return imc;
    }

    public void setImc(String imc) {
        this.imc = imc;
    }

    public String getAltura() {
        return altura;
    }

    public void setAltura(String altura) {
        this.altura = altura;
    }

    public String getPeso() {
        return peso;
    }

    public void setPeso(String peso) {
        this.peso = peso;
    }

    public Double getCircunferenciaAbdominal() {
        return circunferenciaAbdominal;
    }

    public void setCircunferenciaAbdominal(Double circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }

    public String getPercentualMassaMagra() {
        return percentualMassaMagra;
    }

    public void setPercentualMassaMagra(String percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public String getPercentualMassaGorda() {
        return percentualMassaGorda;
    }

    public void setPercentualMassaGorda(String percentualMassaGorda) {
        this.percentualMassaGorda = percentualMassaGorda;
    }

    public Date getUltimaAvaliacao() {
        return ultimaAvaliacao;
    }

    public void setUltimaAvaliacao(Date ultimaAvaliacao) {
        this.ultimaAvaliacao = ultimaAvaliacao;
    }

    public Date getProximaAvaliacao() {
        return proximaAvaliacao;
    }

    public void setProximaAvaliacao(Date proximaAvaliacao) {
        this.proximaAvaliacao = proximaAvaliacao;
    }

    public List<PesoXGorduraDTO> getPesoXGordura() {
        return pesoXGordura;
    }

    public void setPesoXGordura(List<PesoXGorduraDTO> pesoXGordura) {
        this.pesoXGordura = pesoXGordura;
    }
}
