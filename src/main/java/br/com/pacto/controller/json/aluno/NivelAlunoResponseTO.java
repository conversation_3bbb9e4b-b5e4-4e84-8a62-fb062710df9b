package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.nivel.Nivel;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 28/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações dos níveis dos alunos")
public class NivelAlunoResponseTO {
    @ApiModelProperty(value = "Código único identificador do nível", example = "45")
    private Integer id;
    @ApiModelProperty(value = "Nome do nível", example = "Intermediário 1")
    private String nome;

    public NivelAlunoResponseTO(Nivel nivel) {
        this.id = nivel.getCodigo();
        this.nome = nivel.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
