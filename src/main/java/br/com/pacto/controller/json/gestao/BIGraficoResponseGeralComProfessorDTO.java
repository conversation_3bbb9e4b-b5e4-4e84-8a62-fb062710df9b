package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
@ApiModel(description = "Resposta geral de dados de gráfico BI com separação por professor, contendo dados individualizados por professor")
public class BIGraficoResponseGeralComProfessorDTO {
    @ApiModelProperty(value = "Identificador único da view de gráfico BI", example = "Carteira")
    private String id;

    @ApiModelProperty(value = "Nome descritivo da view de gráfico BI", example = "Carteira")
    private String nome;

    @ApiModelProperty(value = "Indica se é uma view padrão do sistema ou personalizada pelo usuário", example = "false")
    private boolean padrao;

    @ApiModelProperty(value = "Lista dos indicadores selecionados para exibição no gráfico")
    private ArrayList<String> indicadores;

    @ApiModelProperty(value = "Lista de professores incluídos na consulta com seus dados básicos")
    private List<BIGraficoResponseProfessorDTO> professores;

    @ApiModelProperty(value = "Lista de dados dos indicadores organizados por período e professor")
    private List<BIGraficoResponseComProfessorDTO> dados;

     public BIGraficoResponseGeralComProfessorDTO( ){

     }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isPadrao() {
        return padrao;
    }

    public void setPadrao(boolean padrao) {
        this.padrao = padrao;
    }

    public ArrayList<String> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(ArrayList<String> indicadores) {
        this.indicadores = indicadores;
    }

    public List<BIGraficoResponseProfessorDTO> getProfessores() {
        return professores;
    }

    public void setProfessores(List<BIGraficoResponseProfessorDTO> professores) {
        this.professores = professores;
    }

    public List<BIGraficoResponseComProfessorDTO> getDados() {
        return dados;
    }

    public void setDados(List<BIGraficoResponseComProfessorDTO> dados) {
        this.dados = dados;
    }
}
