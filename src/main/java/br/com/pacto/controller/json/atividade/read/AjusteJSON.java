/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.read;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Informações de ajuste de aparelho para uma atividade")
public class AjusteJSON extends SuperJSON {

    @ApiModelProperty(value = "Nome do ajuste do aparelho", example = "Altura do banco")
    private String nome;

    @ApiModelProperty(value = "Valor do ajuste", example = "Posição 5")
    private String valor;

    @ApiModelProperty(value = "Código da atividade associada ao ajuste", example = "123")
    private Integer codAtividade;

    public AjusteJSON() {
    }

    public AjusteJSON(String nome, String valor) {
        this.nome = nome;
        this.valor = valor;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public Integer getCodAtividade() {
        if (codAtividade == null) {
            codAtividade = 0;
        }
        return codAtividade;
    }

    public void setCodAtividade(Integer codAtividade) {
        this.codAtividade = codAtividade;
    }
}
