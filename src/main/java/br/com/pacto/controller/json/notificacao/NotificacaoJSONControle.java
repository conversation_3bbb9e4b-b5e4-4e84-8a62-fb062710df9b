/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.notificacao;

import br.com.pacto.bean.notificacao.Notificacao;
import br.com.pacto.bean.notificacao.TipoNotificacaoEnum;
import br.com.pacto.bean.notificacao.VisibilidadeNotificacaoEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.notificacao.NotificacaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.notificacao.ExemploRespostaNotificacoesGet;
import br.com.pacto.swagger.respostas.notificacao.ExemploRespostaHistoricoNotificacoes;
import br.com.pacto.swagger.respostas.notificacao.ExemploRespostaNotificacaoString;
import br.com.pacto.swagger.respostas.notificacao.ExemploRespostaMarcarLida;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/notificacoes")
public class NotificacaoJSONControle extends SuperControle {

    @Autowired
    private NotificacaoService service;
    @Autowired
    private UsuarioService usuarioService;

    @ApiOperation(value = "Consultar notificações do usuário",
                  notes = "Retorna as notificações do usuário logado, podendo filtrar apenas as não lidas. " +
                          "Requer autenticação através do contexto da empresa. " +
                          "Retorna as notificações organizadas em duas categorias: academia e professor.",
                  tags = "Notificação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Notificações consultadas com sucesso", response = ExemploRespostaNotificacoesGet.class)
    })
    @RequestMapping(value = "{ctx}/get", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap get(@ApiParam(value = "Contexto da empresa para identificação do ambiente", defaultValue = "academia123")
                 @PathVariable String ctx,
                 @ApiParam(value = "Nome de usuário para identificação do aluno", defaultValue = "joao.silva")
                 @RequestParam String username,
                 @ApiParam(value = "Filtrar apenas notificações não lidas pelo usuário", defaultValue = "false")
                 @RequestParam(required = false, defaultValue = "false") Boolean apenasNaoLidas,
                 @ApiParam(value = "Matrícula do aluno como alternativa ao username", defaultValue = "12345")
                 @RequestParam(required = false) String matricula
                 ) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            //Notificacao obj = service...;
            Usuario usuario;
            if(UteisValidacao.emptyString(matricula)){
                usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = usuarioService.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }
            if (usuario != null) {
                List<Notificacao> lista = service.obterMaisNotificacoes(ctx, null, usuario.getCliente().getCodigo(), apenasNaoLidas,
                        10, 0, VisibilidadeNotificacaoEnum.ALUNO);
                List<NotificacaoJSON> arrJSON = new ArrayList<NotificacaoJSON>();
                for (Notificacao notificacao : lista) {
                    //verificar se a notificação ñ se tornou obsoleta
                    if ((notificacao.getTipo() == TipoNotificacaoEnum.SOLICITAR_RENOVACAO
                            || notificacao.getTipo() == TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO) && notificacao.getCliente() != null
                            && notificacao.getPrograma() != null) {
                        if (notificacao.getPrograma().getDataRenovacao() != null) {
                            continue;
                        }
                    }
                    if(notificacao.getTipo() == TipoNotificacaoEnum.SOLICITAR_RENOVACAO
                            && service.isTreinoVigente(ctx, usuario.getCliente().getCodigo())) {
                        return null;
                    }

                    arrJSON.add(notificacao.toJSONObject());
                }
                mm.addAttribute("academia", arrJSON);
                mm.addAttribute("professor", new ArrayList<NotificacaoJSON>());
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar histórico de notificações do cliente",
                  notes = "Retorna o histórico paginado de notificações de um cliente específico. " +
                          "Requer autenticação através do contexto da empresa. " +
                          "Permite navegação através de índice e quantidade máxima de resultados.",
                  tags = "Notificação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Histórico de notificações consultado com sucesso", response = ExemploRespostaHistoricoNotificacoes.class)
    })
    @RequestMapping(value = "{ctx}/historico", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap historico(@ApiParam(value = "Contexto da empresa para identificação do ambiente", defaultValue = "academia123")
                       @PathVariable final String ctx,
                       @ApiParam(value = "Código do cliente para consulta do histórico", defaultValue = "1234")
                       @RequestParam final Integer codigoCliente,
                       @ApiParam(value = "Índice inicial para paginação dos resultados", defaultValue = "0")
                       @RequestParam final Integer index,
                       @ApiParam(value = "Quantidade máxima de resultados por página", defaultValue = "10")
                       @RequestParam final Integer maxResults) {
        ModelMap mm = new ModelMap();
        try {
            //super.init(ctx, token);
            if (codigoCliente != null && codigoCliente > 0) {
                List<Notificacao> lista = service.obterMaisNotificacoes(ctx, null, codigoCliente, false,
                        maxResults, index, VisibilidadeNotificacaoEnum.ALUNO);
                List<NotificacaoJSON> arrJSON = new ArrayList<NotificacaoJSON>();
                for (Notificacao notificacao : lista) {
                    //verificar se a notificação ñ se tornou obsoleta
                    if ((notificacao.getTipo() == TipoNotificacaoEnum.SOLICITAR_RENOVACAO
                            || notificacao.getTipo() == TipoNotificacaoEnum.LEMBRAR_ALUNO_COMPROMISSO) && notificacao.getCliente() != null
                            && notificacao.getPrograma() != null) {
                        if (notificacao.getPrograma().getDataRenovacao() != null) {
                            continue;
                        }
                    }
                    arrJSON.add(notificacao.toJSONObject());
                }
                mm.addAttribute(RETURN, arrJSON);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    
    
    @ApiOperation(value = "Registrar resposta de notificação",
                  notes = "Registra a resposta do usuário para uma notificação específica. " +
                          "Requer autenticação através do contexto da empresa. " +
                          "Retorna a notificação atualizada com a resposta registrada.",
                  tags = "Notificação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Resposta da notificação registrada com sucesso", response = ExemploRespostaNotificacaoString.class)
    })
    @RequestMapping(value = "{ctx}/respostaNotificacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap respostaNotificacao(@ApiParam(value = "Contexto da empresa para identificação do ambiente", defaultValue = "academia123")
                                 @PathVariable String ctx,
                                 @ApiParam(value = "Identificador único da notificação", defaultValue = "1001")
                                 @RequestParam String idNotf,
                                 @ApiParam(value = "Resposta do usuário à notificação", defaultValue = "Confirmado")
                                 @RequestParam String reposta) {
        ModelMap mm = new ModelMap();
        try {
            Notificacao notf = service.gravarResposta(ctx, Integer.valueOf(idNotf), reposta);
            mm.addAttribute(RETURN, notf.toJSON());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    
    @ApiOperation(value = "Gerar notificação CRM para cliente",
                  notes = "Gera uma nova notificação do tipo CRM para um cliente específico. " +
                          "Requer autenticação através do contexto da empresa. " +
                          "Retorna a notificação criada em formato JSON.",
                  tags = "Notificação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Notificação CRM gerada com sucesso", response = ExemploRespostaNotificacaoString.class)
    })
    @RequestMapping(value = "{ctx}/gerarNotificacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarNotificacaoCRM(@ApiParam(value = "Contexto da empresa para identificação do ambiente", defaultValue = "academia123")
                                 @PathVariable String ctx,
                                 @ApiParam(value = "Identificador do cliente que receberá a notificação", defaultValue = "1234")
                                 @RequestParam String idCliente,
                                 @ApiParam(value = "Título da notificação CRM", defaultValue = "Contato da Academia")
                                 @RequestParam String titulo,
                                 @ApiParam(value = "Texto da mensagem CRM para o cliente", defaultValue = "Olá João! Que tal agendar uma avaliação física?")
                                 @RequestParam String textoCRM,
                                 @ApiParam(value = "Opções de resposta disponíveis para o cliente", defaultValue = "Agendar|Mais tarde")
                                 @RequestParam String opcoes) {
        ModelMap mm = new ModelMap();
        try {
            Notificacao notf = service.gerarNotificacao(ctx, Integer.valueOf(idCliente), Calendario.hoje(),
                    titulo, 
                    textoCRM, 
                    TipoNotificacaoEnum.CONTATO_CRM,
                    opcoes, false);
            mm.addAttribute(RETURN, notf.toJSON());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Marcar notificações como lidas",
                  notes = "Marca uma notificação específica ou todas as notificações do usuário como lidas. " +
                          "Requer autenticação através do contexto da empresa. " +
                          "Retorna a quantidade de notificações marcadas como lidas.",
                  tags = "Notificação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Notificações marcadas como lidas com sucesso", response = ExemploRespostaMarcarLida.class)
    })
    @RequestMapping(value = "{ctx}/marcarLida", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarLida(@ApiParam(value = "Contexto da empresa para identificação do ambiente", defaultValue = "academia123")
                        @PathVariable String ctx,
                        @ApiParam(value = "Nome de usuário para identificação do aluno", defaultValue = "joao.silva")
                        @RequestParam String username,
                        @ApiParam(value = "Identificador da notificação específica a ser marcada como lida", defaultValue = "1001")
                        @RequestParam(required = false) Integer idNotificacao,
                        @ApiParam(value = "Matrícula do aluno como alternativa ao username", defaultValue = "12345")
                        @RequestParam(required = false) String matricula,
                        @ApiParam(value = "Indica se deve marcar todas as notificações como lidas", defaultValue = "false")
                        @RequestParam boolean marcarTodas) {
        ModelMap mm = new ModelMap();
        try {
            if (!marcarTodas && UteisValidacao.emptyNumber(idNotificacao)) {
                throw new ServiceException("idNotificacao não informada.");
            }
            mm.addAttribute(RETURN, "Notificaçoes marcadas como lida. Total " +
                    service.marcarLidas(ctx, username, matricula, idNotificacao, marcarTodas));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Gerar notificação por email",
                  notes = "Gera uma nova notificação enviada por email para um destinatário específico. " +
                          "Requer autenticação através do contexto da empresa. " +
                          "Retorna a notificação criada em formato JSON.",
                  tags = "Notificação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Notificação por email gerada com sucesso", response = ExemploRespostaNotificacaoString.class)
    })
    @RequestMapping(value = "{ctx}/gerarNotificacaoEmail", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarNotificacaoEmail(@ApiParam(value = "Contexto da empresa para identificação do ambiente", defaultValue = "academia123")
                                   @PathVariable String ctx,
                                   @ApiParam(value = "Endereço de email do destinatário", defaultValue = "<EMAIL>")
                                   @RequestParam String email,
                                   @ApiParam(value = "Título da notificação por email", defaultValue = "Lembrete de Treino")
                                   @RequestParam String titulo,
                                   @ApiParam(value = "Texto da mensagem da notificação", defaultValue = "Não esqueça do seu treino hoje às 18h!")
                                   @RequestParam String texto,
                                   @ApiParam(value = "Opções de resposta disponíveis", defaultValue = "Confirmar|Reagendar")
                                   @RequestParam String opcoes,
                                   @ApiParam(value = "Código do tipo de notificação conforme TipoNotificacaoEnum", defaultValue = "9")
                                   @RequestParam Integer tipoNotificacao) {
        ModelMap mm = new ModelMap();
        try {
//            String msgHomologacao = "[sandbox]" + texto;
            Notificacao notf = service.gerarNotificacaoPorEmail(ctx, email, titulo, texto, opcoes, Calendario.hoje(), TipoNotificacaoEnum.obterPorID(tipoNotificacao));
            mm.addAttribute(RETURN, notf.toJSON());
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/delete", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarNotificacaoEmail(@PathVariable String ctx,
                                   @RequestParam Integer codigo) {
        ModelMap mm = new ModelMap();
        try {
            Notificacao notf = service.obterPorId(ctx, codigo);
            service.excluir(ctx, notf);
            mm.addAttribute(RETURN, "Notificação removida com sucesso.");
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(NotificacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
}
