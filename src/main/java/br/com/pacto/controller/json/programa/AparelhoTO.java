package br.com.pacto.controller.json.programa;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by joa<PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de entrada para criação ou alteração de aparelho")
public class AparelhoTO {

    @ApiModelProperty(value = "Código único identificador do aparelho (usado apenas para alteração)", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do aparelho", example = "Esteira Ergométrica", required = true)
    private String nome;

    @ApiModelProperty(value = "Lista de códigos dos ajustes associados ao aparelho", example = "[1, 2, 3]")
    private List<Integer> ajusteIds = new ArrayList<>();

    @ApiModelProperty(value = "Lista de códigos das atividades associadas ao aparelho", example = "[10, 15, 20]")
    private List<Integer> atividadeIds = new ArrayList<>();

    @ApiModelProperty(value = "Lista de nomes de novos ajustes a serem criados para o aparelho", example = "[\"Altura do banco\", \"Inclinação\"]")
    private List<String> novosAjustes = new ArrayList<>();

    @ApiModelProperty(value = "Indica se o aparelho é específico para crossfit", example = "false")
    private Boolean crossfit = false;

    @ApiModelProperty(value = "Sigla ou abreviação do aparelho", example = "EST")
    private String sigla;

    @ApiModelProperty(value = "Nome do ícone representativo do aparelho", example = "treadmill-icon")
    private String icone;

    @ApiModelProperty(value = "Indica se o aparelho pode ser usado em reservas de equipamento", example = "true")
    private Boolean usarEmReservaEquipamentos = false;

    @ApiModelProperty(value = "Código do sensor Selfloop associado ao aparelho", example = "SL001")
    private String sensorSelfloops;

    public AparelhoTO(){

    }

    public AparelhoTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<Integer> getAjusteIds() {
        return ajusteIds;
    }

    public void setAjusteIds(List<Integer> ajusteIds) {
        this.ajusteIds = ajusteIds;
    }

    public List<Integer> getAtividadeIds() {
        return atividadeIds;
    }

    public void setAtividadeIds(List<Integer> atividadeIds) {
        this.atividadeIds = atividadeIds;
    }

    public List<String> getNovosAjustes() {
        return novosAjustes;
    }

    public void setNovosAjustes(List<String> novosAjustes) {
        this.novosAjustes = novosAjustes;
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public Boolean getUsarEmReservaEquipamentos() {
        if (usarEmReservaEquipamentos == null) {
            return false;
        }
        return usarEmReservaEquipamentos;
    }

    public void setUsarEmReservaEquipamentos(Boolean usarEmReservaEquipamentos) {
        this.usarEmReservaEquipamentos = usarEmReservaEquipamentos;
    }

    public String getSensorSelfloops() {
        return sensorSelfloops;
    }

    public void setSensorSelfloops(String sensorSelfloops) {
        this.sensorSelfloops = sensorSelfloops;
    }
}
