package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Histórico de contatos e observações do aluno")
public class HistoricoContatoAlunoVO {

    @ApiModelProperty(value = "Texto do contato ou observação realizada", example = "Cliente relatou interesse em aulas de spinning")
    private String texto;

    @ApiModelProperty(value = "Origem do contato. Valores possíveis: 'Contato CRM', 'Observação Treino', 'Observação ADM'", example = "Contato CRM")
    private String origem;

    @ApiModelProperty(value = "Data do contato ou observação", example = "2024-06-15")
    private Date dataContrato;

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Date getDataContrato() {
        return dataContrato;
    }

    public void setDataContrato(Date dataContrato) {
        this.dataContrato = dataContrato;
    }
}
