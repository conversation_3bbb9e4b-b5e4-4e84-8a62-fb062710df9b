package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.atividade.CategoriaAtividadeTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.musculo.GrupoMuscularController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.atividade.CategoriaAtividadeService;
import br.com.pacto.swagger.respostas.categoria.atividade.ExemploRespostaCategoriaAtividadeResponseTO;
import br.com.pacto.swagger.respostas.categoria.atividade.ExemploRespostaListCategoriaAtividadeResponseTO;
import br.com.pacto.swagger.respostas.categoria.atividade.ExemploRespostaListCategoriaAtividadeResponseTOPaginacao;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 23/08/2018.
 */

@Controller
@RequestMapping("/psec/categoria-atividade")
public class CategoriaAtividadeController {

    private CategoriaAtividadeService categoriaAtividadeService;

    @Autowired
    public CategoriaAtividadeController(CategoriaAtividadeService categoriaAtividadeService) {
        Assert.notNull(categoriaAtividadeService, "O serviço de categoria atividade não foi injetado corretamente");
        this.categoriaAtividadeService = categoriaAtividadeService;
    }

    @ApiOperation(
            value = "Consultar categorias de atividade física",
            notes = "Consulta as informações das categorias de atividades físicas.",
            tags = "Categoria de Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.\n\n Ordena por um atributo contido no objeto da resposta.\n\n" +
                    "<strong>Ordens disponíveis</strong>\n\n" +
                    "- <strong>ASC</strong> - Ordena pelo atributo escolhido de forma ascendente\n" +
                    "- <strong>DESC</strong> - Ordena pelo atributo escolhido de forma descendente\n\n" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListCategoriaAtividadeResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarCategoriaAtividades(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong>Filtra pelo nome da categoria de atividade</li>" +
                    "</ul>", defaultValue = "{\"quicksearchValue\":\"Musculação\"}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroCategoriaAtividadeJSON filtroCategoriaAtividadeJSON = new FiltroCategoriaAtividadeJSON(filtros);
            return ResponseEntityFactory.ok(categoriaAtividadeService.consultarCategoriaAtividades(filtroCategoriaAtividadeJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(GrupoMuscularController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as categorias de atividades.", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar categoria de atividade física",
            notes = "Consulta as informações de uma categoria de atividade física através do código identificador dela.",
            tags = "Categoria de Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaCategoriaAtividadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarCategoriaAtividade(
            @ApiParam(value = "Código da categoria que será consultada", defaultValue = "1", required = true)
            @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.consultarCategoriaAtividade(id));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o Categoria de Atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Cadastrar categoria de atividade física",
            notes = "Cadastra as informações de uma categoria de atividade física.",
            tags = "Categoria de Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaCategoriaAtividadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirCategoriaAtividade(
            @ApiParam(value = "Informações para cadastrar uma nova categoria de atividade.")
            @RequestBody CategoriaAtividadeTO categoriaAtividadeTO) {
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.inserir(categoriaAtividadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir Categoria de Atividade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Atualizar categoria de atividade física",
            notes = "Atualiza as informações de uma categoria de atividade física.",
            tags = "Categoria de Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaCategoriaAtividadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarCategoriaAtividade(
            @ApiParam(value = "Código da categoria que será alterada", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Informações da categoria que serão atualizadas. Não é necessário informar o atributo id.")
            @RequestBody CategoriaAtividadeTO categoriaAtividadeTO) {
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.alterar(id, categoriaAtividadeTO));

        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar Categoria de Atividade", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Deletar categoria de atividade física",
            notes = "Exclui as informações de uma categoria de atividade física.",
            tags = "Categoria de Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirCategoriaAtividade(
            @ApiParam(value = "Código da categoria que será excluída", required = true, defaultValue = "1")
            @PathVariable("id") final Integer id) {
        try {
            categoriaAtividadeService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir Categoria de Atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todas as categorias de atividade física",
            notes = "Consulta todas as categorias de atividades físicas.",
            tags = "Categoria de Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListCategoriaAtividadeResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_ATIVIDADE)
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(categoriaAtividadeService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaAtividadeController.class.getName()).log(Level.SEVERE, "Erro ao consultar todas as categorias de atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
