package br.com.pacto.controller.json.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo on 12/11/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para criação do colaborador coordenador inicial da empresa")
public class CreateClientDTO {

    @ApiModelProperty(value = "Nome completo do colaborador coordenador", example = "<PERSON>", required = true)
    private String nome;

    @ApiModelProperty(value = "E-mail do colaborador que será usado como login", example = "<EMAIL>", required = true)
    private String email;

    @ApiModelProperty(value = "Número do celular do colaborador", example = "(11) 99999-9999")
    private String celular;

    @ApiModelProperty(value = "Senha de acesso do colaborador", example = "senha123", required = true)
    private String senha;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

}
