package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.ficha.CategoriaFichaResponseTO;
import br.com.pacto.bean.ficha.FiltroCategoriaFichaJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.CategoriaFichaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.swagger.respostas.programa.ExemploRespostaCategoriaFichaResponseTO;
import br.com.pacto.swagger.respostas.programa.ExemploRespostaListCategoriaFichaResponseTOPaginacao;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;
import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
    @RequestMapping("/psec/categoria-ficha")
public class CategoriaFichaController {
    CategoriaFichaService categoriaFichaService;
    FichaService fichaService;

    @Autowired
    public CategoriaFichaController(CategoriaFichaService categoriaFichaService, FichaService fichaService){
        Assert.notNull(categoriaFichaService, "O serviço de ficha não foi injetado corretamente");
        this.categoriaFichaService = categoriaFichaService;

        Assert.notNull(fichaService, "O serviço de ficha não foi injetado corretamente");
        this.fichaService = fichaService;
    }
    @Autowired
    private HttpServletRequest request;


    @ApiOperation(value = "Cadastrar categoria de ficha", notes = "Cadastra uma nova categoria de ficha no sistema", tags = "Categoria Ficha")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Categoria de ficha cadastrada com sucesso", response = ExemploRespostaCategoriaFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarCategoriaFicha(@RequestBody CategoriaFichaResponseTO categoriaFichaResponseTO, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(categoriaFichaService.criarCategoriaFicha(categoriaFichaResponseTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao criar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Consultar categorias de ficha", notes = "Lista as categorias de ficha cadastradas no sistema com paginação", tags = "Categoria Ficha")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de categorias de ficha retornada com sucesso", response = ExemploRespostaListCategoriaFichaResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_FICHAS)
    @RequestMapping(value = "/categorias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarCategoriaFichas(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome da categoria.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Categoria\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros, @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            FiltroCategoriaFichaJSON filtroCategoriaFichaJSON = new FiltroCategoriaFichaJSON(filtros);
            return ResponseEntityFactory.ok(categoriaFichaService.listarCategoriasFicha(filtroCategoriaFichaJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao carregar categoria de fichas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Editar categoria de ficha", notes = "Edita uma categoria de ficha existente no sistema", tags = "Categoria Ficha")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Categoria de ficha editada com sucesso", response = ExemploRespostaCategoriaFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarCategoriaFichas(@RequestBody CategoriaFichaResponseTO categoriaFichaResponseTO, HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(categoriaFichaService.editarCategoriaFicha(categoriaFichaResponseTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao editar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "Excluir categoria de ficha", notes = "Exclui uma categoria de ficha do sistema", tags = "Categoria Ficha")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Categoria de ficha excluída com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirCategoriaFichas(
            @ApiParam(value = "ID da categoria de ficha a ser excluída", required = true, example = "1")
            @PathVariable("id") final Integer id){
        try {
            categoriaFichaService.excluirCategoriaFicha(id, request);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(CategoriaFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
}
