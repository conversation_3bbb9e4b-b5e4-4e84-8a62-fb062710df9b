package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.avaliacao.AnamneseTreinoPorIADTO;
import br.com.pacto.bean.colaborador.FiltroColaboradorJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.ProgramaTreinoTO;
import br.com.pacto.controller.json.colaborador.ColaboradorController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.programa.PrescricaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.swagger.respostas.programa.*;
import br.com.pacto.swagger.respostas.treino.ExemploRespostaDetalheTreinoAlunoDTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.json.ResultAlunoClienteSinteticoJSON;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;
import static br.com.pacto.controller.json.base.SuperControle.STATUS_SUCESSO;

/**
 * Created by ulisses on 13/08/2018.
 */
@Controller
@RequestMapping("/psec/programas")
public class ProgramaTreinoController {

    private final ProgramaTreinoService programaTreinoService;
    private final PrescricaoService prescricaoService;

    @Autowired
    public ProgramaTreinoController(ProgramaTreinoService programaTreinoService,
                                    PrescricaoService prescricaoService
    ) {
        Assert.notNull(programaTreinoService, "O serviço de programa treino não foi injetado corretamente");
        this.programaTreinoService = programaTreinoService;
        this.prescricaoService = prescricaoService;
    }

    @Autowired
    private HttpServletRequest request;

    @ApiOperation(value = "Consultar programas pré-definidos",
            notes = "Consulta programas de treino pré-definidos com filtros e paginação",
            tags = "Programa de Treino")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
            "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
            "<strong>Filtros disponíveis:</strong>\n" +
            "- <strong>quicksearchValue:</strong> Termo de busca para nome do programa.\n" +
            "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).\n" +
            "- <strong>situacaoPrograma:</strong> Filtra pela situação do programa (Deve ser informado como uma lista ex: [\"ATIVO\", \"INATIVO\"]).\n" +
            "- <strong>generoPrograma:</strong> Filtra pelo gênero do programa (Deve ser informado como uma lista ex: [\"M\", \"F\"]).\n" +
            "- <strong>ordenacao:</strong> Campo para ordenação personalizada.",
            defaultValue = "{\"quicksearchValue\":\"Hipertrofia\", \"quicksearchFields\":[\"nome\"], \"situacaoPrograma\":[\"ATIVO\"], \"generoPrograma\":[\"M\"]}")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programas pré-definidos consultados com sucesso", response = ExemploRespostaListProgramaTreinoResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pre-definido", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPreDefinidos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                              @RequestHeader(value = "user-agent", required = false) String userAgent,
                                                                              @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroProgramaTreinoJSON filtroProgramaTreinoJSON = new FiltroProgramaTreinoJSON(filtros);
            boolean app = UteisValidacao.emptyString(userAgent) || userAgent.toUpperCase().contains("TREINO_IOS");
            return ResponseEntityFactory.ok(programaTreinoService.obterProgramasPreDefinidos(paginadorDTO, filtroProgramaTreinoJSON, app), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar programas pré-definidos simplificado",
            notes = "Consulta programas de treino pré-definidos de forma simplificada retornando apenas ID e nome",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programas pré-definidos consultados com sucesso", response = ExemploRespostaListProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pre-definido/slim", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPreDefinidosSlim(@ApiParam(value = "Nome do programa para filtro", example = "Hipertrofia") @RequestParam String nomePrograma,
                                                                                  @ApiParam(value = "Indica se é consulta de rede", example = "true") @RequestParam String rede,
                                                                                  @ApiParam(value = "Chave da rede para consulta", example = "rede123") @RequestParam String chaveRede) throws JSONException {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.obterProgramasPreDefinidosSlim(Boolean.valueOf(rede),
                    nomePrograma, chaveRede, false));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar programa de treino por ID",
            notes = "Consulta um programa de treino específico pelo seu identificador",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa de treino consultado com sucesso", response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramaTreino(@ApiParam(value = "ID do programa de treino", required = true, example = "123") @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.consultarProgramaTreino(id, null));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar programa de treino anterior",
            notes = "Consulta o programa de treino anterior de um cliente específico",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa de treino anterior consultado com sucesso", response = ExemploRespostaInteger.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "{clientecodigo}/{id}/anterior", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramaTreinoAnterior(@ApiParam(value = "Código do cliente", required = true, example = "456") @PathVariable("clientecodigo") Integer clienteCodigo,
                                                                               @ApiParam(value = "ID do programa de treino atual", required = true, example = "123") @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.consultarProgramaTreinoAnterior(clienteCodigo, id, null));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Criar programa de treino",
            notes = "Cria um novo programa de treino para um aluno",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa de treino criado com sucesso", response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarProgramaTreino(
            @ApiParam(value = "ID do programa pré-definido para usar como base", example = "123") @RequestParam(value = "preDefinidoId", required = false) Integer preDefinidoId,
            @ApiParam(value = "Chave da franqueadora", example = "franq123") @RequestParam(value = "chaveFranqueadora", required = false) String chaveFranqueadora,
            @ApiParam(value = "Indica se deve renovar o programa atual", example = "false") @RequestParam(value = "renovarAtual", required = false) Boolean renovarAtual,
            @ApiParam(value = "ID da empresa", example = "1") @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Origem do programa de treino", defaultValue = "1", example = "1") @RequestParam(value = "origem", required = false, defaultValue = "1") Integer origem,
            @ApiParam(value = "Dados do programa de treino a ser criado", required = true) @RequestBody ProgramaTreinoTO programaTreinoTO
    ) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.criarProgramaTreino(
                    empresaId,
                    programaTreinoTO, preDefinidoId,
                    chaveFranqueadora,
                    renovarAtual, request, origem));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa", e);
            if (UteisValidacao.emptyString(e.getMessage())) {
                String msgs = "";
                for (int i = 0; i < ((ValidacaoException) e).getMensagens().size(); i++) {
                    msgs += ((ValidacaoException) e).getMensagens().get(0);
                    msgs += ";";
                }
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), msgs);
            } else {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ApiOperation(value = "Alterar programa de treino",
            notes = "Altera um programa de treino existente",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa de treino alterado com sucesso", response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarProgramaTreino(@ApiParam(value = "ID do programa de treino", required = true, example = "123") @PathVariable("id") final Integer id,
                                                                     @ApiParam(value = "ID da empresa", required = true, example = "1") @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                     @ApiParam(value = "Dados do programa de treino a ser alterado", required = true) @RequestBody ProgramaTreinoTO programaTreinoTO) {
        try {
            programaTreinoTO.setId(id);
            return ResponseEntityFactory.ok(programaTreinoService.alterarProgramaTreino(empresaId, programaTreinoTO, request));

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Aprovar programa de treino",
            notes = "Aprova um programa de treino alterando seu status de revisão",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa de treino aprovado com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/aprovar/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aprovarProgramaTreino(@ApiParam(value = "ID do programa de treino", required = true, example = "123") @PathVariable("id") final Integer id,
                                                                     @ApiParam(value = "ID da empresa", required = true, example = "1") @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                     @ApiParam(value = "Dados do programa de treino para aprovação", required = true) @RequestBody ProgramaTreinoTO programaTreinoTO) {
        try {
            programaTreinoTO.setId(id);
            programaTreinoService.alterarEmRevisaoProfessor(id, programaTreinoTO.getEmRevisaoProfessor());
            return ResponseEntityFactory.ok(true);

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Verificar programa conflitante",
            notes = "Verifica se existe conflito de datas entre programas de treino de um aluno",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Verificação de conflito realizada com sucesso", response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/conflitante", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programaConflitante(@ApiParam(value = "Data de início em timestamp", required = true, example = "1640995200000") @RequestParam("inicio") final Long inicio,
                                                                   @ApiParam(value = "Data de término em timestamp", required = true, example = "1648771200000") @RequestParam("termino") Long termino,
                                                                   @ApiParam(value = "ID do aluno", required = true, example = "456") @RequestParam("alunoId") Integer alunoId,
                                                                   @ApiParam(value = "ID do programa de treino", required = true, example = "123") @RequestParam("programaId") Integer programaId) {
        try {
            ProgramaTreinoTO programaTreinoTO = new ProgramaTreinoTO();
            programaTreinoTO.setId(programaId);
            programaTreinoTO.setInicio(inicio);
            programaTreinoTO.setTermino(termino);
            programaTreinoTO.setAlunoId(alunoId);
            return ResponseEntityFactory.ok(programaTreinoService.programaConflitante(programaTreinoTO));

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Verificar programa conflitante por colaborador",
            notes = "Verifica se existe conflito de datas entre programas de treino de um colaborador",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Verificação de conflito por colaborador realizada com sucesso", response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/conflitante/colaborador", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> programaConflitanteColaborador(@ApiParam(value = "Data de início em timestamp", required = true, example = "1640995200000") @RequestParam("inicio") final Long inicio,
                                                                              @ApiParam(value = "Data de término em timestamp", required = true, example = "1648771200000") @RequestParam("termino") Long termino,
                                                                              @ApiParam(value = "ID do colaborador", required = true, example = "789") @RequestParam("colaboradorId") Integer colaboradorId,
                                                                              @ApiParam(value = "ID do programa de treino", required = true, example = "123") @RequestParam("programaId") Integer programaId) {
        try {
            ProgramaTreinoTO programaTreinoTO = new ProgramaTreinoTO();
            programaTreinoTO.setId(programaId);
            programaTreinoTO.setInicio(inicio);
            programaTreinoTO.setTermino(termino);
            programaTreinoTO.setColaboradorId(colaboradorId);
            return ResponseEntityFactory.ok(programaTreinoService.programaConflitante(programaTreinoTO));

        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar programa", e);
            return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Tornar programa pré-definido",
            notes = "Converte um programa de treino existente em programa pré-definido",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa convertido para pré-definido com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}/tornar-predefinido", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tornarProgramaPreDefinido(@ApiParam(value = "ID do programa de treino", required = true, example = "123") @PathVariable("id") final Integer id) {
        try {
            programaTreinoService.tornarProgramaPreDefinido(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar colocar o programa como pré-definido", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Excluir programa de treino",
            notes = "Exclui um programa de treino do sistema",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa de treino excluído com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirProgramaTreino(@ApiParam(value = "ID do programa de treino", required = true, example = "123") @PathVariable("id") final Integer id) {
        try {
            programaTreinoService.excluirProgramaTreino(id, request);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir programa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ApiOperation(value = "Calcular aulas previstas",
            notes = "Calcula e atualiza a quantidade de aulas previstas para um programa de treino",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Cálculo de aulas previstas realizado com sucesso", response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/{id}/calcular-aulas-previstas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarCalculosAulasPrevista(
            @ApiParam(value = "ID do programa de treino", required = true, example = "123") @PathVariable("id") Integer programaId,
            @ApiParam(value = "Campo que foi alterado", required = true, example = "totalTreinos") @RequestParam(value = "campoAlterado") String campoAlterado,
            @ApiParam(value = "Novo valor do campo", required = true, example = "36") @RequestParam(value = "value") String value,
            @ApiParam(value = "Data de início em timestamp", required = true, example = "1640995200000") @RequestParam(value = "inicio") Long inicio,
            @ApiParam(value = "Data de término em timestamp", required = true, example = "1648771200000") @RequestParam(value = "termino") Long termino,
            @ApiParam(value = "Total de treinos", required = true, example = "36") @RequestParam(value = "totalTreinos") Integer totalTreinos,
            @ApiParam(value = "Quantidade de dias por semana", required = true, example = "3") @RequestParam(value = "qtdDiasSemana") Integer qtdDiasSemana
    ) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.updateCalculosAulasPrevistas(programaId, campoAlterado, value, inicio, termino, totalTreinos, qtdDiasSemana));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Atualizar situação de programa predefinido",
            notes = "Atualiza a situação (ativo/inativo) de um programa de treino predefinido específico",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Situação do programa predefinido atualizada com sucesso", response = ExemploRespostaAtualizarSituacaoProgramaPredefinido.class)
    })
    @ResponseBody
    @RequestMapping(value = "/atualizar-situacao-predefinido/{id}/{situacao}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModelMap atualizarSituacaoProgramaPredefinido(@ApiParam(value = "ID único do programa predefinido", required = true, defaultValue = "123") @PathVariable("id") Integer id,
                                                         @ApiParam(value = "Nova situação do programa predefinido. \n\n" +
                                                                 "<strong>Valores disponíveis</strong>\n" +
                                                                 "- 0 (ATIVO - Programa ativo e disponível para uso)\n" +
                                                                 "- 1 (INATIVO - Programa inativo e indisponível para uso)\n", required = true, defaultValue = "0") @PathVariable("situacao") Integer situacao) {
        ModelMap mm = new ModelMap();
        try {
            programaTreinoService.atualizarSituacaoProgramaPredefinido(id, situacao, request);
            mm.addAttribute(STATUS_SUCESSO);
        } catch (ServiceException e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar a situação do programa", e);
        }
        return mm;
    }

    @ApiOperation(value = "Criar programa de treino predefinido",
            notes = "Cria um novo programa de treino predefinido que poderá ser utilizado como modelo para outros programas",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa predefinido criado com sucesso", response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/criar-predefinido", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarProgramaPreDefinido(@ApiParam(value = "Dados do programa de treino predefinido a ser criado", required = true) @RequestBody ProgramaTreinoTO programaTreinoTO) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.criarProgramaPreDefinido(programaTreinoTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar acompanhamento do aluno",
            notes = "Consulta informações de acompanhamento e detalhamento do treino de um aluno específico",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Acompanhamento do aluno consultado com sucesso", response = ExemploRespostaDetalheTreinoAlunoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aluno-acompanhamento/{codigoPessoa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunoAcompanhamento(@ApiParam(value = "Código da pessoa do aluno", required = true, example = "12345") @PathVariable Integer codigoPessoa,
                                                                   HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.consultarAcompanhamento(codigoPessoa, request));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar prescrições de programa de treino",
            notes = "Lista prescrições de programas de treino com filtros e paginação",
            tags = "Programa de Treino")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Prescrições listadas com sucesso", response = ExemploRespostaListPessoaPrescricaoDTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/lista-prescricao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaPrescricao(@ApiParam(value = "Filtros em formato JSON string", example = "{\"primario\":{\"tipo\":\"aluno\"}}") @RequestParam(value = "filters", required = false) String filtros,
                                                               @ApiParam(value = "ID da empresa", required = true, example = "1") @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                               @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtrosJSON = filtros == null ? new JSONObject() : new JSONObject(filtros);
            return ResponseEntityFactory.ok(prescricaoService.listaPrescricao(empresaId, filtrosJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar prescrições de programa de treino V2",
            notes = "Lista prescrições de programas de treino com filtros e paginação (versão 2)",
            tags = "Programa de Treino")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Prescrições V2 listadas com sucesso", response = ExemploRespostaListPessoaPrescricaoDTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/lista-prescricaoV2", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaPrescricaoV2(@ApiParam(value = "Filtros em formato JSON string", example = "{\"primario\":{\"tipo\":\"aluno\"}}") @RequestParam(value = "filters", required = false) String filtros,
                                                                 @ApiParam(value = "ID da empresa", required = true, example = "1") @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                 @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            JSONObject filtrosJSON = filtros == null ? new JSONObject() : new JSONObject(filtros);
            return ResponseEntityFactory.ok(prescricaoService.listaPrescricaoV2(empresaId, filtrosJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar matrículas para prescrição",
            notes = "Consulta as matrículas dos alunos disponíveis para prescrição de programa de treino",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Matrículas consultadas com sucesso", response = ExemploRespostaListInteger.class)
    })
    @ResponseBody
    @RequestMapping(value = "/matriculas-prescricao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> matriculasPrescricao(@ApiParam(value = "Filtros em formato JSON string", example = "{\"primario\":{\"tipo\":\"aluno\"}}") @RequestParam(value = "filters", required = false) String filtros,
                                                                    @ApiParam(value = "ID da empresa", required = true, example = "1") @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            JSONObject filtrosJSON = filtros == null ? new JSONObject() : new JSONObject(filtros);
            return ResponseEntityFactory.ok(prescricaoService.matriculasPrescricao(empresaId, filtrosJSON));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
        value = "Consultar grupos musculares trabalhados por período",
        notes = "Consulta os grupos musculares trabalhados por um aluno em um período específico, " +
                "retornando dados de evolução física incluindo estatísticas de treino, composição corporal " +
                "e análise detalhada dos grupos musculares exercitados. " +
                "Útil para acompanhar o desenvolvimento e distribuição do trabalho muscular do aluno.",
        tags = "Gestão de Programa de Treino"
    )
    @ApiResponses({
        @ApiResponse(code = 200, message = "Consulta realizada com sucesso",
                     response = br.com.pacto.swagger.respostas.programatreino.ExemploRespostaGruposTrabalhadosPeriodo.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/consultarGruposTrabalhadosPeriodo", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> consultarGruposTrabalhadosPeriodo(
            @ApiParam(value = "Contexto da empresa/unidade", required = true, defaultValue = "empresa123")
            @PathVariable String ctx,
            @ApiParam(value = "Data inicial do período de consulta no formato dd/MM/yyyy", required = true, defaultValue = "01/01/2024")
            @RequestParam String dataInicial,
            @ApiParam(value = "Data final do período de consulta no formato dd/MM/yyyy", required = true, defaultValue = "31/01/2024")
            @RequestParam String dataFinal,
            @ApiParam(value = "Código identificador do aluno no sistema", required = true, defaultValue = "1234")
            @RequestParam Integer codigoCliente) throws Exception {

        final ModelMap modelMap = new ModelMap();
        ResultAlunoClienteSinteticoJSON result = new ResultAlunoClienteSinteticoJSON();
        List<Map<String, Object>> grupos = new ArrayList<>();

        try {
            return ResponseEntityFactory.ok(programaTreinoService.gruposMuscularesTrabalhadosPeriodo(ctx, dataInicial, dataFinal, codigoCliente));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar grupos musculares trabalhados por período", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Filtro principal para prescrição",
            notes = "Obtém lista de filtros principais para prescrição de programas de treino",
            tags = "Programa de Treino")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
            "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
            "<strong>Filtros disponíveis:</strong>\n" +
            "- <strong>quicksearchValue:</strong> Termo de busca para nome do colaborador.\n" +
            "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\", \"userName\"]).\n" +
            "- <strong>situacoes:</strong> Filtra pela situação do colaborador (Deve ser informado como uma lista ex: [\"ATIVO\", \"INATIVO\"]).\n" +
            "- <strong>tipoColaborador:</strong> Filtra pelo tipo do colaborador (ex: \"PR\", \"TW\", \"PT\", \"OR\", \"CO\", \"PI\", \"PE\", \"TE\", \"ES\", \"FO\", \"CR\", \"MD\", \"FC\", \"AD\").",
            defaultValue = "{\"quicksearchValue\":\"João\", \"quicksearchFields\":[\"nome\"], \"situacoes\":[\"ATIVO\"], \"tipoColaborador\":\"PR\"}")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Filtros principais obtidos com sucesso", response = ExemploRespostaListItemTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "filtro-prescricao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaFiltroPrincipal(
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore PaginadorDTO paginadorDTO,
            @ApiParam(value = "ID da empresa", required = true, example = "1") @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            HttpServletRequest request
    ) throws JSONException {
        try {
            FiltroColaboradorJSON filtros = new FiltroColaboradorJSON(filters);
            return ResponseEntityFactory.ok(prescricaoService.filtroPrincipal(empresaId, filtros, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar colaboradores", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Enviar programa de treino em massa",
            notes = "Envia um programa de treino base para múltiplos clientes simultaneamente",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Envio em massa iniciado com sucesso", response = ExemploRespostaString.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ENVIAR_TREINO_EM_MASSA)
    @RequestMapping(value = "/enviar/{codigoProgramaBase}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> enviarProgramaTreino(
            @ApiParam(value = "Código do programa base para envio", required = true, example = "123") @PathVariable(value = "codigoProgramaBase") Integer codigoProgramaBase,
            @ApiParam(value = "ID da empresa", example = "1") @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Lista de IDs dos clientes que receberão o programa", required = true) @RequestBody List<Integer> clientes
    ) {
        try {
            programaTreinoService.enviarProgramaTreino(empresaId, codigoProgramaBase, clientes);
            return ResponseEntityFactory.ok("processando...");
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa", e);
            if (UteisValidacao.emptyString(e.getMessage())) {
                String msgs = "";
                for (int i = 0; i < ((ValidacaoException) e).getMensagens().size(); i++) {
                    msgs += ((ValidacaoException) e).getMensagens().get(0);
                    msgs += ";";
                }
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), msgs);
            } else {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ApiOperation(value = "Importar programas predefinidos entre empresas",
            notes = "Importa programas de treino predefinidos de uma empresa de origem para uma empresa de destino. " +
                    "O processo verifica se já existem programas com o mesmo nome na empresa destino para evitar duplicações. " +
                    "Retorna um relatório detalhado com as quantidades de programas encontrados, existentes, importados com sucesso e com erro.",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Importação de programas predefinidos processada", response = ExemploRespostaImportacaoProgramasPredefinidos.class)
    })
    @ResponseBody
    @RequestMapping(value = "/importar-predefinidos/{ctxOrigem}/{ctxDestino}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> importarProgramaPreDefinido(@ApiParam(value = "Chave da empresa de origem dos programas predefinidos", required = true, example = "empresa123") @PathVariable("ctxOrigem") String ctxOrigem,
                                                                           @ApiParam(value = "Chave da empresa de destino para importação dos programas", required = true, example = "empresa456") @PathVariable("ctxDestino") String ctxDestino) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.importarProgramaPreDefinido(ctxOrigem, ctxDestino, request));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar importar programa predefinido", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Criar programa de treino por Inteligência Artificial",
            notes = "Cria um programa de treino personalizado utilizando Inteligência Artificial baseado na anamnese do cliente",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Programa de treino criado por IA com sucesso", response = ExemploRespostaProgramaDeTreinoGeradoPorIADTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/criaProgramaTreinoPorIA", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criaProgramaTreinoPorIA(@ApiParam(value = "Dados da anamnese para geração do programa por IA", required = true) @RequestBody AnamneseTreinoPorIADTO anamneseTreinoPorIADTO) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.preparaProgramaTreinoPorIA(anamneseTreinoPorIADTO, null));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar programa por IA - ", e);
            return ResponseEntityFactory.erroInterno("erro.interno", e.getMessage());
        }
    }

    @ApiOperation(value = "Atualizar banco de atividades para IA",
            notes = "Executa processo de atualização do banco de atividades utilizado pela Inteligência Artificial",
            tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Banco de atividades IA atualizado com sucesso", response = ExemploRespostaString.class)
    })
    @ResponseBody
    @RequestMapping(value = "/processos/atualizarBancoAtividadesIA", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarBancoAtividadesIA() {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.atualizarBancoAtividadesIA(null));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo ", e);
            return ResponseEntityFactory.erroInterno("erro.interno", e.getMessage());
        }
    }

}
