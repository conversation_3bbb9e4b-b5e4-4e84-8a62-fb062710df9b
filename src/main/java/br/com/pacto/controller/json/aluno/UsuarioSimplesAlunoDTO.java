package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 06/08/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações básicas de autenticação do aluno.")
public class UsuarioSimplesAlunoDTO {

    @ApiModelProperty(value = "Identificador único do aluno", example = "105")
    private Integer alunoId;

    @ApiModelProperty(value = "Nome de usuário utilizado para login", example = "joana.pereira")
    private String username;

    @ApiModelProperty(value = "Senha do aluno para autenticação", example = "senha123")
    private String password;

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
