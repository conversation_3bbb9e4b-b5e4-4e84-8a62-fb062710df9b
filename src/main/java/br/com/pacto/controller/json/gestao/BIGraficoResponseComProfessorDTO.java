package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
@ApiModel(description = "Dados de indicadores de gráfico BI para um período específico com separação por professor")
public class BIGraficoResponseComProfessorDTO {
    @ApiModelProperty(value = "Período de referência dos dados (formato: MM/yyyy)", example = "01/2024")
    private String periodo;

    @ApiModelProperty(value = "Mapa de valores dos indicadores por professor, onde a chave é o ID do professor e o valor são os indicadores")
    private Map<String , BIGraficoResponseIndicadoresDTO> valorPorProfessor;

     public BIGraficoResponseComProfessorDTO( ){

     }

    public String getPeriodo() {
        return periodo;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }

    public Map<String, BIGraficoResponseIndicadoresDTO> getValorPorProfessor() {
        return valorPorProfessor;
    }

    public void setValorPorProfessor(Map<String, BIGraficoResponseIndicadoresDTO> valorPorProfessor) {
        this.valorPorProfessor = valorPorProfessor;
    }
}
