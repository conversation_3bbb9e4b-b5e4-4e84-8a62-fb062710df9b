package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Dados consolidados de Business Intelligence para Crossfit")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BICrossfitDTO {

    @ApiModelProperty(value = "Número total de alunos cadastrados", example = "150")
    private Integer numeroDealunos;

    @ApiModelProperty(value = "Quantidade de resultados de treinos lançados no período", example = "85")
    private Integer resultadosLancados;

    @ApiModelProperty(value = "Número total de agendamentos realizados no período", example = "320")
    private Integer numeroAgendamentos;

    @ApiModelProperty(value = "Quantidade de faltas registradas nos últimos 7 dias", example = "12")
    private Integer faltasNosUltimos7Dias;

    @ApiModelProperty(value = "Dados de ocupação por dia da semana e período")
    private OcupacaoDTO ocupacao;

    @ApiModelProperty(value = "Lista de frequência por professor")
    private List<FrequenciaProfessorDTO> frequenciaPorProfessor;

    public Integer getNumeroDealunos() {
        return numeroDealunos;
    }

    public void setNumeroDealunos(Integer numeroDealunos) {
        this.numeroDealunos = numeroDealunos;
    }

    public Integer getResultadosLancados() {
        return resultadosLancados;
    }

    public void setResultadosLancados(Integer resultadosLancados) {
        this.resultadosLancados = resultadosLancados;
    }

    public Integer getNumeroAgendamentos() {
        return numeroAgendamentos;
    }

    public void setNumeroAgendamentos(Integer numeroAgendamentos) {
        this.numeroAgendamentos = numeroAgendamentos;
    }

    public Integer getFaltasNosUltimos7Dias() {
        return faltasNosUltimos7Dias;
    }

    public void setFaltasNosUltimos7Dias(Integer faltasNosUltimos7Dias) {
        this.faltasNosUltimos7Dias = faltasNosUltimos7Dias;
    }

    public OcupacaoDTO getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(OcupacaoDTO ocupacao) {
        this.ocupacao = ocupacao;
    }

    public List<FrequenciaProfessorDTO> getFrequenciaPorProfessor() {
        return frequenciaPorProfessor;
    }

    public void setFrequenciaPorProfessor(List<FrequenciaProfessorDTO> frequenciaPorProfessor) {
        this.frequenciaPorProfessor = frequenciaPorProfessor;
    }
}
