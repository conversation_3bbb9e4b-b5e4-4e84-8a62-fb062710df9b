package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Avaliação do professor feita pelo cliente com informações adicionais")
public class AvaliacaoProfessorVO {

    @ApiModelProperty(value = "Código único da avaliação", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Data de registro da avaliação", example = "2024-06-15")
    private Date dataRegistro;

    @ApiModelProperty(value = "Data da última atualização da avaliação", example = "2024-06-15")
    private Date dataUpdate;

    @ApiModelProperty(value = "Código do usuário que fez a avaliação", example = "456")
    private Integer codUsuario;

    @ApiModelProperty(value = "Nome de usuário do cliente que avaliou", example = "joao.silva")
    private String clienteUsername;

    @ApiModelProperty(value = "Código do professor avaliado", example = "789")
    private Integer professorCodigo;

    @ApiModelProperty(value = "Nome do professor avaliado", example = "Maria Santos")
    private String professorNome;

    @ApiModelProperty(value = "Nota atribuída ao professor (1 a 5)", example = "5")
    private Integer notaAvaliada;

    @ApiModelProperty(value = "Código da empresa", example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "URL da foto do professor", example = "https://exemplo.com/fotos/professor123.jpg")
    private String fotoProfessor;

    @ApiModelProperty(value = "Comentário adicional sobre a avaliação", example = "Excelente professor, muito atencioso e didático")
    private String comentario;

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataUpdate() {
        return dataUpdate;
    }

    public void setDataUpdate(Date dataUpdate) {
        this.dataUpdate = dataUpdate;
    }

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public String getClienteUsername() {
        return clienteUsername;
    }

    public void setClienteUsername(String clienteUsername) {
        this.clienteUsername = clienteUsername;
    }

    public Integer getProfessorCodigo() {
        return professorCodigo;
    }

    public void setProfessorCodigo(Integer professorCodigo) {
        this.professorCodigo = professorCodigo;
    }

    public String getProfessorNome() {
        return professorNome;
    }

    public void setProfessorNome(String professorNome) {
        this.professorNome = professorNome;
    }

    public Integer getNotaAvaliada() {
        return notaAvaliada;
    }

    public void setNotaAvaliada(Integer notaAvaliada) {
        this.notaAvaliada = notaAvaliada;
    }

    public String getFotoProfessor() {
        return fotoProfessor;
    }

    public void setFotoProfessor(String fotoProfessor) {
        this.fotoProfessor = fotoProfessor;
    }

    public String getComentario() {
        return comentario;
    }

    public void setComentario(String comentario) {
        this.comentario = comentario;
    }
}
