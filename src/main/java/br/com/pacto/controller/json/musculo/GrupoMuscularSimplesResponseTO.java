package br.com.pacto.controller.json.musculo;

import br.com.pacto.bean.musculo.GrupoMuscular;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados simplificados de um grupo muscular")
public class GrupoMuscularSimplesResponseTO {

    @ApiModelProperty(value = "Identificador único do grupo muscular", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do grupo muscular", example = "Membros superiores")
    private String nome;

    public GrupoMuscularSimplesResponseTO(GrupoMuscular grupoMuscular) {
        this.id = grupoMuscular.getCodigo();
        this.nome = grupoMuscular.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
