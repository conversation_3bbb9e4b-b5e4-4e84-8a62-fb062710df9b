package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.avaliacao.AvaliacaoFisica;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created paulo 07/11/2018
 */

@ApiModel(description = "Composição corporal detalhada do aluno.")
public class ComposicaoCorporalDTO {

    @ApiModelProperty(value = "Massa magra do aluno em kg", example = "45.8")
    private Double massaMagra;

    @ApiModelProperty(value = "Massa gorda do aluno em kg", example = "18.3")
    private Double massaGorda;

    @ApiModelProperty(value = "Peso ósseo do aluno em kg", example = "10.1")
    private Double pesoOsseo;

    @ApiModelProperty(value = "Resíduos presentes no corpo em kg", example = "5.2")
    private Double residuos;


    public ComposicaoCorporalDTO(AvaliacaoFisica avaliacaoFisica) {
        this.massaMagra = avaliacaoFisica.getMassaMagra();
        this.massaGorda = avaliacaoFisica.getMassaGorda();
        this.pesoOsseo = avaliacaoFisica.getPesoOsseo();
        this.residuos = avaliacaoFisica.getResidual();
    }

    public Double getMassaMagra() {
        return massaMagra;
    }

    public void setMassaMagra(Double massaMagra) {
        this.massaMagra = massaMagra;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }

    public Double getPesoOsseo() {
        return pesoOsseo;
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }

    public Double getResiduos() {
        return residuos;
    }

    public void setResiduos(Double residuos) {
        this.residuos = residuos;
    }
}
