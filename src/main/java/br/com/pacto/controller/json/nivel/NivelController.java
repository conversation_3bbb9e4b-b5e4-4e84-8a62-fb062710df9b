package br.com.pacto.controller.json.nivel;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.swagger.respostas.nivel.ExemploConsultarNiveisResponseDTO;
import br.com.pacto.swagger.respostas.nivel.ExemploIncluirNivelResponseDTO;
import br.com.pacto.swagger.respostas.nivel.ExemploAlterarNivelResponseDTO;
import br.com.pacto.swagger.respostas.nivel.ExemploExcluirNivelResponseDTO;
import br.com.pacto.swagger.respostas.nivel.ExemploListarTodosNiveisResponseDTO;
import br.com.pacto.swagger.respostas.nivel.ExemploNivelAlunoResponseDTO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 24/08/2018.
 */
@Controller
@RequestMapping("/psec/niveis")
public class NivelController extends SuperController {

    private final NivelService nivelService;
    private final ClienteSinteticoService clienteSinteticoService;

    @Autowired
    public NivelController(NivelService nivelService, ClienteSinteticoService clienteSinteticoService){
        Assert.notNull(nivelService, "O serviço de Nível não foi injetado corretamente");
        this.nivelService = nivelService;
        this.clienteSinteticoService = clienteSinteticoService;
    }

    @ApiOperation(value = "Consultar níveis com paginação",
                  notes = "<strong>Permissão necessária:</strong> NIVEIS<br/>" +
                         "<strong>Autenticação:</strong> Requer autenticação via empresaId<br/>" +
                         "<strong>Descrição:</strong> Consulta níveis de alunos cadastrados no sistema com suporte a filtros e paginação<br/>" +
                         "<strong>Retorno:</strong> Lista paginada de níveis encapsulada em EnvelopeRespostaDTO",
                  tags = "Gestão de Níveis")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "0", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade de elementos por página", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "<strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li>nome - Ordena pelo nome do nível</li>" +
                    "<li>ordem - Ordena pela ordem do nível</li>" +
                    "<li>ativo - Ordena pela situação do nível</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploConsultarNiveisResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarNiveis(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do nível.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).\n" +
                    "- <strong>situacoes:</strong> Filtra pela situação do nível (Deve ser informado como uma lista ex: [\"ATIVO\", \"INATIVO\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Iniciante\", \"quicksearchFields\":[\"nome\"], \"situacoes\":[\"ATIVO\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroNivelJSON filtroNivelJSON = new FiltroNivelJSON(filtros);
            return ResponseEntityFactory.ok(nivelService.consultarNivel(filtroNivelJSON, paginadorDTO),paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os níveis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Incluir novo nível",
                  notes = "<strong>Permissão necessária:</strong> NIVEIS<br/>" +
                         "<strong>Autenticação:</strong> Requer autenticação via empresaId<br/>" +
                         "<strong>Descrição:</strong> Inclui um novo nível de aluno no sistema<br/>" +
                         "<strong>Retorno:</strong> Dados do nível incluído encapsulados em EnvelopeRespostaDTO",
                  tags = "Gestão de Níveis")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível incluído com sucesso", response = ExemploIncluirNivelResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirNivel(
            @ApiParam(value = "Dados do nível a ser incluído", required = true)
            @RequestBody NivelTO nivelTO) {
        try {
            return ResponseEntityFactory.ok(nivelService.inserir(nivelTO));
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Alterar nível existente",
                  notes = "<strong>Permissão necessária:</strong> NIVEIS<br/>" +
                         "<strong>Autenticação:</strong> Requer autenticação via empresaId<br/>" +
                         "<strong>Descrição:</strong> Altera os dados de um nível existente no sistema<br/>" +
                         "<strong>Retorno:</strong> Dados do nível alterado encapsulados em EnvelopeRespostaDTO",
                  tags = "Gestão de Níveis")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível alterado com sucesso", response = ExemploAlterarNivelResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarNivel(
            @ApiParam(value = "Código identificador do nível a ser alterado", required = true, example = "1")
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados atualizados do nível", required = true)
            @RequestBody NivelTO nivelTO) {
        try {
            return ResponseEntityFactory.ok(nivelService.alterar(id, nivelTO));

        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Excluir nível",
                  notes = "<strong>Permissão necessária:</strong> NIVEIS<br/>" +
                         "<strong>Autenticação:</strong> Requer autenticação via empresaId<br/>" +
                         "<strong>Descrição:</strong> Exclui um nível do sistema<br/>" +
                         "<strong>Retorno:</strong> Confirmação da exclusão encapsulada em EnvelopeRespostaDTO",
                  tags = "Gestão de Níveis")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível excluído com sucesso", response = ExemploExcluirNivelResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirNivel(
            @ApiParam(value = "Código identificador do nível a ser excluído", required = true, example = "1")
            @PathVariable("id") final Integer id){
        try {
            nivelService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Listar todos os níveis",
                  notes = "<strong>Permissão necessária:</strong> NIVEIS<br/>" +
                         "<strong>Autenticação:</strong> Requer autenticação via empresaId<br/>" +
                         "<strong>Descrição:</strong> Lista todos os níveis cadastrados no sistema sem paginação<br/>" +
                         "<strong>Retorno:</strong> Lista completa de níveis encapsulada em EnvelopeRespostaDTO",
                  tags = "Gestão de Níveis")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de níveis obtida com sucesso", response = ExemploListarTodosNiveisResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.NIVEIS)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodosNiveis() {
        try {
            return ResponseEntityFactory.ok(nivelService.listarTodosNiveis());
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todos os niveis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(value = "Obter nível do aluno",
                  notes = "<strong>Autenticação:</strong> Requer autenticação via empresaId<br/>" +
                         "<strong>Descrição:</strong> Obtém o código do nível associado a um aluno específico<br/>" +
                         "<strong>Retorno:</strong> Código do nível do aluno encapsulado em EnvelopeRespostaDTO",
                  tags = "Gestão de Níveis")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível do aluno obtido com sucesso", response = ExemploNivelAlunoResponseDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aluno/{codigoCliente}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> nivelAluno(
            @ApiParam(value = "Código identificador do aluno", required = true, example = "123")
            @PathVariable("codigoCliente") final Integer codigoCliente) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.nivelAluno(codigoCliente));
        } catch (ServiceException e) {
            Logger.getLogger(NivelController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar nivel", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

}
