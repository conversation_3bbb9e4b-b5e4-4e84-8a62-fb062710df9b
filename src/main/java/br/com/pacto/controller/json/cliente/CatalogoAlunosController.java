package br.com.pacto.controller.json.cliente;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.enumerador.cliente.TiposConsultaCatalogoAlunoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 16/08/2018.
 */
@Controller
@RequestMapping("/psec/treino-alunos")
public class CatalogoAlunosController {

    private final ClienteSinteticoService clienteSinteticoService;

    @Autowired
    public CatalogoAlunosController(ClienteSinteticoService clienteSinteticoService){
        Assert.notNull(clienteSinteticoService, "O serviço de cliente sintético não foi injetado corretamente");
        this.clienteSinteticoService = clienteSinteticoService;
    }

    @ApiOperation(
            value = "Consultar catálogo de alunos da academia",
            notes = "Consulta o catálogo de alunos da academia com opções avançadas de filtro por nome e tipo de consulta específico. " +
                    "Permite diferentes tipos de consulta para atender diversas necessidades operacionais como visualizar todos os alunos, " +
                    "apenas a carteira do professor logado, alunos presentes na academia, alunos com treinos vencidos, etc. " +
                    "Essencial para o acompanhamento e gestão dos alunos pelos professores e coordenadores.",
            tags = "Clientes"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Catálogo de alunos consultado com sucesso)", response = br.com.pacto.swagger.respostas.cliente.ExemploRespostaListAlunoCatalogoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ALUNOS)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterCatalogoAlunos(
            @ApiParam(value = "Código da empresa para consulta do catálogo de alunos", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Filtro por nome do aluno para busca textual. Permite busca parcial por nome completo.", defaultValue = "João Silva")
            @RequestParam(value = "filtroNome", required = false) String filtroNome,
            @ApiParam(value = "Tipo de consulta do catálogo que define quais alunos serão retornados.<br/><br/>" +
                    "<strong>Tipos disponíveis:</strong><br/>" +
                    "- <strong>TODOS:</strong> Todos os alunos da empresa<br/>" +
                    "- <strong>CARTEIRA:</strong> Apenas alunos da carteira do professor logado<br/>" +
                    "- <strong>ACOMPANHANDO:</strong> Alunos que estão sendo acompanhados atualmente<br/>" +
                    "- <strong>AGENDADO:</strong> Alunos com agendamentos ativos<br/>" +
                    "- <strong>DESACOMPANHADO:</strong> Alunos que não estão sendo acompanhados<br/>" +
                    "- <strong>ACADEMIA:</strong> Alunos presentes na academia<br/>" +
                    "- <strong>ACADEMIA_TREINO_VENCER:</strong> Alunos na academia com treino próximo ao vencimento<br/>" +
                    "- <strong>ACADEMIA_TREINO_VENCIDO:</strong> Alunos na academia com treino vencido<br/>" +
                    "- <strong>ACADEMIA_CARTEIRA:</strong> Alunos da carteira do professor que estão na academia<br/>" +
                    "- <strong>SEM_TREINO:</strong> Alunos que não possuem programa de treino ativo<br/>" +
                    "- <strong>TREINO_VENCIDO:</strong> Alunos com programa de treino vencido",
                    defaultValue = "TODOS",
                    allowableValues = "TODOS,CARTEIRA,ACOMPANHANDO,AGENDADO,DESACOMPANHADO,ACADEMIA,ACADEMIA_TREINO_VENCER,ACADEMIA_TREINO_VENCIDO,ACADEMIA_CARTEIRA,SEM_TREINO,TREINO_VENCIDO")
            @RequestParam(value = "tipo", required = false) TiposConsultaCatalogoAlunoEnum tipo,
            HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterCatalogoAlunos(request, empresaId, filtroNome, tipo));
        } catch (ServiceException e) {
            Logger.getLogger(CatalogoAlunosController.class.getName()).log(Level.SEVERE, "Erro ao tentar catalogar os alunos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


}
