package br.com.pacto.controller.json.ranking;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.swagger.respostas.ranking.ExemploRespostaListRankingJSON;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Joao Moita on 28/09/2018.
 */
@Controller
@RequestMapping("/psec/ranking")
public class RankingController {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private WodService ws;

    @ApiOperation(
            value = "Consultar ranking de alunos por WOD (Workout of the Day)",
            notes = "Consulta o ranking de alunos para um WOD específico, retornando a lista ordenada com as posições, " +
                    "dados dos alunos e suas performances no treino. Inclui informações como tempo, peso, repetições, " +
                    "rounds e nível do crossfit de cada participante.",
            tags = "WOD"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListRankingJSON.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.RANKING_WOD)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> rankingAlunos(
            @ApiParam(value = "Código identificador do WOD para consulta do ranking", defaultValue = "123", required = true)
            @RequestParam("wodId") final Integer wodId,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<RankingJSON> ranking = ws.ranking(ctx, wodId, request);

            return ResponseEntityFactory.ok(ranking);
        } catch (Exception e) {
            Logger.getLogger(RankingController.class.getName()).log(Level.SEVERE, "Erro ao tentar ranking alunos", e);
            return ResponseEntityFactory.erroInterno("erro_ranking_aluno", e.getMessage());
        }
    }
}
