package br.com.pacto.controller.json.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo on 11/11/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Resposta da ativação da empresa com token de acesso")
public class ResponseActiveEmpresaDTO {

    @ApiModelProperty(value = "Token de autenticação gerado para o colaborador coordenador", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @ApiModelProperty(value = "ID da empresa ativada", example = "123")
    private Integer empresaId;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getEmpresaId() {
        return empresaId;
    }

    public void setEmpresaId(Integer empresaId) {
        this.empresaId = empresaId;
    }

}
