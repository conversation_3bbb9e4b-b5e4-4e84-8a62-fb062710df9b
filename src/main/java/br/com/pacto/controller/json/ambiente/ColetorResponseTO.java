package br.com.pacto.controller.json.ambiente;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do coletor")
public class ColetorResponseTO {
    @ApiModelProperty(value = "Código único identificador do coletor", example = "13")
    private Integer id;
    @ApiModelProperty(value = "Nome do coletor de acesso", example = "Catraca 01")
    private String nome;

    public ColetorResponseTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
