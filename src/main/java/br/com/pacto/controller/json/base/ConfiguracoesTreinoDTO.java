package br.com.pacto.controller.json.base;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de treino do sistema")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesTreinoDTO {

    @ApiModelProperty(value = "Define se deve emitir ficha após vencimento do treino", example = "true")
    private String emitir_ficha_apos_vencimento_treino;

    @ApiModelProperty(value = "Número de impressões permitidas para a ficha", example = "3")
    private String numero_impressao_ficha;

    @ApiModelProperty(value = "Define se deve bloquear impressão da ficha após todas as execuções", example = "false")
    private String bloquear_impressao_ficha_apos_todas_execucoes;

    @ApiModelProperty(value = "Número de dias antes do vencimento para notificar", example = "7")
    private String dias_antes_vencimento;

    @ApiModelProperty(value = "Define se deve agrupar séries em set", example = "true")
    private String agrupamento_series_set;

    @ApiModelProperty(value = "Define se deve permitir apenas alunos ativos", example = "true")
    private String permitir_apenas_alunos_ativos;

    @ApiModelProperty(value = "Define se deve visualizar mensagem de aviso", example = "true")
    private String visualizar_mensagem_aviso;

    @ApiModelProperty(value = "Define se deve permitir visualizar WOD (Workout of the Day)", example = "false")
    private String permitir_visualizar_wod;

    @ApiModelProperty(value = "Define se deve permitir visualizar CREF do professor", example = "true")
    private String permitir_visualizar_cref;

    @ApiModelProperty(value = "Define se deve permitir visualizar PAR-Q com 10 perguntas", example = "true")
    private String permitir_visualizar_par_q_10_perguntas;

    @ApiModelProperty(value = "Define se deve permitir visualizar lei do PAR-Q", example = "true")
    private String permitir_visualizar_lei_parq;

    @ApiModelProperty(value = "Define se deve permitir visualizar aviso de pendências", example = "true")
    private String permitir_visualizar_aviso_de_pendencias;

    @ApiModelProperty(value = "Define se deve forçar criação de novo programa", example = "false")
    private String forcar_criar_novo_programa;

    public Boolean getAgrupamento_series_set() {
        if (!UteisValidacao.emptyString(agrupamento_series_set)) {
            return agrupamento_series_set.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setAgrupamento_series_set(String agrupamento_series_set) {
        this.agrupamento_series_set = agrupamento_series_set;
    }

    public Boolean getEmitir_ficha_apos_vencimento_treino() {
        if (!UteisValidacao.emptyString(emitir_ficha_apos_vencimento_treino)) {
            return emitir_ficha_apos_vencimento_treino.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setEmitir_ficha_apos_vencimento_treino(String emitir_ficha_apos_vencimento_treino) {
        this.emitir_ficha_apos_vencimento_treino = emitir_ficha_apos_vencimento_treino;
    }

    public String getNumero_impressao_ficha() {
        return numero_impressao_ficha;
    }

    public void setNumero_impressao_ficha(String numero_impressao_ficha) {
        this.numero_impressao_ficha = numero_impressao_ficha;
    }

    public Boolean getBloquear_impressao_ficha_apos_todas_execucoes() {
        if (!UteisValidacao.emptyString(bloquear_impressao_ficha_apos_todas_execucoes)) {
            return bloquear_impressao_ficha_apos_todas_execucoes.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setBloquear_impressao_ficha_apos_todas_execucoes(String bloquear_impressao_ficha_apos_todas_execucoes) {
        this.bloquear_impressao_ficha_apos_todas_execucoes = bloquear_impressao_ficha_apos_todas_execucoes;
    }

    public String getDias_antes_vencimento() {
        return dias_antes_vencimento;
    }

    public void setDias_antes_vencimento(String dias_antes_vencimento) {
        this.dias_antes_vencimento = dias_antes_vencimento;
    }

    public Boolean getPermitir_apenas_alunos_ativos() {
        if (!UteisValidacao.emptyString(permitir_apenas_alunos_ativos)) {
            return permitir_apenas_alunos_ativos.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_apenas_alunos_ativos(String permitir_apenas_alunos_ativos) {
        this.permitir_apenas_alunos_ativos = permitir_apenas_alunos_ativos;
    }

    public Boolean getVisualizar_mensagem_aviso() {
        if (!UteisValidacao.emptyString(visualizar_mensagem_aviso)) {
            return visualizar_mensagem_aviso.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setVisualizar_mensagem_aviso(String visualizar_mensagem_aviso) {
        this.visualizar_mensagem_aviso = visualizar_mensagem_aviso;
    }

    public Boolean getPermitir_visualizar_wod() {
        if (!UteisValidacao.emptyString(permitir_visualizar_wod)) {
            return permitir_visualizar_wod.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_wod(String permitir_visualizar_wod) {
        this.permitir_visualizar_wod = permitir_visualizar_wod;
    }

    public Boolean getPermitir_visualizar_cref() {
        if (!UteisValidacao.emptyString(permitir_visualizar_cref)) {
            return permitir_visualizar_cref.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_cref(String permitir_visualizar_cref) {
        this.permitir_visualizar_cref = permitir_visualizar_cref;
    }

    public Boolean getPermitir_visualizar_par_q_10_perguntas() {
        if (!UteisValidacao.emptyString(permitir_visualizar_par_q_10_perguntas)) {
            return permitir_visualizar_par_q_10_perguntas.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_par_q_10_perguntas(String permitir_visualizar_par_q_10_perguntas) {
        this.permitir_visualizar_par_q_10_perguntas = permitir_visualizar_par_q_10_perguntas;
    }

    public Boolean getPermitir_visualizar_aviso_de_pendencias() {
        if (!UteisValidacao.emptyString(permitir_visualizar_aviso_de_pendencias)) {
            return permitir_visualizar_aviso_de_pendencias.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_aviso_de_pendencias(String permitir_visualizar_aviso_de_pendencias) {
        this.permitir_visualizar_aviso_de_pendencias = permitir_visualizar_aviso_de_pendencias;
    }

    public Boolean getForcar_criar_novo_programa() {
        if (!UteisValidacao.emptyString(forcar_criar_novo_programa)) {
            return forcar_criar_novo_programa.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setForcar_criar_novo_programa(String forcar_criar_novo_programa) {
        this.forcar_criar_novo_programa = forcar_criar_novo_programa;
    }

    public Boolean getPermitir_visualizar_lei_parq() {
        if (!UteisValidacao.emptyString(permitir_visualizar_lei_parq)) {
            return permitir_visualizar_lei_parq.trim().equals("true");
        } else {
            return false;
        }
    }

    public void setPermitir_visualizar_lei_parq(String permitir_visualizar_lei_parq) {
        this.permitir_visualizar_lei_parq = permitir_visualizar_lei_parq;
    }
}
