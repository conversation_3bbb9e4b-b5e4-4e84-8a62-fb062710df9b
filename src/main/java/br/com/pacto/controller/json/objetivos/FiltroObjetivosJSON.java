package br.com.pacto.controller.json.objetivos;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR> 28/01/2019
 */
@ApiModel(description = "Filtros para consulta de objetivos predefinidos, permitindo refinar os resultados por nome")
public class FiltroObjetivosJSON extends SuperJSON {

    @ApiModelProperty(value = "Indica se a busca deve ser aplicada no campo nome do objetivo predefinido", example = "true")
    private Boolean nome = false;

    @ApiModelProperty(value = "Termo de busca rápida para filtrar objetivos predefinidos por nome", example = "Perda de Peso")
    private String parametro;

    public FiltroObjetivosJSON(JSONObject filters) throws JSONException {
        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue");
            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }

                }

            }
        }
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }
}
