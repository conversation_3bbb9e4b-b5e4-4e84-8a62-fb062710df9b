package br.com.pacto.controller.json.agendamento;

import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.aluno.SituacaoAlunoEnum;
import br.com.pacto.controller.json.aluno.SituacaoContratoZWEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados do aluno matriculado em uma turma incluindo situação, presença e informações de agendamento")
public class AlunoTurmaDTO {

    @ApiModelProperty(value = "Código identificador único do aluno", example = "1001")
    private Integer id;

    @ApiModelProperty(value = "Número da matrícula do aluno no sistema ZW", example = "2024001")
    private Integer matriculaZW;

    @ApiModelProperty(value = "Nome completo do aluno", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "Nome da unidade onde o aluno está matriculado", example = "Unidade Centro")
    private String unidade;

    @ApiModelProperty(value = "URL da foto do aluno", example = "https://exemplo.com/fotos/joao.jpg")
    private String imageUri;
    private SituacaoContratoZWEnum situacaoContrato;

    @ApiModelProperty(value = "Situação do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)\n", example = "AT")
    private SituacaoAlunoEnum situacaoAluno;

    @ApiModelProperty(value = "Tipo de vínculo do aluno com a aula. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- MATRICULADO (Aluno regularmente matriculado)\n" +
            "- REPOSICAO (Aula de reposição)\n" +
            "- DESMARCADO (Aula desmarcada pelo aluno)\n" +
            "- AULA_EXPERIMENTAL (Aula experimental/teste)\n" +
            "- DIARIA (Diária avulsa)\n" +
            "- DIARIA_GYMPASS (Diária via GymPass)\n" +
            "- DIARIA_TOTALPASS (Diária via TotalPass)\n" +
            "- MARCACAO (Marcação de aula)\n" +
            "- VISITANTE (Aluno visitante)\n" +
            "- DEPENDENTE (Dependente de aluno)\n" +
            "- DESAFIO (Participação em desafio)\n" +
            "- INTEGRACAO (Via integração externa)\n" +
            "- PARTICIPANTE_FIXO (Participante fixo da turma)\n" +
            "- ESPERA (Lista de espera)", example = "MATRICULADO")
    private AlunoVinculoAulaEnum vinculoComAula;

    @ApiModelProperty(value = "Indica se a presença do aluno foi confirmada", example = "true")
    private Boolean confirmado;

    @ApiModelProperty(value = "Indica se o aluno está fixo na turma", example = "false")
    private Boolean fixo = false;

    @ApiModelProperty(value = "Justificativa para ausência ou situação especial", example = "Atestado médico")
    private String justificativa;

    @ApiModelProperty(value = "Timestamp do horário de marcação da aula", example = "1705737600000")
    private Long horarioMarcacao;

    @ApiModelProperty(value = "Código do aluno passivo (para transferências)", example = "1002")
    private Integer codigoPassivo;

    @ApiModelProperty(value = "Código do aluno indicado", example = "1003")
    private Integer codigoIndicado;

    @ApiModelProperty(value = "Equipamento reservado para o aluno", example = "Esteira 5")
    private String equipamentoReservado;

    @ApiModelProperty(value = "ID do usuário no sistema Selfloops", example = "user123")
    private String userIdSelfloops;

    @ApiModelProperty(value = "Potência média durante o treino", example = "150")
    private Integer averagePower;

    @ApiModelProperty(value = "Calorias queimadas durante o treino", example = "320")
    private Integer calories;

    @ApiModelProperty(value = "Informações sobre créditos do aluno", example = "5 créditos restantes")
    private String credito;

    @ApiModelProperty(value = "Tooltip com informações detalhadas sobre créditos", example = "Válido até 31/12/2024")
    private String toolTipCredito;

    @ApiModelProperty(value = "Código da pessoa no sistema", example = "5001")
    private Integer codigoPessoa;

    @ApiModelProperty(value = "Data de nascimento do aluno", example = "15/03/1990")
    private String dataNascimento = null;

    @ApiModelProperty(value = "Código do cliente no sistema", example = "1001")
    private Integer codigoCliente;

    @ApiModelProperty(value = "Tempo de duração da aula em minutos", example = "60")
    private Integer tempoDeAula;

    @ApiModelProperty(value = "Posição do aluno no ranking da turma", example = "3")
    private Integer posicaoRankingAluno;

    @ApiModelProperty(value = "Sistema de origem do agendamento", example = "AULA_CHEIA")
    private String origemSistema;

    @ApiModelProperty(value = "Indica se o aluno está autorizado para gestão de rede", example = "false")
    private Boolean autorizadoGestaoRede = false;

    @ApiModelProperty(value = "Código de acesso autorizado", example = "")
    private String codAcessoAutorizado = "";

    @ApiModelProperty(value = "Matrícula do responsável pela autorização", example = "0")
    private Integer matriculaAutorizado = 0;

    public AlunoTurmaDTO() {
    }

    public AlunoTurmaDTO(AlunoResponseTO alunoResponseTO) {
        this.id = alunoResponseTO.getId();
        this.matriculaZW = alunoResponseTO.getMatriculaZW();
        this.nome = alunoResponseTO.getNome();
        this.imageUri = alunoResponseTO.getImageUri();
        this.situacaoContrato = alunoResponseTO.getSituacaoContratoZW();
        this.situacaoAluno = alunoResponseTO.getSituacaoAluno();
        this.confirmado = alunoResponseTO.getConfirmado() != null ? alunoResponseTO.getConfirmado() : false;
        this.justificativa = alunoResponseTO.getJustificativa();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public SituacaoContratoZWEnum getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(SituacaoContratoZWEnum situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public AlunoVinculoAulaEnum getVinculoComAula() {
        return vinculoComAula;
    }

    public void setVinculoComAula(AlunoVinculoAulaEnum vinculoComAula) {
        this.vinculoComAula = vinculoComAula;
    }

    public Boolean getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) { this.justificativa = justificativa; }

    public Long getHorarioMarcacao() {
        return horarioMarcacao;
    }

    public void setHorarioMarcacao(Long horarioMarcacao) {
        this.horarioMarcacao = horarioMarcacao;
    }

    public Boolean getFixo() {
        return fixo;
    }

    public void setFixo(Boolean fixo) {
        this.fixo = fixo;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public Integer getCodigoIndicado() {
        return codigoIndicado;
    }

    public void setCodigoIndicado(Integer codigoIndicado) {
        this.codigoIndicado = codigoIndicado;
    }

    public String getEquipamentoReservado() {
        return equipamentoReservado;
    }

    public void setEquipamentoReservado(String equipamentoReservado) {
        this.equipamentoReservado = equipamentoReservado;
    }

    public String getUserIdSelfloops() {
        return userIdSelfloops;
    }

    public void setUserIdSelfloops(String userIdSelfloops) {
        this.userIdSelfloops = userIdSelfloops;
    }

    public Boolean getAutorizadoGestaoRede() {
        if (autorizadoGestaoRede == null) {
            autorizadoGestaoRede = false;
        }
        return autorizadoGestaoRede;
    }

    public void setAutorizadoGestaoRede(Boolean autorizadoGestaoRede) {
        this.autorizadoGestaoRede = autorizadoGestaoRede;
    }

    public String getCodAcessoAutorizado() {
        if (codAcessoAutorizado == null) {
            codAcessoAutorizado = "";
        }
        return codAcessoAutorizado;
    }

    public void setCodAcessoAutorizado(String codAcessoAutorizado) {
        this.codAcessoAutorizado = codAcessoAutorizado;
    }

    public Integer getMatriculaAutorizado() {
        if (matriculaAutorizado == null){
            matriculaAutorizado = 0;
        }
        return matriculaAutorizado;
    }

    public void setMatriculaAutorizado(Integer matriculaAutorizado) {
        this.matriculaAutorizado = matriculaAutorizado;
    }

    public Integer getAveragePower() {
        return averagePower;
    }

    public void setAveragePower(Integer averagePower) {
        this.averagePower = averagePower;
    }

    public Integer getCalories() {
        return calories;
    }

    public void setCalories(Integer calories) {
        this.calories = calories;
    }

    public String getCredito() {
        return credito;
    }

    public void setCredito(String credito) {
        this.credito = credito;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }


    public String getToolTipCredito() {
        return toolTipCredito;
    }

    public void setToolTipCredito(String toolTipCredito) {
        this.toolTipCredito = toolTipCredito;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getTempoDeAula() {
        return tempoDeAula;
    }

    public void setTempoDeAula(Integer tempoDeAula) {
        this.tempoDeAula = tempoDeAula;
    }

    public Integer getPosicaoRankingAluno() {
        return posicaoRankingAluno;
    }

    public void setPosicaoRankingAluno(Integer posicaoRankingAluno) {
        this.posicaoRankingAluno = posicaoRankingAluno;
    }


    public void setOrigemSistema(String origemSistema) {
        this.origemSistema = origemSistema;
    }

    public String getOrigemSistema() {
        return origemSistema;
    }
}
