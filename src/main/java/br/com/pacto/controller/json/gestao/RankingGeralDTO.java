package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados do ranking geral de alunos no Crossfit")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RankingGeralDTO {

    @ApiModelProperty(value = "Código identificador do aluno", example = "123")
    private Integer alunoId;

    @ApiModelProperty(value = "Percentual de aproveitamento do aluno no ranking", example = "87.5")
    private Double aproveitamento;

    @ApiModelProperty(value = "Nome completo do aluno", example = "Ana Paula Costa")
    private String nome;

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public Double getAproveitamento() {
        return aproveitamento;
    }

    public void setAproveitamento(Double aproveitamento) {
        this.aproveitamento = aproveitamento;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
