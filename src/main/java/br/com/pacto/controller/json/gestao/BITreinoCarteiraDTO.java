package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO contendo dados de Business Intelligence relacionados à carteira de alunos, incluindo totais por situação, renovações e acompanhamentos.")
public class BITreinoCarteiraDTO {

    @ApiModelProperty(value = "Total geral de alunos na carteira.", example = "120")
    private Integer totalAlunos;

    @ApiModelProperty(value = "Total de alunos com situação ativa.", example = "97")
    private Integer ativos;

    @ApiModelProperty(value = "Total de alunos com situação inativa.", example = "18")
    private Integer inativos;

    @ApiModelProperty(value = "Total de alunos visitantes.", example = "5")
    private Integer visitantes;

    @ApiModelProperty(value = "Total de renovações de carteira realizadas.", example = "45")
    private Integer totalRenovacoesCarteira;

    @ApiModelProperty(value = "Total de não renovações de carteira.", example = "8")
    private Integer totalNaoRenovacoesCarteira;

    @ApiModelProperty(value = "Total de alunos sem acompanhamento.", example = "12")
    private Integer totalAlunosSemAcompanhamento;

    @ApiModelProperty(value = "Total de alunos em acompanhamento.", example = "85")
    private Integer totalAlunosEmAcompanhamento;

    public BITreinoCarteiraDTO(DashboardBI dash) {
        this.totalAlunos = dash.getTotalAlunos();
        this.ativos = dash.getTotalAlunosAtivos();
        this.inativos = dash.getTotalAlunosInativos();
        this.visitantes = dash.getTotalAlunosVisitantes();
        this.taxaRenovacaoZW = dash.getPercentualRenovacoes();
        this.aVencerZW = dash.getTotalAlunosAvencer();
        this.tempoPermanenciaCarteiraZW = new TempoPermanenciaDTO(dash);
        this.fluxoCarteiraZW = new FluxoCarteiraZWDTO(dash);
        this.totalRenovacoesCarteira = dash.getTotalRenovacoesCarteira();
        this.totalNaoRenovacoesCarteira = dash.getTotalNaoRenovaramCarteira();
        this.totalAlunosSemAcompanhamento = dash.getTotalAlunosSemAcompanhamento();
    }

    @ApiModelProperty(value = "Taxa de renovação calculada como percentual de contratos renovados sobre contratos vencidos nos últimos 30 dias.", example = "75")
    private Integer taxaRenovacaoZW;

    @ApiModelProperty(value = "Total de alunos cujos contratos estão a vencer em 30 dias.", example = "18")
    private Integer aVencerZW;

    @ApiModelProperty(value = "Dados de tempo de permanência na carteira quando um professor específico é definido.")
    private TempoPermanenciaDTO tempoPermanenciaCarteiraZW = new TempoPermanenciaDTO();

    @ApiModelProperty(value = "Dados de fluxo da carteira quando um professor específico é definido.")
    private FluxoCarteiraZWDTO fluxoCarteiraZW = new FluxoCarteiraZWDTO();

    public Integer getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(Integer totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public Integer getInativos() {
        return inativos;
    }

    public void setInativos(Integer inativos) {
        this.inativos = inativos;
    }

    public Integer getVisitantes() {
        return visitantes;
    }

    public void setVisitantes(Integer visitantes) {
        this.visitantes = visitantes;
    }

    public Integer getTaxaRenovacaoZW() {
        return taxaRenovacaoZW;
    }

    public void setTaxaRenovacaoZW(Integer taxaRenovacaoZW) {
        this.taxaRenovacaoZW = taxaRenovacaoZW;
    }

    public Integer getaVencerZW() {
        return aVencerZW;
    }

    public void setaVencerZW(Integer aVencerZW) {
        this.aVencerZW = aVencerZW;
    }

    public TempoPermanenciaDTO getTempoPermanenciaCarteiraZW() {
        return tempoPermanenciaCarteiraZW;
    }

    public void setTempoPermanenciaCarteiraZW(TempoPermanenciaDTO tempoPermanenciaCarteiraZW) {
        this.tempoPermanenciaCarteiraZW = tempoPermanenciaCarteiraZW;
    }

    public FluxoCarteiraZWDTO getFluxoCarteiraZW() {
        return fluxoCarteiraZW;
    }

    public void setFluxoCarteiraZW(FluxoCarteiraZWDTO fluxoCarteiraZW) {
        this.fluxoCarteiraZW = fluxoCarteiraZW;
    }

    public Integer getTotalRenovacoesCarteira() {
        return totalRenovacoesCarteira;
    }

    public void setTotalRenovacoesCarteira(Integer totalRenovacoesCarteira) {
        this.totalRenovacoesCarteira = totalRenovacoesCarteira;
    }

    public Integer getTotalNaoRenovacoesCarteira() {
        return totalNaoRenovacoesCarteira;
    }

    public void setTotalNaoRenovacoesCarteira(Integer totalNaoRenovacoesCarteira) {
        this.totalNaoRenovacoesCarteira = totalNaoRenovacoesCarteira;
    }

    public Integer getTotalAlunosSemAcompanhamento() { return totalAlunosSemAcompanhamento; }

    public void setTotalAlunosSemAcompanhamento(Integer totalAlunosSemAcompanhamento) { this.totalAlunosSemAcompanhamento = totalAlunosSemAcompanhamento; }

    public Integer getTotalAlunosEmAcompanhamento() {
        return totalAlunosEmAcompanhamento;
    }

    public void setTotalAlunosEmAcompanhamento(Integer totalAlunosEmAcompanhamento) {
        this.totalAlunosEmAcompanhamento = totalAlunosEmAcompanhamento;
    }
}
