package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
@ApiModel(description = "Resposta geral de dados de gráfico BI sem separação por professor, contendo dados agregados de todos os professores")
public class BIGraficoResponseGeralSemProfessorDTO {
    @ApiModelProperty(value = "Identificador único da view de gráfico BI", example = "Agenda")
    private String id;

    @ApiModelProperty(value = "Nome descritivo da view de gráfico BI", example = "Agenda")
    private String nome;

    @ApiModelProperty(value = "Indica se é uma view padrão do sistema ou personalizada pelo usuário", example = "true")
    private boolean padrao;

    @ApiModelProperty(value = "Lista dos indicadores selecionados para exibição no gráfico")
    private ArrayList<String> indicadores;

    @ApiModelProperty(value = "Mapa de professores incluídos na consulta, onde a chave é o ID e o valor é o nome ou 'todos'")
    private Map<String,String> professores;

    @ApiModelProperty(value = "Período em meses dos dados consultados", example = "6")
    private Integer periodoMeses;

    @ApiModelProperty(value = "Lista de dados dos indicadores organizados por período")
    private List<BIGraficoResponseSemProfessorDTO> dados;

     public BIGraficoResponseGeralSemProfessorDTO( ){

     }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isPadrao() {
        return padrao;
    }

    public void setPadrao(boolean padrao) {
        this.padrao = padrao;
    }

    public ArrayList<String> getIndicadores() {
        return indicadores;
    }

    public void setIndicadores(ArrayList<String> indicadores) {
        this.indicadores = indicadores;
    }

    public Map<String, String> getProfessores() {
        return professores;
    }

    public void setProfessores(Map<String, String> professores) {
        this.professores = professores;
    }

    public Integer getPeriodoMeses() {
        return periodoMeses;
    }

    public void setPeriodoMeses(Integer periodoMeses) {
        this.periodoMeses = periodoMeses;
    }

    public List<BIGraficoResponseSemProfessorDTO> getDados() {
        return dados;
    }

    public void setDados(List<BIGraficoResponseSemProfessorDTO> dados) {
        this.dados = dados;
    }
}
