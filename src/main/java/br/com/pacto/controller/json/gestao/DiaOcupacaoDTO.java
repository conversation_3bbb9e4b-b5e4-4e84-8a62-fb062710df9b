package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

@ApiModel(description = "Dados de ocupação por período do dia")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DiaOcupacaoDTO {

    @ApiModelProperty(value = "Número de agendamentos no período da manhã", example = "15")
    private Integer manha = 0;

    @ApiModelProperty(value = "Número de agendamentos no período da tarde", example = "25")
    private Integer tarde = 0;

    @ApiModelProperty(value = "Número de agendamentos no período da noite", example = "30")
    private Integer noite = 0;

    public DiaOcupacaoDTO() {
    }

    public DiaOcupacaoDTO(Map<String, Integer> mapa) {
        this.manha = mapa.get("manha");
        this.tarde = mapa.get("tarde");
        this.noite = mapa.get("noite");
    }
    public DiaOcupacaoDTO(Integer manha, Integer tarde, Integer noite) {
        this.manha = manha;
        this.tarde = tarde;
        this.noite = noite;
    }

    public Integer getManha() {
        return manha;
    }

    public void setManha(Integer manha) {
        this.manha = manha;
    }

    public Integer getTarde() {
        return tarde;
    }

    public void setTarde(Integer tarde) {
        this.tarde = tarde;
    }

    public Integer getNoite() {
        return noite;
    }

    public void setNoite(Integer noite) {
        this.noite = noite;
    }
}
