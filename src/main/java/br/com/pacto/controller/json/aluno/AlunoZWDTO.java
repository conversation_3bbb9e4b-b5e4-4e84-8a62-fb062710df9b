package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo in 09/05/2019
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de envio para integração do aluno com o sistema ZW.")
public class AlunoZWDTO {

    @ApiModelProperty(value = "Identificador interno do aluno", example = "1024")
    private Integer alunoId;

    @ApiModelProperty(value = "Identificador do professor responsável", example = "301")
    private Integer professorId;

    @ApiModelProperty(value = "Indica se o aluno usará o aplicativo ZW", example = "true")
    private Boolean usarAplicativo;

    @ApiModelProperty(value = "E-mail de login do aluno", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Senha de acesso do aluno", example = "senha123")
    private String senha;

    public Integer getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(Integer alunoId) {
        this.alunoId = alunoId;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Boolean getUsarAplicativo() {
        return usarAplicativo;
    }

    public void setUsarAplicativo(Boolean usarAplicativo) {
        this.usarAplicativo = usarAplicativo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }
}
