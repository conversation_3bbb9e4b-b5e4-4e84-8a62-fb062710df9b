/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.read;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Informações de um método de execução de exercício")
public class MetodoExecucaoJSON {

    @ApiModelProperty(value = "Nome do método de execução", example = "Pirâmide Decrescente")
    private String nome;

    @ApiModelProperty(value = "Código identificador do método", example = "0")
    private Integer metodo;

    public MetodoExecucaoJSON() {
    }

    public MetodoExecucaoJSON(String nome, Integer metodo) {
        this.nome = nome;
        this.metodo = metodo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getMetodo() {
        return metodo;
    }

    public void setMetodo(Integer metodo) {
        this.metodo = metodo;
    }
}
