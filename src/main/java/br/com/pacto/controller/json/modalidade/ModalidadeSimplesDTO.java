package br.com.pacto.controller.json.modalidade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 08/08/2019
 */
@ApiModel(description = "Dados simplificados de uma modalidade de treino")
public class ModalidadeSimplesDTO {

    @ApiModelProperty(value = "Código identificador único da modalidade", example = "5")
    private Integer id;

    @ApiModelProperty(value = "Nome da modalidade", example = "Musculação")
    private String nome;

    @ApiModelProperty(value = "Cor representativa da modalidade em hexadecimal", example = "#FF5722")
    private String cor;

    @ApiModelProperty(value = "URL da imagem da modalidade", example = "https://exemplo.com/modalidades/musculacao.jpg")
    private String imageUri;

    public ModalidadeSimplesDTO(Integer id, String nome, String cor) {
        this.id = id;
        this.nome = nome;
        this.cor = cor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getImageUri() {
        return imageUri == null ? "" : imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }
}
