package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DiasSemanaDashboardBI;
import br.com.pacto.util.enumeradores.DiasSemana;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "DTO contendo dados de execução de treinos por dias da semana, organizados por períodos do dia.")
public class BITreinoDiaSemanaExecucaoDTO {

    @ApiModelProperty(value = "Dados de execução de treinos na segunda-feira.")
    private PeriodoDiaDTO segunda = new PeriodoDiaDTO();

    @ApiModelProperty(value = "Dados de execução de treinos na terça-feira.")
    private PeriodoDiaDTO terca = new PeriodoDiaDTO();

    @ApiModelProperty(value = "Dados de execução de treinos na quarta-feira.")
    private PeriodoDiaDTO quarta = new PeriodoDiaDTO();

    @ApiModelProperty(value = "Dados de execução de treinos na quinta-feira.")
    private PeriodoDiaDTO quinta = new PeriodoDiaDTO();

    @ApiModelProperty(value = "Dados de execução de treinos na sexta-feira.")
    private PeriodoDiaDTO sexta = new PeriodoDiaDTO();

    @ApiModelProperty(value = "Dados de execução de treinos no sábado.")
    private PeriodoDiaDTO sabado = new PeriodoDiaDTO();

    @ApiModelProperty(value = "Dados de execução de treinos no domingo.")
    private PeriodoDiaDTO domingo = new PeriodoDiaDTO();

    public BITreinoDiaSemanaExecucaoDTO() {

    }
    public BITreinoDiaSemanaExecucaoDTO(List<DiasSemanaDashboardBI> listaOrdenada) {
        this.segunda = new PeriodoDiaDTO();
        this.terca = new PeriodoDiaDTO();
        this.quarta = new PeriodoDiaDTO();
        this.quinta = new PeriodoDiaDTO();
        this.sexta = new PeriodoDiaDTO();
        this.sabado = new PeriodoDiaDTO();
        this.domingo = new PeriodoDiaDTO();
        for(DiasSemanaDashboardBI dia : listaOrdenada){
            switch (dia.getDiaSemana()){
                case SABADO:
                    this.sabado = new PeriodoDiaDTO(dia);
                    break;
                case SEGUNDA_FEIRA:
                    this.segunda = new PeriodoDiaDTO(dia);
                    break;
                case TERCA_FEIRA:
                    this.terca = new PeriodoDiaDTO(dia);
                    break;
                case QUARTA_FEIRA:
                    this.quarta = new PeriodoDiaDTO(dia);
                    break;
                case QUINTA_FEIRA:
                    this.quinta = new PeriodoDiaDTO(dia);
                    break;
                case SEXTA_FEIRA:
                    this.sexta = new PeriodoDiaDTO(dia);
                    break;
                case DOMINGO:
                    this.domingo = new PeriodoDiaDTO(dia);
                    break;
            }
        }
    }
    public BITreinoDiaSemanaExecucaoDTO(List<DiasSemanaDashboardBI> listaOrdenada, List<TreinoRealizadoAppDTO> treinoRealizadoAppDTOS) {
        this.segunda = new PeriodoDiaDTO();
        this.terca = new PeriodoDiaDTO();
        this.quarta = new PeriodoDiaDTO();
        this.quinta = new PeriodoDiaDTO();
        this.sexta = new PeriodoDiaDTO();
        this.sabado = new PeriodoDiaDTO();
        this.domingo = new PeriodoDiaDTO();
        for(DiasSemanaDashboardBI dia : listaOrdenada){
            switch (dia.getDiaSemana()){
                case SABADO:
                    this.sabado = new PeriodoDiaDTO(dia, filtrarTreinosPorDiaDaSemana(treinoRealizadoAppDTOS, DiasSemana.SABADO));
                    break;
                case SEGUNDA_FEIRA:
                    this.segunda = new PeriodoDiaDTO(dia, filtrarTreinosPorDiaDaSemana(treinoRealizadoAppDTOS, DiasSemana.SEGUNDA_FEIRA));
                    break;
                case TERCA_FEIRA:
                    this.terca = new PeriodoDiaDTO(dia, filtrarTreinosPorDiaDaSemana(treinoRealizadoAppDTOS, DiasSemana.TERCA_FEIRA));
                    break;
                case QUARTA_FEIRA:
                    this.quarta = new PeriodoDiaDTO(dia, filtrarTreinosPorDiaDaSemana(treinoRealizadoAppDTOS, DiasSemana.QUARTA_FEIRA));
                    break;
                case QUINTA_FEIRA:
                    this.quinta = new PeriodoDiaDTO(dia, filtrarTreinosPorDiaDaSemana(treinoRealizadoAppDTOS, DiasSemana.QUINTA_FEIRA));
                    break;
                case SEXTA_FEIRA:
                    this.sexta = new PeriodoDiaDTO(dia, filtrarTreinosPorDiaDaSemana(treinoRealizadoAppDTOS, DiasSemana.SEXTA_FEIRA));
                    break;
                case DOMINGO:
                    this.domingo = new PeriodoDiaDTO(dia, filtrarTreinosPorDiaDaSemana(treinoRealizadoAppDTOS, DiasSemana.DOMINGO));
                    break;
            }
        }
    }

    public TreinoRealizadoAppDTO filtrarTreinosPorDiaDaSemana(List<TreinoRealizadoAppDTO> lista, DiasSemana diaSemanaFiltrado) {
        for (TreinoRealizadoAppDTO treino : lista) {
            if (treino.getDiaRealizado() == diaSemanaFiltrado) {
                return treino;
            }
        }

        return new TreinoRealizadoAppDTO();
    }

    public PeriodoDiaDTO getSegunda() {
        return segunda;
    }

    public void setSegunda(PeriodoDiaDTO segunda) {
        this.segunda = segunda;
    }

    public PeriodoDiaDTO getTerca() {
        return terca;
    }

    public void setTerca(PeriodoDiaDTO terca) {
        this.terca = terca;
    }

    public PeriodoDiaDTO getQuarta() {
        return quarta;
    }

    public void setQuarta(PeriodoDiaDTO quarta) {
        this.quarta = quarta;
    }

    public PeriodoDiaDTO getQuinta() {
        return quinta;
    }

    public void setQuinta(PeriodoDiaDTO quinta) {
        this.quinta = quinta;
    }

    public PeriodoDiaDTO getSexta() {
        return sexta;
    }

    public void setSexta(PeriodoDiaDTO sexta) {
        this.sexta = sexta;
    }

    public PeriodoDiaDTO getSabado() {
        return sabado;
    }

    public void setSabado(PeriodoDiaDTO sabado) {
        this.sabado = sabado;
    }

    public PeriodoDiaDTO getDomingo() {
        return domingo;
    }

    public void setDomingo(PeriodoDiaDTO domingo) {
        this.domingo = domingo;
    }
}
