/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.empresa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import servicos.integracao.adm.client.EmpresaWS;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Informações da empresa em formato JSON")
public class EmpresaJSON {

    @ApiModelProperty(value = "Identificador da empresa em formato string", example = "123")
    private String id;

    @ApiModelProperty(value = "Código numérico da empresa", example = "123")
    private Long codigo;

    @ApiModelProperty(value = "Nome da empresa", example = "Academia Pacto")
    private String nome;

    @ApiModelProperty(value = "Número de telefone da empresa", example = "(11) 3333-4444")
    private String telefone;

    public EmpresaJSON(){}

    public EmpresaJSON(EmpresaWS empresaWS){
      this.id = empresaWS.getCodigo().toString();
      this.nome = empresaWS.getNome();
      this.telefone = empresaWS.getNumero();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }
}
