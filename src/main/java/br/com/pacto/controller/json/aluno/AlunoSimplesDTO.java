package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados simplificados do aluno.")
public class AlunoSimplesDTO {

    @ApiModelProperty(value = "ID do aluno", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Nome completo do aluno", example = "João da <PERSON>")
    private String nome;

    @ApiModelProperty(value = "Nível do aluno")
    private NivelAlunoResponseTO nivel;

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "456789")
    private Integer matricula;

    @ApiModelProperty(value = "Situação atual do aluno", example = "Ativo")
    private String situacao;

    @ApiModelProperty(value = "Data de término do contrato", example = "2025-12-31")
    private Date terminoContrato;

    @ApiModelProperty(value = "Data de término do programa vigente", example = "2025-10-15")
    private Date terminoProgramaVigente;

    @ApiModelProperty(value = "Lista de e-mails do aluno", example = "[\"<EMAIL>\", \"<EMAIL>\"]")
    private List<String> emails = new ArrayList<>();

    @ApiModelProperty(value = "Lista de telefones do aluno")
    private List<TelefoneDTO> fones = new ArrayList<>();

    @ApiModelProperty(value = "Data do acompanhamento mais recente", example = "2025-06-01")
    private String dataAcompanhamento;

    public AlunoSimplesDTO() {

    }

    public AlunoSimplesDTO(ClienteSintetico clienteSintetico) {
        this.id = clienteSintetico.getCodigo();
        this.matricula = clienteSintetico.getMatricula();
        this.nome = clienteSintetico.getNome();
        this.terminoContrato = clienteSintetico.getDataFimPeriodoAcesso();
        if (clienteSintetico.getProgramaVigente() != null) {
            Date dataterminoPrograma = Uteis.getDataComHoraZerada(clienteSintetico.getProgramaVigente().getDataTerminoPrevisto());
            this.terminoProgramaVigente = dataterminoPrograma;
        }
        this.situacao = clienteSintetico.getSituacao();
        if (clienteSintetico.getDia() != null) {
            this.dataAcompanhamento = Uteis.getDataComHHMM(clienteSintetico.getDia());
        }
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public NivelAlunoResponseTO getNivel() {
        return nivel;
    }

    public void setNivel(NivelAlunoResponseTO nivel) {
        this.nivel = nivel;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getTerminoContrato() {
        return terminoContrato;
    }

    public void setTerminoContrato(Date terminoContrato) {
        this.terminoContrato = terminoContrato;
    }

    public Date getTerminoProgramaVigente() {
        return terminoProgramaVigente;
    }

    public void setTerminoProgramaVigente(Date terminoProgramaVigente) {
        this.terminoProgramaVigente = terminoProgramaVigente;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<TelefoneDTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneDTO> fones) {
        this.fones = fones;
    }

    public String getDataAcompanhamento() {
        return dataAcompanhamento;
    }

    public void setDataAcompanhamento(String dataAcompanhamento) {
        this.dataAcompanhamento = dataAcompanhamento;
    }
}
