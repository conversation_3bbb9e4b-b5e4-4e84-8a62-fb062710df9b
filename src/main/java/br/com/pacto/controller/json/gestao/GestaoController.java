package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.gestao.CategoriaIndicadorEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.programa.ExecucoesTreinoTO;
import br.com.pacto.bean.programa.ProgramaTreinoAndamentoTO;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.professor.FiltroGestaoJSON;
import br.com.pacto.controller.json.professor.ProfessorController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.gestao.GestaoServiceImpl;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.gestao.ExemploRespostaAndamentosProgramas;
import br.com.pacto.swagger.respostas.gestao.ExemploRespostaExecucoesTreino;
import br.com.pacto.swagger.respostas.gestao.ExemploRespostaIndicadoresAgenda;
import br.com.pacto.swagger.respostas.gestao.ExemploRespostaTiposEvento;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Api(tags = "BI Agenda")
@Controller
@RequestMapping("/psec/gestao")
public class GestaoController extends SuperControle {

    @Autowired
    private ProfessorSinteticoService professorSinteticoService;
    @Autowired
    private TipoEventoService tipoEventoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private GestaoServiceImpl gestaoService;

    @ApiOperation(
            value = "Consultar indicadores da agenda",
            notes = "Carrega os indicadores da agenda dos professores incluindo dados de agendamentos, execuções, cancelamentos e faltas. " +
                    "Retorna uma lista de indicadores agrupados por professor com suas respectivas estatísticas de agenda.",
            tags = "BI Agenda"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaIndicadoresAgenda.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-agenda", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAgenda(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>colaboradorIds:</strong> Filtra por um ou mais colaboradores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>tiposEvento:</strong> Filtra por um ou mais tipos de evento (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>dataInicio:</strong> Data inicial do período para consulta (timestamp em milissegundos).\n" +
                    "- <strong>dataFim:</strong> Data final do período para consulta (timestamp em milissegundos).\n" +
                    "- <strong>status:</strong> Filtra pela situação do aluno (Deve ser informado como uma lista ex: [\"ATIVO\", \"INATIVO\"]).",
                    defaultValue = "{\"colaboradorIds\":[10], \"tiposEvento\":[1], \"dataInicio\":1704067200000, \"dataFim\":1735689599000, \"status\":[\"ATIVO\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiParam(value = "Configurações adicionais para o filtro", defaultValue = "{}")
            @RequestParam(value = "configs", required = false) JSONObject configs,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId,
            HttpServletRequest request
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters, configs);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.AGENDA);

            return ResponseEntityFactory.ok(professorSinteticoService.carregarIndicadores(request, null, empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar indicadores da agenda por alunos",
            notes = "Carrega os indicadores da agenda detalhados por alunos incluindo nome do aluno, evento, horário e situação do agendamento. " +
                    "Retorna uma lista de agendamentos com informações específicas de cada aluno.",
            tags = "BI Agenda"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaIndicadoresAgenda.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/indicadores-agenda/alunos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> indicadoresAgendaAlunos(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>colaboradorIds:</strong> Filtra por um ou mais colaboradores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>tiposEvento:</strong> Filtra por um ou mais tipos de evento (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>dataInicio:</strong> Data inicial do período para consulta (timestamp em milissegundos).\n" +
                    "- <strong>dataFim:</strong> Data final do período para consulta (timestamp em milissegundos).",
                    defaultValue = "{\"colaboradorIds\":[10], \"tiposEvento\":[1], \"dataInicio\":1704067200000, \"dataFim\":1735689599000}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestHeader("empresaId") Integer empresaId
    ) {
        try {
            FiltroGestaoJSON filtroGestaoJSON = new FiltroGestaoJSON(filters);
            filtroGestaoJSON.setCategoria(CategoriaIndicadorEnum.AGENDA);

            return ResponseEntityFactory.ok(agendamentoService.obterPorProfessor(empresaId, filtroGestaoJSON, sort));
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar tipos de evento",
            notes = "Consulta os tipos de evento disponíveis para o usuário atual considerando suas permissões. " +
                    "Retorna uma lista de tipos de evento com ID e descrição que o usuário tem acesso.",
            tags = "BI Agenda"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaTiposEvento.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/tiposDeEvento", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tiposEvento(
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Indica se deve incluir tipos de evento inativos na consulta", defaultValue = "false", required = true)
            @RequestParam(value = "incluirInativos") Boolean incluirInativos
    ) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Usuario usuario = usuarioService.obterPorId(ctx, sessaoService.getUsuarioAtual().getId());
            List<TipoEvento> tipoEventoList = tipoEventoService.consultarQuaisTiposUsuarioTemPermissao(ctx, usuario, !incluirInativos);
            List<TipoDeEventosDTO> tipoDeEventosDTOS = new ArrayList<>();
            for (TipoEvento te : tipoEventoList) {
                tipoDeEventosDTOS.add(new TipoDeEventosDTO(te));
            }
            return ResponseEntityFactory.ok(tipoDeEventosDTOS);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar andamentos de programas de treino",
            notes = "Consulta o andamento dos programas de treino dos alunos incluindo nome do programa, aluno, execuções realizadas e porcentagem de conclusão. " +
                    "Retorna uma lista paginada com informações sobre o progresso dos programas de treino.",
            tags = "BI Agenda"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAndamentosProgramas.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.COLABORADORES)
    @RequestMapping(value = "/andamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gestaoAndamento(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do programa ou aluno.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\", \"aluno\"]).\n" +
                    "- <strong>professoresIds:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>dataInicio:</strong> Data inicial do período para consulta (timestamp em milissegundos).\n" +
                    "- <strong>dataFim:</strong> Data final do período para consulta (timestamp em milissegundos).",
                    defaultValue = "{\"quicksearchValue\":\"Silva\", \"quicksearchFields\":[\"aluno\"], \"professoresIds\":[10], \"dataInicio\":1704067200000, \"dataFim\":1735689599000}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {

        FiltroGestaoProgramaDTO filtroGestaoProgramaDTO;
        try {
            filtroGestaoProgramaDTO = new FiltroGestaoProgramaDTO(filters);
        } catch (JSONException e) {
            filtroGestaoProgramaDTO = null;
            e.printStackTrace();
        }

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ProgramaTreinoAndamentoTO> programas = gestaoService.gestaoAndamento(ctx, empresaId, filtroGestaoProgramaDTO, paginadorDTO);
            return ResponseEntityFactory.ok(programas, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar indicadores da agenda", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar execuções de treino",
            notes = "Consulta as execuções de treino dos alunos incluindo matrícula, nome do aluno, professor responsável e quantidade de execuções realizadas. " +
                    "Retorna uma lista paginada com informações sobre as execuções de treino dos alunos.",
            tags = "BI Agenda"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaExecucoesTreino.class)
    })
    @ResponseBody
    @RequestMapping(value = "/execucoes-treino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> gestaoExecucoesTreino(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno ou professor.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"aluno\", \"nome\"]).\n" +
                    "- <strong>professoresIds:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>dataInicio:</strong> Data inicial do período para consulta (timestamp em milissegundos).\n" +
                    "- <strong>dataFim:</strong> Data final do período para consulta (timestamp em milissegundos).\n" +
                    "- <strong>origemExecucao:</strong> Filtra pela origem da execução (Deve ser informado como uma lista ex: [\"APP\", \"WEB\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Silva\", \"quicksearchFields\":[\"aluno\"], \"professoresIds\":[10], \"dataInicio\":1704067200000, \"dataFim\":1735689599000, \"origemExecucao\":[\"APP\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {

        FiltroGestaoProgramaDTO filtroGestaoProgramaDTO;
        try {
            filtroGestaoProgramaDTO = new FiltroGestaoProgramaDTO(filters);
        } catch (JSONException e) {
            filtroGestaoProgramaDTO = null;
            e.printStackTrace();
        }
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ExecucoesTreinoTO> programas = gestaoService.gestaoExecucoesTreino(ctx, empresaId, filtroGestaoProgramaDTO, paginadorDTO);
            return ResponseEntityFactory.ok(programas, paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ProfessorController.class.getName()).log(Level.SEVERE, "Erro ao tentar carregar relatório de execuções de treino", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
