package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.gestao.IndicadorGraficoEnum;
import br.com.pacto.bean.gestao.ItemGraficoTO;
import br.com.pacto.bean.gestao.ItemGrupoIndicadores;
import br.com.pacto.bean.gestao.TipoGraficoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.gestao.BIGraficoService;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.olap4j.impl.ArrayMap;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Anderson
 * @since 18/10/2019
 */

@Controller
@RequestMapping("/psec/grafico-bi")
public class BIGraficoController extends SuperController {

    private final BIGraficoService biGraficoService;
    @Autowired
    private SessaoService sessaoService;


    @Autowired
    public BIGraficoController(BIGraficoService bi) {
        Assert.notNull(bi, "O serviço O serviço de gráficos BI não foi injetado corretamente");
        this.biGraficoService = bi;
    }

    @ApiOperation(
            value = "Obter dados de indicadores de gráfico BI",
            notes = "Consulta dados de indicadores de Business Intelligence para geração de gráficos. " +
                    "Retorna dados históricos dos indicadores selecionados, podendo incluir informações " +
                    "agregadas por professor ou dados gerais da academia. Suporta indicadores padrão " +
                    "e personalizados salvos pelo usuário.",
            tags = "BI Gráficos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Dados dos indicadores obtidos com sucesso", response = br.com.pacto.swagger.respostas.grafico.ExemploRespostaBIGraficoIndicadores.class)
    })
    @ResponseBody
    @RequestMapping(value = "/view-data/{nomeIndicador}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> viewsIndicadores(
            @ApiParam(value = "Código da empresa para consulta dos indicadores", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Nome do indicador ou grupo de indicadores para consulta. " +
                    "Pode ser um indicador padrão (ex: 'Agenda', 'Carteira', 'Treino') ou um indicador personalizado salvo pelo usuário.",
                    defaultValue = "Agenda", required = true)
            @PathVariable String nomeIndicador,
            @ApiParam(value = "Período em meses para consulta dos dados históricos dos indicadores",
                    defaultValue = "6", required = true)
            @RequestParam("meses") Integer meses) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ItemGrupoIndicadores> pesquisaGruposIndicadores =  biGraficoService.obterGruposIndicadores(ctx);
            List<ItemGrupoIndicadores> indicadoresVerificados = verificarIndicadorSalvo(pesquisaGruposIndicadores, nomeIndicador);
            List<ItemGraficoTO> indicadoresPadrao = new ArrayList<>();
            List<String> professoresSelecionados = new ArrayList<>();
            BIGraficoResponseGeralSemProfessorDTO responseSemProfessor = new BIGraficoResponseGeralSemProfessorDTO();
            BIGraficoResponseGeralComProfessorDTO respostaComProfessor = new BIGraficoResponseGeralComProfessorDTO();
            boolean existeIndicadorSalvo = !UteisValidacao.emptyList(indicadoresVerificados);
            if (!existeIndicadorSalvo){
                indicadoresPadrao = iniciarIndicadoresPadrao(nomeIndicador);
            }
            boolean isPadrao = !UteisValidacao.emptyList(indicadoresPadrao);
            if (existeIndicadorSalvo){
                String professores = "";
                for(ItemGrupoIndicadores i : indicadoresVerificados){
                    professores = i.getProfessores();
                }
                if(!UteisValidacao.emptyString(professores)){
                    String[] profs = professores.split(";");
                    professoresSelecionados = Arrays.asList(profs);
                }else{
                    //se estiver vazia o array dos professores colocar todos.
                    professoresSelecionados.add("0");
                }
                //Se está selecioando todos os professores.
                if (professoresSelecionados.size() == 1 && professoresSelecionados.get(0).equals("0")){
                    List<ItemGraficoTO> indicadores = obterIndicador(indicadoresVerificados);
                    List<BIGraficoResponseSemProfessorDTO> todosDados = new ArrayList<>();
                    List<DashboardBI> ListaTodosDash;
                    for (String professor : professoresSelecionados){
                        Map<String,String> professorAdd = new ArrayMap<>();
                        professorAdd.put(professor,"todos");
                        responseSemProfessor.setProfessores(professorAdd);
                        ListaTodosDash = biGraficoService.obterDash(ctx, Integer.parseInt(professor), empresaId);
                        todosDados.addAll(biGraficoService.obterDadosSemProfessor(ListaTodosDash, indicadores,meses, nomeIndicador));
                    }
                    responseSemProfessor.setId(nomeIndicador);
                    responseSemProfessor.setNome(nomeIndicador);
                    responseSemProfessor.setPeriodoMeses(meses);
                    responseSemProfessor.setPadrao(false);
                    ArrayList<String> itens = new ArrayList<>();
                    for (ItemGraficoTO item : indicadores){
                        itens.add(item.getIndicador().getValueField().toUpperCase());
                    }
                    responseSemProfessor.setIndicadores(itens);
                    responseSemProfessor.setDados(todosDados);
                    return ResponseEntityFactory.ok(responseSemProfessor);
                }
                //se está selecioando um ou mais professores que não todos
                if (professoresSelecionados.size() > 0){
                    List<ItemGraficoTO> indicadores = obterIndicador(indicadoresVerificados);
                    List<DashboardBI> ListaTodosDash = new ArrayList<>();
                    List<BIGraficoResponseProfessorDTO> listaProfessores = new ArrayList<>();
                    List<ProfessorSintetico> professorSinteticoList = new ArrayList<>();
                    List<BIGraficoResponseComProfessorDTO> todosDados = new ArrayList<>();
                    ProfessorSintetico professorSintetico = new ProfessorSintetico();
                    ProfessorSintetico professorSinteticoPorId = new ProfessorSintetico();
                    for (String professor : professoresSelecionados){
                        BIGraficoResponseProfessorDTO biGraficoResponseProfessorDTO = new BIGraficoResponseProfessorDTO();
                        professorSinteticoPorId = biGraficoService.obterPorId(ctx,Integer.parseInt(professor));
                        professorSintetico = biGraficoService.obterPorCodigoPessoaZW(ctx,professorSinteticoPorId.getCodigoPessoa(),empresaId);
                        professorSinteticoList.add(professorSintetico);
                        biGraficoResponseProfessorDTO.setId(professorSintetico.getCodigoColaborador().toString());
                        biGraficoResponseProfessorDTO.setNome(professorSintetico.getNome());
                        listaProfessores.add(biGraficoResponseProfessorDTO);
                        ListaTodosDash.addAll(biGraficoService.obterDash(ctx, Integer.parseInt(professor), empresaId));
                    }
                    todosDados = (biGraficoService.obterDadosPorProfessor(ListaTodosDash, indicadores, meses, professorSinteticoList));

                    respostaComProfessor.setProfessores(listaProfessores);
                    respostaComProfessor.setId(nomeIndicador);
                    respostaComProfessor.setNome(nomeIndicador);
                    respostaComProfessor.setPadrao(false);
                    ArrayList<String> itens = new ArrayList<>();
                    for (ItemGraficoTO item : indicadores){
                        if (item.isSelecionado()){
                            itens.add(item.getIndicador().getValueField().toUpperCase());
                        }
                    }
                    respostaComProfessor.setIndicadores(itens);
                    respostaComProfessor.setDados(todosDados);
                    return ResponseEntityFactory.ok(respostaComProfessor);
                }
                //se é um dos padrão.
            }else if(isPadrao){
                List<DashboardBI> listaTodosDash = biGraficoService.obterDash(ctx, 0, empresaId);
                responseSemProfessor.setId(nomeIndicador);
                responseSemProfessor.setNome(nomeIndicador);
                responseSemProfessor.setPeriodoMeses(meses);
                responseSemProfessor.setPadrao(true);
                ArrayList<String> itens = new ArrayList<>();
                for (ItemGraficoTO item : indicadoresPadrao){
                    if (item.isSelecionado()){
                        itens.add(item.getIndicador().getValueField().toUpperCase());
                    }
                }
                responseSemProfessor.setIndicadores(itens);
                Map<String, String> professorPadrao = new ArrayMap<>();
                professorPadrao.put("0","todos");
                responseSemProfessor.setProfessores(professorPadrao);
                responseSemProfessor.setDados(biGraficoService.obterDadosSemProfessor(listaTodosDash, indicadoresPadrao,meses, nomeIndicador));
                return ResponseEntityFactory.ok(responseSemProfessor);
            }
            return  ResponseEntityFactory.erroRegistroNotFoun("Nenhum indicador encontrado no banco na tabela 'itemgrupoindicadores' com esse nome","Nenhum indicador com esse nome foi encontrado");
        } catch (ServiceException e) {
            Logger.getLogger(BIGraficoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar todas as views de gráficos BI disponíveis",
            notes = "Consulta todas as views de gráficos de Business Intelligence disponíveis para o usuário. " +
                    "Inclui tanto os indicadores padrão do sistema (Agenda, Carteira, Treino, etc.) quanto " +
                    "os indicadores personalizados salvos pelo usuário.",
            tags = "BI Gráficos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de views obtida com sucesso", response = br.com.pacto.swagger.respostas.grafico.ExemploRespostaListaBIGraficoViews.class)
    })
    @ResponseBody
    @RequestMapping(value = "/views/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listaViews() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<ItemGrupoIndicadores> pesquisaGruposIndicadores =  biGraficoService.obterGruposIndicadores(ctx);
            ArrayList<String> labelsArray = new ArrayList<>();
            List<BIGraficoResponseListaDTO> listaDTOS = new ArrayList<>();
            for (ItemGrupoIndicadores indicadoresCadastrados : pesquisaGruposIndicadores){
                labelsArray.add(indicadoresCadastrados.getNome());
            }
            //retirnado os iguais
            List<String> labels = labelsArray.stream().distinct().collect(Collectors.toList());
            //adicionando os salvos
            for (String label : labels){
                BIGraficoResponseListaDTO listaDTO = new BIGraficoResponseListaDTO();
                listaDTO.setId(label);
                listaDTO.setNome(label);
                listaDTOS.add(listaDTO);
            }
            //adicionando os padrão
            listaDTOS.add(new BIGraficoResponseListaDTO("Agenda",true,"Agenda"));
            listaDTOS.add(new BIGraficoResponseListaDTO("Avaliação do treino dos alunos",true,"Avaliação do treino dos alunos"));
            listaDTOS.add(new BIGraficoResponseListaDTO("Avaliação Física",true,"Avaliação Física"));
            listaDTOS.add(new BIGraficoResponseListaDTO("Carteira",true,"Carteira"));
            listaDTOS.add(new BIGraficoResponseListaDTO("Percentuais",true,"Percentuais"));
            listaDTOS.add(new BIGraficoResponseListaDTO("Tipos de agendamento",true,"Tipos de agendamento"));
            listaDTOS.add(new BIGraficoResponseListaDTO("Total das avaliações de treino",true,"Total das avaliações de treino"));
            listaDTOS.add(new BIGraficoResponseListaDTO("Treino",true,"Treino"));
            return ResponseEntityFactory.ok(listaDTOS);
        } catch (ServiceException e) {
            Logger.getLogger(BIGraficoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar nova view de gráfico BI",
            notes = "Cadastra uma nova view personalizada de gráfico de Business Intelligence. " +
                    "Permite ao usuário criar combinações customizadas de indicadores e professores " +
                    "para visualização em gráficos personalizados.",
            tags = "BI Gráficos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "View cadastrada com sucesso", response = br.com.pacto.swagger.respostas.grafico.ExemploRespostaCadastroBIGraficoView.class)
    })
    @ResponseBody
    @RequestMapping(value = "/views", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroViews(
            @ApiParam(value = "Dados da view de gráfico BI em formato JSON. " +
                    "Deve conter o nome da view, lista de indicadores selecionados e IDs dos professores.",
                    defaultValue = "{\"nome\":\"Minha View Personalizada\",\"indicadores\":[\"TOTAL\",\"ATIVOS\"],\"professorIds\":[1,2]}",
                    required = true)
            @RequestBody String json) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<String> professoresSelecionados = new ArrayList<>();
            JSONObject objJson = new JSONObject(json);
            JSONArray indicadoresJson = objJson.getJSONArray("indicadores");
            int len = indicadoresJson.length();
            ArrayList<String> listaIndicadores = new ArrayList<>();
            for(int j=0; j<len; j++) {
                listaIndicadores.add(indicadoresJson.get(j).toString());
            }
            JSONArray professoresJson = objJson.getJSONArray("professorIds");
            int length = professoresJson.length();
            for(int j=0; j<length; j++) {
                ProfessorSintetico professorSintetico = biGraficoService.obterPorIdColaborador(ctx, Integer.parseInt(professoresJson.get(j).toString()));
                professoresSelecionados.add(professorSintetico.getCodigo().toString());
            }
            String nomeIndicador = objJson.get("nome").toString();
            List<ItemGraficoTO> indicadores = indicadorSalvar(listaIndicadores);
            biGraficoService.gravarGrupoIndicador(ctx,nomeIndicador,indicadores,professoresSelecionados);
            Map<String,String> retorno = new ArrayMap<>();
            retorno.put("id",nomeIndicador);
            return ResponseEntityFactory.ok(retorno);
        } catch (ServiceException e) {
            Logger.getLogger(BIGraficoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (JSONException e) {
            Logger.getLogger(BIGraficoController.class.getName()).log(Level.SEVERE, "Erro ao tentar converter JSON", e);
            return ResponseEntityFactory.erroInterno(e.toString(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Remover view de gráfico BI",
            notes = "Remove uma view personalizada de gráfico de Business Intelligence. " +
                    "Exclui permanentemente a configuração salva de indicadores e professores " +
                    "associada ao nome especificado.",
            tags = "BI Gráficos"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "View removida com sucesso", response = br.com.pacto.swagger.respostas.grafico.ExemploRespostaRemocaoBIGraficoView.class)
    })
    @ResponseBody
    @RequestMapping(value = "/views/{nomeIndicador}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerViews(
            @ApiParam(value = "Nome da view de gráfico BI a ser removida",
                    defaultValue = "Minha View Personalizada", required = true)
            @PathVariable String nomeIndicador) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            biGraficoService.excluirGrupo(ctx, nomeIndicador);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(BIGraficoController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private List<ItemGraficoTO> indicadorSalvar( List<String> listaIndicadores ) {
        List<ItemGraficoTO> indicadores = new ArrayList<>();
        for (String indicador : listaIndicadores){
            for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                if (ind.getValueField().toUpperCase().equals(indicador)){
                    indicadores.add(new ItemGraficoTO(ind,
                            true,
                            TipoGraficoEnum.LINHA));
                }
            }
        }
        return indicadores;
    }

    private List<ItemGraficoTO> obterIndicador(List<ItemGrupoIndicadores> pesquisaGruposIndicadores) {
        List<ItemGraficoTO> itemGraficoTOS = new ArrayList<>();
        for (ItemGrupoIndicadores indicador : pesquisaGruposIndicadores){
            ItemGraficoTO item = new ItemGraficoTO(indicador.getIndicador(),true,indicador.getTipo());
            itemGraficoTOS.add(item);
        }
        return itemGraficoTOS;
    }

    private List<ItemGrupoIndicadores> verificarIndicadorSalvo(List<ItemGrupoIndicadores> pesquisaGruposIndicadores, String nomeIndicador) {
        List<ItemGrupoIndicadores> indicadoresList = new ArrayList<>();
        for (ItemGrupoIndicadores itemGrupoIndicadores : pesquisaGruposIndicadores){
            if (itemGrupoIndicadores.getNome().equals(nomeIndicador)) {
                indicadoresList.add(itemGrupoIndicadores);
            }
        }
        return indicadoresList;
    }

    private List<ItemGraficoTO> iniciarIndicadoresPadrao(String nomeIndicador) {
        List<ItemGraficoTO> indicadores = new ArrayList<>();
        switch (nomeIndicador) {
            case "Agenda":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_AGENDAMENTO_PROFESSORES)
                                    || ind.equals(IndicadorGraficoEnum.IND_HORAS_DISPONIBILIDADE)
                                    || ind.equals(IndicadorGraficoEnum.IND_HORAS_EXECUTADAS)
                                    || ind.equals(IndicadorGraficoEnum.IND_PERC_OCUPACAO),
                            TipoGraficoEnum.LINHA));
                }

                break;
            case "Avaliação do treino dos alunos":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_NR_AVALIACOES)
                                    || ind.equals(IndicadorGraficoEnum.IND_MEDIA_AVALIACOES),
                            TipoGraficoEnum.LINHA));
                }

                break;
            case "Avaliação Física":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_COM_AVALIACAO)
                                    || ind.equals(IndicadorGraficoEnum.IND_SEM_AVALIACAO),
                            TipoGraficoEnum.LINHA));
                }

                break;
            case "Carteira":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_TOTAL_ALUNOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_ATIVOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_INATIVOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_RENOVADOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_NAO_RENOVADOS),
                            TipoGraficoEnum.LINHA));
                }

                break;
            case "Percentuais":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_PERC_TREINO_EM_DIA)
                                    || ind.equals(IndicadorGraficoEnum.IND_PERC_TREINO_VENCIDOS),
                            TipoGraficoEnum.LINHA));
                }

                break;
            case "Tipos de agendamento":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_AGENDAMENTO_NOVOS_TREINOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_AGENDAMENTO_TREINO_RENOVADOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_AGENDAMENTO_TREINO_REVISADOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_AGENDAMENTO_AVALIACOES_FISICAS),
                            TipoGraficoEnum.LINHA));
                }

                break;
            case "Total das avaliações de treino":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_ESTRELA_1)
                                    || ind.equals(IndicadorGraficoEnum.IND_ESTRELA_2)
                                    || ind.equals(IndicadorGraficoEnum.IND_ESTRELA_3)
                                    || ind.equals(IndicadorGraficoEnum.IND_ESTRELA_4)
                                    || ind.equals(IndicadorGraficoEnum.IND_ESTRELA_5),
                            TipoGraficoEnum.LINHA));
                }

                break;
            case "Treino":
                for (IndicadorGraficoEnum ind : IndicadorGraficoEnum.values()) {
                    indicadores.add(new ItemGraficoTO(ind,
                            ind.equals(IndicadorGraficoEnum.IND_ATIVOS_TREINO)
                                    || ind.equals(IndicadorGraficoEnum.IND_EM_DIA)
                                    || ind.equals(IndicadorGraficoEnum.IND_VENCIDOS)
                                    || ind.equals(IndicadorGraficoEnum.IND_RENOVAR),
                            TipoGraficoEnum.LINHA));
                }

                break;
        }
        return indicadores;
    }

}
