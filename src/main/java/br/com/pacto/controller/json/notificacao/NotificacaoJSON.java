/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.notificacao;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Representação de uma notificação do sistema")
public class NotificacaoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código único da notificação", example = "1001")
    private Integer cod;

    @ApiModelProperty(value = "Tipo da notificação. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- INICIOU_TREINO (Iniciou o treino)\n" +
            "- AUMENTOU_CARGA (Aumentou carga)\n" +
            "- DIMINUIU_CARGA (Diminuiu carga)\n" +
            "- CONCLUIU_TREINO (Concluiu treino)\n" +
            "- CHAMAR_ATENCAO_PROFESSOR (<PERSON><PERSON>u professor)\n" +
            "- CONCLUIU_SERIE (Concluiu uma série)\n" +
            "- EDITOU_SERIE (Editou uma série)\n" +
            "- ALUNO_CHEGOU (Chegou)\n" +
            "- ALUNO_SOLICITA_REAGENDA (Deseja reagendar)\n" +
            "- AGENDA (Lembrete agendamento)\n" +
            "- AGENDAMENTO_CONFIRMADO (Agendamento confirmado)\n" +
            "- AGENDAMENTO_CANCELADO (Agendamento cancelado)\n" +
            "- SOLICITAR_RENOVACAO (Solicitar Renovar Treino)\n" +
            "- LEMBRAR_ALUNO_COMPROMISSO (Lembrete Compromisso)\n" +
            "- AGENDAMENTO_NOVO (Novo Compromisso)\n" +
            "- AGENDAMENTO_ALTERADO (Agendamento alterado)\n" +
            "- ALUNO_EM_RISCO (Aluno em risco)\n" +
            "- ALUNO_AGENDOU (Aluno agendou)\n" +
            "- CONTATO_CRM (Contato CRM)\n" +
            "- ALUNO_DA_FILA_ENTROU_NA_AULA (A vaga para sua aula foi liberada)\n" +
            "- AGENDAMENTO_LOCACAO_CANCELADO (Agendamento da locação cancelado)",
            example = "INICIOU_TREINO")
    private String tipo;

    @ApiModelProperty(value = "Código do professor responsável pela notificação", example = "25")
    private Integer codProfessor;

    @ApiModelProperty(value = "Data e hora de registro da notificação no formato dd/MM/yyyy HH:mm", example = "15/03/2024 14:30")
    private String data;

    @ApiModelProperty(value = "Título da notificação", example = "Iniciou o treino")
    private String titulo;

    @ApiModelProperty(value = "Texto descritivo da notificação", example = "João Silva iniciou seu treino de musculação")
    private String texto;

    @ApiModelProperty(value = "Código do cliente/aluno relacionado à notificação", example = "1234")
    private Integer codCliente;

    @ApiModelProperty(value = "Telefone do aluno para contato", example = "(62) 99999-8888")
    private String telAluno;

    @ApiModelProperty(value = "Cor hexadecimal representando a gravidade da notificação. \n\n" +
            "<strong>Cores disponíveis</strong>\n" +
            "- #70c061 (LEVE - Verde)\n" +
            "- #FDA902 (MÉDIA - Amarelo)\n" +
            "- #DA2D3D (GRAVE - Vermelho)",
            example = "#70c061")
    private String gravidadeCor;

    @ApiModelProperty(value = "Opções disponíveis para interação com a notificação", example = "Confirmar|Cancelar")
    private String opcoes;

    @ApiModelProperty(value = "Resposta do usuário à notificação", example = "Confirmado")
    private String resposta;

    @ApiModelProperty(value = "Indica se a notificação foi lida pelo usuário", example = "false")
    private boolean lida = false;

    @ApiModelProperty(value = "Indica se o push da notificação foi enviado", example = "true")
    private boolean pushEnviado = false;

    public NotificacaoJSON(final Integer cod, final String titulo, final String texto,
            final String tipoNotf, final String data, final Integer codProfessor,
            final Integer codCliente, final String telAluno, final String gravidadeCor, final String opcoes, final String resposta, final  boolean lida, final  boolean pushEnviado) {
        this.cod = cod;
        this.titulo = titulo;
        this.texto = texto;
        this.tipo = tipoNotf;
        this.data = data;
        this.codProfessor = codProfessor;
        this.codCliente = codCliente;
        this.telAluno = telAluno;
        this.gravidadeCor = gravidadeCor;
        this.opcoes = opcoes;
        this.resposta = resposta;
        this.lida = lida;
        this.pushEnviado = pushEnviado;
    }

    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public Integer getCodProfessor() {
        return codProfessor;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public String getTelAluno() {
        return telAluno;
    }

    public void setTelAluno(String telAluno) {
        this.telAluno = telAluno;
    }

    public String getGravidadeCor() {
        return gravidadeCor;
    }

    public void setGravidadeCor(String gravidadeCor) {
        this.gravidadeCor = gravidadeCor;
    }

    public String getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(String opcoes) {
        this.opcoes = opcoes;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }


    public boolean isLida() {
        return lida;
    }

    public void setLida(boolean lida) {
        this.lida = lida;
    }

    public boolean isPushEnviado() {
        return pushEnviado;
    }

    public void setPushEnviado(boolean pushEnviado) {
        this.pushEnviado = pushEnviado;
    }
}
