package br.com.pacto.controller.json.turma;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.turma.TurmaService;
import br.com.pacto.swagger.respostas.turma.*;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/turmas")
public class TurmasController extends SuperController {


    private TurmaService turmaService;

    @Autowired
    public TurmasController(TurmaService turmaService) {
        Assert.notNull(turmaService, "O serviço de turmas não foi injetado corretamente");
        this.turmaService = turmaService;
    }

    @ApiOperation(
            value = "Consultar turmas",
            notes = "Lista turmas com filtros e paginação",
            tags = "Turma"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Turmas listadas com sucesso", response = ExemploRespostaListTurmaResponseDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTurmas(@ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                                                                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                                                                    "<strong>Filtros disponíveis:</strong>\n" +
                                                                    "- <strong>codigo:</strong> Filtra por código específico da turma (Deve ser informado como número ex: 123).\n" +
                                                                    "- <strong>vigencia:</strong> Filtra por vigência da turma (Deve ser informado como uma lista ex: [\"VIGENTE\", \"NAO_VIGENTE\"]).\n" +
                                                                    "- <strong>modalidadeIds:</strong> Filtra por uma ou mais modalidades (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                                                                    "- <strong>quicksearchValue:</strong> Termo de busca para nome ou identificador da turma.",
                                                                    defaultValue = "{\"vigencia\":[\"VIGENTE\"], \"modalidadeIds\":[1], \"quicksearchValue\":\"Spinning\"}")
                                                            @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            @ApiIgnore PaginadorDTO paginadorDTO,
                                                            @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.listarTurmas(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar turmas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar turma por ID",
            notes = "Obtém os detalhes de uma turma específica pelo seu código identificador",
            tags = "Turma"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Turma obtida com sucesso", response = ExemploRespostaTurmaResponseDTOIndividual.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTurma(@ApiParam(value = "Código identificador da turma", required = true, example = "1")
                                                          @PathVariable Integer id) throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.obterTurma(id));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Salvar turma",
            notes = "Cadastra uma nova turma no sistema",
            tags = "Turma"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Turma salva com sucesso", response = ExemploRespostaTurmaResponseDTOIndividual.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> save(@ApiParam(value = "Dados da turma para cadastro", required = true)
                                                    @RequestBody TurmaResponseDTO turmaDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.save(turmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar turma", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Atualizar turma",
            notes = "Atualiza os dados de uma turma existente",
            tags = "Turma"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Turma atualizada com sucesso", response = ExemploRespostaTurmaResponseDTOIndividual.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> update(@ApiParam(value = "Dados da turma para atualização", required = true)
                                                      @RequestBody TurmaResponseDTO turmaDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.update(turmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Sincronizar turma com MGB", notes = "Inicia a sincronização de uma turma com a plataforma MGB", tags = "Configurar Turmas")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Sincronização iniciada com sucesso", response = ExemploRespostaSincronizacaoMGB.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/sync-turma-mgb/{codTurma}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> syncTurmaMgb(@ApiParam(value = "Código da turma para sincronização", required = true, example = "1")
                                                            @PathVariable Integer codTurma,
                                                            @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            turmaService.syncTurmaMgb(empresaId, codTurma);
            return ResponseEntityFactory.ok("A sincronização foi iniciada e pode levar alguns minutos, dependendo da quantidade de horários da turma. Acompanhe as turmas sincronizadas na plataforma do MGB.");
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao sincronizar turma com mgb", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Salvar ou atualizar lista de horários", notes = "Salva ou atualiza uma lista de horários de turma", tags = "Turma")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Horários salvos/atualizados com sucesso", response = ExemploRespostaListHorarioTurmaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveOrUpdateHorario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateHorario(@ApiParam(value = "Lista de horários para salvar ou atualizar", required = true)
                                                                   @RequestBody List<HorarioTurmaResponseDTO> horarioDTO,
                                                                   @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveOrUpdateHorario(horarioDTO, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar/atualizar horários", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Salvar horário", notes = "Cadastra um novo horário de turma", tags = "Turma")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Horário salvo com sucesso", response = ExemploRespostaHorarioTurmaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveHorario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveHorario(@ApiParam(value = "Dados do horário para cadastro", required = true)
                                                           @RequestBody HorarioTurmaResponseDTO horarioDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveHorario(horarioDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar horário", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Atualizar horário", notes = "Atualiza os dados de um horário de turma existente", tags = "Turma")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Horário atualizado com sucesso", response = ExemploRespostaHorarioTurmaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/updateHorario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> updateHorario(@ApiParam(value = "Dados do horário para atualização", required = true)
                                                             @RequestBody HorarioTurmaResponseDTO horarioDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.updateHorario(horarioDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar horário", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Salvar ambiente", notes = "Cadastra um novo ambiente para turmas", tags = "Turma")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Ambiente salvo com sucesso", response = ExemploRespostaAmbienteDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveAmbiente", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveAmbiente(@ApiParam(value = "Dados do ambiente para cadastro", required = true)
                                                            @RequestBody AmbienteDTO ambienteDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveAmbiente(ambienteDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar ambiente", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Salvar nível de turma", notes = "Cadastra um novo nível de turma", tags = "Nível de Turma")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Nível de turma salvo com sucesso", response = ExemploRespostaNivelTurmaDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/saveNivelTurma", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveNivelTurma(@ApiParam(value = "Dados do nível de turma para cadastro", required = true)
                                                              @RequestBody NivelTurmaDTO nivelTurmaDTO) {
        try {
            return ResponseEntityFactory.ok(turmaService.saveNivelTurma(nivelTurmaDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar nível de turma", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Listar horários de turma", notes = "Lista os horários de uma turma específica com filtros e paginação", tags = "Turma")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "professor,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Horários listados com sucesso", response = ExemploRespostaListHorarioTurmaResponseDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horarioTurma/{turma}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarHorariosTurma(@ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                                                                           "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                                                                           "<strong>Filtros disponíveis:</strong>\n" +
                                                                           "- <strong>professor:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                                                                           "- <strong>ambiente:</strong> Filtra por um ou mais ambientes (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                                                                           "- <strong>nivelTurma:</strong> Filtra por um ou mais níveis de turma (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                                                                           "- <strong>dias:</strong> Filtra por dias da semana (Deve ser informado como uma lista ex: [\"Segunda-feira\", \"Terça-feira\"]).",
                                                                           defaultValue = "{\"professor\":[10], \"ambiente\":[1], \"nivelTurma\":[1], \"dias\":[\"Segunda-feira\"]}")
                                                                   @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                   @ApiIgnore PaginadorDTO paginadorDTO,
                                                                   @ApiParam(value = "Código identificador da turma", required = true, example = "1")
                                                                   @PathVariable Integer turma) throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.listarHorariosTurma(filtros, paginadorDTO, turma), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar horários da turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Remover horário de turma", notes = "Remove um horário específico de uma turma", tags = "Turma")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Horário removido com sucesso", response = ExemploRespostaRemocaoHorario.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horarioTurma/remover/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerHorarioTurma(@ApiParam(value = "Código identificador do horário", required = true, example = "1")
                                                                   @PathVariable Integer id,
                                                                   @ApiParam(value = "Código identificador da empresa", required = true, example = "1")
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.removerHorarioTurma(id, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover horário da turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "Validar horário de turma", notes = "Valida se existem alunos matriculados em um horário específico da turma", tags = "Turma")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Validação realizada com sucesso", response = ExemploRespostaValidacaoHorario.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horarioTurma/validar/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarHorarioTurma(@ApiParam(value = "Código identificador do horário", required = true, example = "1")
                                                                   @PathVariable Integer id) throws JSONException {
        try {
            return ResponseEntityFactory.ok(turmaService.validarExisteAlunosHorarioTurma(id));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar horário da turma", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
