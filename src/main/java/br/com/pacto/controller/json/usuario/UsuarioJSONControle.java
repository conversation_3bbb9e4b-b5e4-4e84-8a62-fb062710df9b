/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.usuario;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.oamd.Conexao;
import br.com.pacto.base.oamd.OAMD;
import br.com.pacto.base.oamd.OAMDService;
import br.com.pacto.base.oamd.RedeEmpresaVO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.base.util.Propagador;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.colaborador.ColaboradorTO;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.cliente.ClienteAppJSON;
import br.com.pacto.controller.json.cliente.ClienteJSON;
import br.com.pacto.controller.json.cliente.ClienteResumidoJSON;
import br.com.pacto.controller.json.colaborador.ColaboradorController;
import br.com.pacto.controller.json.crossfit.CrossfitJSONControle;
import br.com.pacto.controller.json.crossfit.RankingJSON;
import br.com.pacto.enumerador.cliente.StatusClienteEnum;
import br.com.pacto.objeto.*;
import br.com.pacto.security.dto.AlterarSenhaDTO;
import br.com.pacto.security.dto.UsuarioAppDTO;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.usuario.UsuarioServiceImpl;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.cliente.ClienteRedeEmpresaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.gestao.BITreinoService;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.service.intf.professor.ProfessorSinteticoService;
import br.com.pacto.service.intf.programa.PrescricaoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.usuario.FotoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.service.intf.usuarioEmail.UsuarioEmailService;
import br.com.pacto.service.intf.wod.WodService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.PontosAlunosTO;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.json.AgendaTotalJSON;
import br.com.pacto.util.json.ItemJSON;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;
import br.com.pacto.swagger.respostas.usuario.ExemploRespostaValidarUsuarioRedeSocialCrypt;
import br.com.pacto.swagger.respostas.usuario.ExemploRespostaValidarUsuarioRedeSocialCryptPorCodUsuario;
import br.com.pacto.swagger.respostas.usuario.ExemploRespostaValidarUsuarioCrypt;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/usuario")
public class UsuarioJSONControle extends SuperControle {

    private transient IntegracaoCadastrosWSConsumer iCadWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

    private final ClienteSinteticoService clienteSinteticoService;
    private final UsuarioService usuarioService;
    private final UsuarioEmailService usuarioEmailService;
    private final ProfessorSinteticoService professorSinteticoService;
    private final EmpresaService empresaService;
    private final FotoService fotoService;
    private final BITreinoService biTreinoService;
    private final PerfilService perfilService;
    private final OAMDService oamdService;
    private final PrescricaoService prescricaoService;
    private final ClienteRedeEmpresaService clienteRedeEmpresaService;

    @Autowired
    public UsuarioJSONControle(ClienteSinteticoService clienteSinteticoService, UsuarioService usuarioService,
                               UsuarioEmailService usuarioEmailService,
                               PrescricaoService prescricaoService,
                               ProfessorSinteticoService professorSinteticoService, EmpresaService empresaService,
                               FotoService fotoService,
                               OAMDService oamdService,
                               ClienteRedeEmpresaService clienteRedeEmpresaService,
                               BITreinoService biTreinoService, PerfilService perfilService) {

        this.clienteSinteticoService = clienteSinteticoService;
        this.prescricaoService = prescricaoService;
        this.oamdService = oamdService;
        this.clienteRedeEmpresaService = clienteRedeEmpresaService;
        this.usuarioService = usuarioService;
        this.professorSinteticoService = professorSinteticoService;
        this.empresaService = empresaService;
        this.fotoService = fotoService;
        this.biTreinoService = biTreinoService;
        this.perfilService = perfilService;
        this.usuarioEmailService = usuarioEmailService;
    }

    @RequestMapping(value = "{ctx}/login", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuario(@PathVariable String ctx,
                            @RequestParam String userName, @RequestParam String pwd, HttpServletRequest request) {
        ModelMap mm = new ModelMap();

        try {
            UsuarioJSON uJSON = obterUsuarioValidado(request, ctx, userName, pwd);
            mm.addAttribute(RETURN, uJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Validar usuário com dados criptografados",
                  notes = "Valida as credenciais de um usuário utilizando dados criptografados para maior segurança na transmissão. " +
                          "Recebe userName e pwd em formato JSON criptografado e retorna os dados completos do usuário também criptografados. " +
                          "<br/><br/>" +
                          "<strong>Permissões:</strong> Não requer autenticação prévia<br/>" +
                          "<strong>Comportamento:</strong> Endpoint de autenticação segura com criptografia AES<br/>" +
                          "<strong>Retorno:</strong> Dados do usuário em formato JSON criptografado ou erro criptografado",
                  tags = "Autenticação e Login")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Usuário validado com sucesso",
                        response = br.com.pacto.swagger.respostas.usuario.ExemploRespostaValidarUsuarioCrypt.class)
    })
    @RequestMapping(value = "{ctx}/vA6fM2oW4uY8tS4aA4bZ6tM2uA0tV8dB", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioCrypt(HttpServletRequest request,
                                @ApiParam(value = "Contexto da empresa/academia para identificação do ambiente",
                                         required = true,
                                         defaultValue = "academia123")
                                @PathVariable String ctx,
                                @ApiParam(value = "Dados de login criptografados em formato JSON. " +
                                                 "Deve conter os campos 'userName' e 'pwd' criptografados com AES. " +
                                                 "<br/><br/>" +
                                                 "<strong>Estrutura esperada (antes da criptografia):</strong><br/>" +
                                                 "- <strong>userName:</strong> Nome de usuário ou e-mail para login<br/>" +
                                                 "- <strong>pwd:</strong> Senha do usuário<br/><br/>" +
                                                 "<strong>Exemplo JSON antes da criptografia:</strong><br/>" +
                                                 "{\"userName\":\"<EMAIL>\", \"pwd\":\"minhasenha123\"}",
                                         required = true,
                                         defaultValue = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************")
                                @RequestBody String content) {
        ModelMap mm = new ModelMap();

        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String userName = o.optString("userName");
            String pwd = o.optString("pwd");

            UsuarioJSON uJSON = obterUsuarioValidado(request, ctx, userName, pwd);

            mm.addAttribute(RETURN, Uteis.encryptUserData(new JSONObject(uJSON).toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private UsuarioJSON obterUsuarioValidado(HttpServletRequest request, String ctx, String userName, String pwd) throws Exception {
        Usuario u;
        try {
            u = usuarioService.validarUsuario(ctx, userName, pwd);
        } catch (Exception ex) {
            u = usuarioService.validarUsuario(ctx, userName, pwd, true, true);
            if (u != null && u.getCliente() == null) {
                throw new ServiceException("Usuário não encontrado.");
            }
        }
        try {
            if (u.getUsuarioEmail() != null && !UteisValidacao.emptyNumber(u.getUsuarioEmail().getCodigo())) {
                u.setUsuarioEmail(usuarioEmailService.obterPorId(ctx, u.getUsuarioEmail().getCodigo(), u.getTipo()));
            }
        } catch (Exception e) {
            Uteis.logar(e, UsuarioServiceImpl.class);
        }
        if (u.getCliente() != null) {
            u.setAvatar(fotoService.defineURLFotoPessoa(request, u.getCliente().getPessoa().getFotoKey(), u.getCliente().getCodigoPessoa(), false, ctx, false));
        } else {
            u.setAvatar(fotoService.defineURLFotoPessoa(request, u.getProfessor().getPessoa().getFotoKey(), u.getProfessor().getCodigoPessoa(), false, ctx, false));
        }
        UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(ctx, u));
        try {
            if (u.getCliente() != null
                    && !UteisValidacao.emptyNumber(u.getCliente().getMatricula())) {
                String result = iCadWS.nomenclaturaVendaCredito(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, u.getCliente().getMatricula().toString());
                JSONObject json = new JSONObject(result);
                uJSON.setNomenclaturaVendaCredito(json.getString("nomenclatura"));
                uJSON.setSaldoCreditos(json.getInt("saldocreditotreino"));
                uJSON.setTotalCreditos(json.getInt("totalcreditotreino"));
                uJSON.setContratoCredito(json.getBoolean("contratoCredito"));
            }
            if(u.getCliente() != null && u.getCliente().getDia() != null) {
                uJSON.setDia(u.getCliente().getDia().getTime());
            }
        } catch (Exception e) {
            //erros na obtenção dos creditos não podem barrar o login
            uJSON.setNomenclaturaVendaCredito("credito");
            uJSON.setSaldoCreditos(0);
            uJSON.setTotalCreditos(0);
            uJSON.setContratoCredito(false);
        }
        uJSON.setTreinoIndependente(independente(ctx));
        if (uJSON != null && !uJSON.getTreinoIndependente()) {
            uJSON.setAcessoAcademia(usuarioService.temAcessoAcademia(uJSON, ctx));
        }
        return uJSON;
    }

    @RequestMapping(value = "{ctx}/loginRF", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioRF(@PathVariable String ctx,
                            @RequestParam String userName, @RequestParam String pwd, HttpServletRequest request) {
        ModelMap mm = new ModelMap();

        try {
            Usuario u = usuarioService.validarUsuario(ctx, userName, pwd, true);
            if (!u.isPermiteLoginWeb()) {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
            return validarUsuario(ctx, userName, pwd, request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/loginApp", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioApp(@PathVariable String ctx,
                            @RequestParam String userName, @RequestParam String pwd, HttpServletRequest request) {
        ModelMap mm = new ModelMap();

        try {
            Usuario u = usuarioService.validarUsuarioApp(ctx, userName, pwd);
            if (u.getCliente() != null) {
                u.setAvatar(fotoService.defineURLFotoPessoa(request, u.getCliente().getPessoa().getFotoKey(), u.getCliente().getCodigoPessoa(), false, ctx, false));
            } else {
                u.setAvatar(fotoService.defineURLFotoPessoa(request, u.getProfessor().getPessoa().getFotoKey(), u.getProfessor().getCodigoPessoa(), false, ctx, false));
            }
            UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(ctx, u));
            try {
                if(u.getCliente() != null
                        && !UteisValidacao.emptyNumber(u.getCliente().getMatricula())){
                    String result = iCadWS.nomenclaturaVendaCredito(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, u.getCliente().getMatricula().toString());
                    JSONObject json = new JSONObject(result);
                    uJSON.setNomenclaturaVendaCredito(json.getString("nomenclatura"));
                    uJSON.setSaldoCreditos(json.getInt("saldocreditotreino"));
                    uJSON.setTotalCreditos(json.getInt("totalcreditotreino"));
                    uJSON.setContratoCredito(json.getBoolean("contratoCredito"));
                }
            }catch (Exception e){
                //erros na obtenção dos creditos não podem barrar o login
                uJSON.setNomenclaturaVendaCredito("credito");
                uJSON.setSaldoCreditos(0);
                uJSON.setTotalCreditos(0);
                uJSON.setContratoCredito(false);
            }
            uJSON.setTreinoIndependente(independente(ctx));
            mm.addAttribute(RETURN, uJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/v2/login", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioV2(@PathVariable String ctx,
                            @RequestParam String userName, @RequestParam String pwd, HttpServletRequest request) {
        ModelMap mm = new ModelMap();

        try {
            List<Usuario> users = usuarioService.validarUsuarioV2(ctx, userName, pwd);
            List<UsuarioJSON> usuarios = new ArrayList<>();
            for (Usuario u : users) {
                if (u.getCliente() != null) {
                    u.setAvatar(fotoService.defineURLFotoPessoa(request, u.getCliente().getPessoa().getFotoKey(), u.getCliente().getCodigoPessoa(), false, ctx, false));
                } else {
                    u.setAvatar(fotoService.defineURLFotoPessoa(request, u.getProfessor().getPessoa().getFotoKey(), u.getProfessor().getCodigoPessoa(), false, ctx, false));
                }
                UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(ctx, u));
                try {
                    if (u.getCliente() != null
                            && !UteisValidacao.emptyNumber(u.getCliente().getMatricula())) {
                        String result = iCadWS.nomenclaturaVendaCredito(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, u.getCliente().getMatricula().toString());
                        JSONObject json = new JSONObject(result);
                        uJSON.setNomenclaturaVendaCredito(json.getString("nomenclatura"));
                        uJSON.setSaldoCreditos(json.getInt("saldocreditotreino"));
                        uJSON.setTotalCreditos(json.getInt("totalcreditotreino"));
                        uJSON.setContratoCredito(json.getBoolean("contratoCredito"));
                    }
                } catch (Exception e) {
                    //erros na obtenção dos creditos não podem barrar o login
                    uJSON.setNomenclaturaVendaCredito("credito");
                    uJSON.setSaldoCreditos(0);
                    uJSON.setTotalCreditos(0);
                    uJSON.setContratoCredito(false);
                }
                uJSON.setTreinoIndependente(independente(ctx));
                usuarios.add(uJSON);
            }
            mm.addAttribute(RETURN, usuarios);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/alterarEmail", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alterarEmail(@PathVariable String ctx,
                          @RequestParam String email, @RequestParam String senha, @RequestParam String novoEmail) {
        ModelMap mm = new ModelMap();

        try {
            Usuario u = usuarioService.validarUsuario(ctx, email, senha);
            Conexao conexao = new Conexao();
            String modulos = OAMD.obterModulos(ctx,conexao.obterConexaoBancoEmpresas());
            String result = "";
            if (!modulos.contains("ZW")) {
                u.setNome(novoEmail);
                u.setUserName(novoEmail);

                result = "Email enviado!";
                usuarioService.alterar(ctx, u);
            } else {
                result = iCadWS.alterarUsuarioMovelAluno(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx,
                        novoEmail, senha, u.getCliente().getCodigoCliente(),SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx));
                if (result != null && result.contains("ERRO")) {
                    throw new ServiceException(result);
                }
            }
            clienteSinteticoService.adicionarUsuarioServicoDescobrir(ctx, novoEmail);
            mm.addAttribute(RETURN, result);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/alterarSenha", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alterarSenha(@PathVariable String ctx,
                          @RequestParam String email, @RequestParam String senha, @RequestParam String novaSenha) {
        ModelMap mm = new ModelMap();

        try {
            Usuario u = usuarioService.validarUsuario(ctx, email, senha, true, true);
            Conexao conexao = new Conexao();
            String modulos = OAMD.obterModulos(ctx, conexao.obterConexaoBancoEmpresas());
            String result = "";
            u.setSenha(Uteis.encriptar(Uteis.removerEspacosInicioFimString(novaSenha)));
            usuarioService.alterar(ctx, u);
            mm.addAttribute(STATUS_SUCESSO, "Senha alterada.");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/bQ3jI3pM1zT1aB7mE3gC3zO6nC3nW1pF", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alterarSenhaCrypt(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();

        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String email = o.optString("email");
            String senha = o.optString("senha");
            String novaSenha = o.optString("novaSenha");

            Usuario u = usuarioService.validarUsuario(ctx, email, senha, true, true);
            u.setSenha(Uteis.encriptar(Uteis.removerEspacosInicioFimString(novaSenha)));
            usuarioService.alterar(ctx, u);

            mm.addAttribute(STATUS_SUCESSO, Uteis.encryptUserData("Senha alterada."));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/v2/bQ3jI3pM1zT1aB7mE3gC3zO6nC3nW1pF", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alterarSenhaPorUsuarioCrypt(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();

        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String email = o.optString("email");
            String novaSenha = o.optString("novaSenha");
            if(UteisValidacao.emptyString(email) || UteisValidacao.emptyString(novaSenha)) {
                throw new ServiceException("Email e nova senha são obrigatórios.");
            }

            Usuario u = usuarioService.validarUsuarioMovelApp(ctx, email, true);
            u.setSenha(Uteis.encriptar(Uteis.removerEspacosInicioFimString(novaSenha)));
            usuarioService.alterar(ctx, u);

            mm.addAttribute(STATUS_SUCESSO, Uteis.encryptUserData("Senha alterada."));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/lembrarSenha", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap lembrarSenha(@PathVariable String ctx,
                          @RequestParam String email) {
        ModelMap mm = new ModelMap();
        try {
            Usuario u = usuarioService.obterPorAtributo(ctx, "userName", email);
            if (u != null) {
                String result = "";
                if (u.getProfessor() != null) {
                    throw new ServiceException("Método não implementado");
                } else {
                    result = iCadWS.alterarUsuarioMovelAluno(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, email, null,
                            u.getCliente().getCodigoCliente(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx));
                }
                if (result != null && result.contains("ERRO")) {
                    throw new ServiceException(result);
                }
                mm.addAttribute(RETURN, result);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/lembrarSenhaAppAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap lembrarSenhaAppAluno(@PathVariable String ctx,
                                  @RequestParam String email,
                                  @RequestParam (required = false) Boolean cliente) {
        ModelMap mm = new ModelMap();
        try {
            String result = processarLembrarSenhaAppAluno(ctx, email, cliente);
            mm.addAttribute(RETURN, result);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private String processarLembrarSenhaAppAluno(String ctx, String email, Boolean cliente) throws Exception {
        String result = "";
        if(cliente != null) {
            Integer codigo = usuarioService.consultarUsuarioMovelPorEmail(ctx, email, cliente);
            if(codigo == null) {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
            result = iCadWS.alterarUsuarioMovelAlunoNovoAppAluno(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, email, null,
                    codigo, cliente ? "APP Treino - Cliente" : "APP Treino - Colaborador", SuperControle.getAppUrlEmail(ctx));
        } else {
            Usuario u = usuarioService.obterPorAtributo(ctx, "userName", email);
            if (u != null) {
                if (u.getProfessor() != null) {
                    result = iCadWS.alterarUsuarioMovelAlunoNovoAppAluno(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, email, null,
                            u.getProfessor().getCodigoColaborador(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx));
                } else {
                    result = iCadWS.alterarUsuarioMovelAlunoNovoAppAluno(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, email, null,
                            u.getCliente().getCodigoCliente(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx));
                }
                if (result != null && result.contains("ERRO")) {
                    throw new ServiceException(result);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        }
        return result;
    }

    @RequestMapping(value = "{ctx}/rO2aS2tZ5jJ2aM8hE3dZ3pH1jY4lG1mM", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap lembrarSenhaAppAlunoCrypt(@PathVariable String ctx, @RequestBody String content) {
        ModelMap mm = new ModelMap();

        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String email = o.optString("email");
            boolean cliente = o.optBoolean("cliente");

            String result = processarLembrarSenhaAppAluno(ctx, email, cliente);
            mm.addAttribute(RETURN, Uteis.encryptUserData(result));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }



    @RequestMapping(value = "{ctx}/lembrarSenhaProfessor", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap lembrarSenhaProfessor(@PathVariable String ctx,
                                   @RequestParam String userName) {
        ModelMap mm = new ModelMap();
        try {
            Usuario u = usuarioService.obterPorAtributo(ctx, "userName", userName);
            if (u != null && u.isPermiteLoginWeb()) {
                String result = iCadWS.alterarUsuarioMovelColaborador(
                        Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg),
                        ctx, userName,
                        null,
                        u.getProfessor().getCodigoColaborador(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx));

                if (result != null && result.contains("ERRO")) {
                    throw new ServiceException(result);
                }
                mm.addAttribute(RETURN, result);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    @RequestMapping(value = "{ctx}/lembrarSenhaColaborador", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap lembrarSenhaColaborador(@PathVariable String ctx,
                                     @RequestParam String email) {
        ModelMap mm = new ModelMap();
        try {
            Usuario u = usuarioService.obterPorAtributo(ctx, "usuarioEmail.email", email);
            if (u != null && u.isPermiteLoginWeb()) {
                String result = iCadWS.alterarUsuarioMovelColaborador(
                        Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg),
                        ctx, u.getUsuarioEmail().getEmail(),
                        null,
                        u.getProfessor().getCodigoColaborador(), SuperControle.getNomeAppParaEmail(ctx), SuperControle.getAppUrlEmail(ctx));

                if (result != null && result.contains("ERRO")) {
                    throw new ServiceException(result);
                }
                mm.addAttribute(RETURN, result);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    @RequestMapping(value = "{ctx}/consultarCliente", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarCliente(@PathVariable String ctx,
                              @RequestParam String nomeOuMatricula,
                              @RequestParam(required = false) Integer empresaZW, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if ((empresaZW != null) && (empresaZW == 0)) {
                empresaZW = null;
            }
            consultarAlunoEmpresa(mm,ctx,empresaZW,nomeOuMatricula, request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/LsuMjJ52Q719OuRTWlcJuaS8th3y06bsyoyiPG5hJb4", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarClienteCrypt(@PathVariable String ctx, @RequestBody String content, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String nomeOuMatricula = o.optString("nomeOuMatricula");
            Integer empresaZW = o.has("empresaZW") && !o.isNull("empresaZW") ? o.optInt("empresaZW") : null;

            if ((empresaZW != null) && (empresaZW == 0)) {
                empresaZW = null;
            }
            consultarAlunoEmpresa(mm,ctx,empresaZW,nomeOuMatricula, request);

            // Criptografar toda a resposta de sucesso
            JSONObject responseJson = new JSONObject();
            for (String key : mm.keySet()) {
                responseJson.put(key, mm.get(key));
            }
            mm.clear();
            mm.addAttribute(RETURN, Uteis.encryptUserData(responseJson.toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private void consultarAlunoEmpresa(ModelMap mm,String ctx,Integer empresaZW , String nomeOuMatricula, HttpServletRequest request) throws Exception{
        List<ClienteSintetico> clienteSinteticos =
                clienteSinteticoService.consultarPorMatriculaOuNome(ctx, empresaZW, null, nomeOuMatricula, 50);
        if (clienteSinteticos == null) {
            throw new ServiceException(getViewUtils().getMensagem("tabela.semregistros"));
        }
        List<ClienteJSON> clienteJSONS = popularClientesJSON(ctx, clienteSinteticos, request);
        clienteJSONS.addAll(prescricaoService.consultarProfessorTreinoPorNome(ctx, nomeOuMatricula));
        try {
            RedeEmpresaVO redeEmpresaVO = oamdService.obterRedePorChave(ctx);
            if(redeEmpresaVO != null
                    && redeEmpresaVO.getGestaoRedes()
            ){
                clienteJSONS.addAll(clienteRedeEmpresaService.consultarPorMatriculaOuNome(ctx, redeEmpresaVO, nomeOuMatricula));
            }
        }catch (Exception e){
            Uteis.logar(e, UsuarioJSONControle.class);
        }
        mm.addAttribute(
                "clientes",
                Ordenacao.ordenarLista(clienteJSONS, "nome"));
    }

    @RequestMapping(value = "{ctx}/consultarClienteEmpresa", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarClienteEmpresa(@PathVariable String ctx,
                                     @RequestParam String nomeOuMatricula,
                                     @RequestParam Integer empresaZW,
                                     HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            consultarAlunoEmpresa(mm,ctx,UteisValidacao.emptyNumber(empresaZW) ? null : empresaZW,nomeOuMatricula,request);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/consultarClientesProfessor", method = RequestMethod.POST)
    public ModelMap consultarClientesProfessor(@PathVariable String contexto,
                                               @RequestParam final String userName,
                                               @RequestParam final Integer statusAluno,
                                               @RequestParam(required = false) Integer index,
                                               @RequestParam(value = "max", required = false) Integer maxResult,
                                               HttpServletRequest request) {

        ModelMap modelMap = new ModelMap();
        try {

            final StatusClienteEnum status = StatusClienteEnum.getFromId(statusAluno);

            final Usuario usuario = usuarioService.obterPorAtributo(contexto, "username", userName);
            if (usuario == null) {
                return reportarErro(modelMap, "mobile.usuarioinvalido");
            }

            final List<ClienteSintetico> clientesSinteticos =
                    clienteSinteticoService.filtrarStatus(contexto, usuario, status, null, maxResult == null ? 0: maxResult, index == null ? 0 : index);

            if (clientesSinteticos == null) {
                return reportarErro(modelMap, "tabela.semregistros");
            }

            modelMap.addAttribute(
                    "clientes",
                    Ordenacao.ordenarLista(popularClientesJSON(contexto, clientesSinteticos,request), "nome"));

        } catch (Exception ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return modelMap;
    }

    private List<ClienteJSON> popularClientesJSON(String contexto, List<ClienteSintetico> clientesSinteticos, HttpServletRequest request) throws Exception {

        List<ClienteJSON> clientesJson = new ArrayList<ClienteJSON>();
        for (ClienteSintetico cliente : clientesSinteticos) {
            ClienteJSON clienteJSON = new ClienteJSON(cliente);
            Usuario usuario = usuarioService.obterPorAtributo(contexto, "cliente.codigo", cliente.getCodigo());
            Empresa empresa = empresaService.obterPorIdZW(contexto, cliente.getEmpresa());

            if (empresa != null) {
                clienteJSON.setNomeEmpresa(empresa.getNome());
            }

            if (usuario == null) {
                clienteJSON.setUserName(cliente.getMatriculaString());
            } else {
                clienteJSON.setUserName(usuario.getUserName());
            }
            String urlFotoPessoa = fotoService.defineURLFotoPessoa(request, cliente.getPessoa().getFotoKey(), cliente.getCodigoPessoa(), false, contexto, !SuperControle.independente(contexto));
            clienteJSON.setUrlFoto(!urlFotoPessoa.contains("fotoPadrao.jpg") ? urlFotoPessoa : "https://cdn1.pactorian.net/fotoPadrao.jpg");

            clientesJson.add(clienteJSON);
        }

        return clientesJson;
    }

    @RequestMapping(value = "{ctx}/consultarClienteIniciaCom", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap consultarClienteIniciaCom(@PathVariable String ctx,
                                       @RequestParam String nomeOuMatricula) {
        ModelMap mm = new ModelMap();
        try {
            List<ClienteSintetico> l = clienteSinteticoService.consultarPorMatriculaOuNome(ctx, null, null, nomeOuMatricula, 0);
            List<ClienteResumidoJSON> jsonArray = new ArrayList<ClienteResumidoJSON>();
            if (l != null) {
                for (ClienteSintetico c : l) {
                    ClienteResumidoJSON json = new ClienteResumidoJSON();
                    Usuario u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", c.getCodigo());
                    if (u != null) {
                        json.setUserName(u.getUserName());
                    }
                    jsonArray.add(json);
                }
                jsonArray = Ordenacao.ordenarLista(jsonArray, "userName");
                mm.addAttribute("clientes", jsonArray);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("tabela.semregistros"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/status", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap status(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(STATUS, Calendario.getData(Calendario.MASC_DATAHORA));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/gethealth", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap status() {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(STATUS, "PactoTreino_" + Calendario.getData(Calendario.MASC_DATAHORA));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/versao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterVersaoProgramaAtual(@PathVariable String ctx, @RequestParam String username) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorAtributo(ctx, "username", username);
            if (usuario != null) {
                UsuarioVersaoJSON usuarioVersaoJSON = preencherVersaoUsuarioJSON(usuario, ctx);
                mm.addAttribute("usuario", usuarioVersaoJSON);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/trocarFoto", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap trocarFoto(@PathVariable String ctx, @RequestParam String codigo, @RequestParam String codigoEmpresaZW, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            professorSinteticoService.atualizarFotoProfessor(ctx, Integer.valueOf(codigo), Integer.valueOf(codigoEmpresaZW), request, getContext());
            Propagador.propagar(request);
            mm.addAttribute(STATUS, "Foto trocada com sucesso!");
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/obterUrlRedirect", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterUrlRedirect(@PathVariable String ctx,
                              @RequestParam String userName, HttpServletRequest request) {
        ModelMap mm = new ModelMap();

        try {
            Usuario u = usuarioService.obterPorAtributo(ctx,"userName", userName);
            if(u == null){
                throw new Exception("Usuário inválido!");
            }
            u.setChave(ctx);
            UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(ctx, u));
            uJSON.montarUrlRedirect(u,request.getRequestURL().substring(0,request.getRequestURL().indexOf("/prest")));
            JSONObject obj = new JSONObject();
            obj.put("urlRedirectAdm",uJSON.getUrlRedirectAdm());
            obj.put("urlRedirectCRM",uJSON.getUrlRedirectCRM());
            obj.put("urlRedirectTreino",uJSON.getUrlRedirectTreino());
            obj.put("urlRedirectFinan",uJSON.getUrlRedirectFinan());
            obj.put("urlRedirectRankingUCP",uJSON.getUrlRedirectRankingUCP());
            mm.addAttribute(RETURN, obj.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }
    public static UsuarioVersaoJSON preencherVersaoUsuarioJSON(Usuario usuario,
                                                               final String ctx) throws ServiceException {
        UsuarioVersaoJSON usuarioVersaoJSON = new UsuarioVersaoJSON();
        try {
            usuarioVersaoJSON.setCodUsuario(usuario.getCodigo());
            usuarioVersaoJSON.setUserName(usuario.getUserName());
            usuarioVersaoJSON.setVersao(usuario.getCliente().getVersao());

        } catch (Exception e) {
            if (e.getClass() == NullPointerException.class) {
                e.printStackTrace();
            }
            throw new ServiceException(e);
        }
        return usuarioVersaoJSON;
    }

    @RequestMapping(value = "{ctx}/ultimosTreinos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap proximosTreinos(@PathVariable String ctx, @RequestParam String userName) {
        ModelMap mm = new ModelMap();
        try {
            Usuario u = usuarioService.obterPorAtributo(ctx,"userName", userName);
            if(u == null){
                throw new Exception("Usuário inválido!");
            }
            ProgramaTreinoService pts = (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
            List<TreinoRealizado> treinoRealizados = pts.obterUltimosTreinosRealizadosCliente(ctx, u.getCliente().getMatricula(), 3);
            List<ItemJSON> jsonArray = new ArrayList<ItemJSON>();
            for(TreinoRealizado t : treinoRealizados){
                jsonArray.add(new ItemJSON(t.getProgramaTreinoFicha().getNomeFicha(), t.getProfessor().getNome(),
                        Uteis.getDataAplicandoFormatacao(t.getDataInicio(), "dd/MM/yyyy HH:mm"), ""));
            }
            mm.addAttribute("ultimosTreinos", jsonArray);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ranking", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ranking(@PathVariable String ctx, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            WodService ws = (WodService) UtilContext.getBean(WodService.class);
            List<Wod> wods = ws.wods(ctx, Calendario.hoje(), Calendario.hoje(), null, null, null);
            if(UteisValidacao.emptyList(wods)){
                mm.addAttribute(RETURN, new ArrayList<ItemJSON>());
            }else{
                List<RankingJSON> scores = ws.ranking(ctx, wods.get(0).getCodigo(), request, 3);
                mm.addAttribute(RETURN, scores);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(CrossfitJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/proximasAulas", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap proximasAulas(@PathVariable String ctx,
                           @RequestParam String userName) {
        ModelMap mm = new ModelMap();

        try {
            Usuario u = usuarioService.obterPorAtributo(ctx,"userName", userName);
            if(u == null){
                throw new Exception("Usuário inválido!");
            }
            AgendaTotalService agendaService = (AgendaTotalService) UtilContext.getBean(AgendaTotalService.class);
            String retorno = agendaService.obterProximasAulas(ctx, u.getCliente().getMatricula(),false);

            if(retorno.startsWith("ERRO:")){
                mm.addAttribute(STATUS_ERRO, retorno.replaceAll("ERRO:", ""));
            } else {
                List<AgendaTotalJSON> proximasAulas = JSONMapper.getList(new JSONArray(retorno), AgendaTotalJSON.class);
                if (proximasAulas == null) {
                    proximasAulas = new ArrayList<AgendaTotalJSON>();
                }
                List<ItemJSON> jsonArray = new ArrayList<ItemJSON>();
                for(AgendaTotalJSON aula : proximasAulas) {
                    if(!aula.getAulaCheia()){
                        jsonArray.add(new ItemJSON(aula.getTitulo(), aula.getResponsavel(), aula.getInicio(), ""));
                    }
                }

                AulaService aulaService = (AulaService) UtilContext.getBean(AulaService.class);
                List<AgendaTotalJSON> aulas = aulaService.obterAulasAluno(ctx, u.getCliente().getMatricula());
                for (AgendaTotalJSON aula : aulas) {
                    if(Calendario.hoje().before(aula.getDia())){
                        jsonArray.add(new ItemJSON(aula.getTitulo(), aula.getResponsavel(), aula.getInicio(), ""));
                    }
                }
                mm.addAttribute("aulas", jsonArray);
            }

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/loginRedeSocial", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioRedeSocial(@PathVariable String ctx,
                                      @RequestParam String email,
                                      HttpServletRequest request,
                                      @RequestParam(required = false, defaultValue = "false") Boolean aluno) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, obterUsuario(request, ctx, email.trim(), aluno));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return mm;
    }

    @RequestMapping(value = "{ctx}/v2/loginRedeSocial", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioRedeSocial(@PathVariable String ctx,
                                      @RequestParam Integer codUsuario,
                                      HttpServletRequest request,
                                      @RequestParam(required = false, defaultValue = "false") Boolean aluno) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, obterUsuarioPorCodigoUsuarioTreino(request, ctx, codUsuario, aluno));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return mm;
    }

    private UsuarioJSON obterUsuarioPorCodigoUsuarioTreino(HttpServletRequest request, String ctx, Integer codUsuario, boolean aluno) throws Exception {
        Usuario u = usuarioService.consultaPorId(ctx, codUsuario);
        if(u == null){
            throw new Exception("Não foi possível encontrar o usuário!");
        }
        u = usuarioService.prepararUsuario(u, ctx, "");
//        if(aluno){
//            if(u.getCliente() == null){
//                throw new Exception("O usuário informado não é de um aluno!");
//            }
//        } else {
//            if(u.getCliente() != null){
//                throw new Exception("O usuário informado é de um aluno!");
//            }
//        }
        return prepararaUsuarioApp(request, ctx, u);
    }

    private UsuarioJSON obterUsuario(HttpServletRequest request, String ctx, String email, boolean aluno) throws Exception {
        Usuario u = null;
        if (UteisValidacao.emptyString(email)) {
            throw new Exception("Email não informado!");
        }
        if (SuperControle.independente(ctx)) {
            List<UsuarioAppBasicoJSON> usuarioAppBasicoJSONS = usuarioService.obterIdentidadeDadosBasicosEmailApp(ctx, email);
            for (UsuarioAppBasicoJSON usuarioAppBasicoJSON : usuarioAppBasicoJSONS) {
                if (usuarioAppBasicoJSON.getCodigocliente() != null && usuarioAppBasicoJSON.getCodigocliente() != 0 && aluno) {
                    u = usuarioService.obterPorAtributo(ctx, "cliente.codigo", usuarioAppBasicoJSON.getCodigocliente());
                    break;
                } else if (!aluno && usuarioAppBasicoJSON.getCodigocolaborador() != null && usuarioAppBasicoJSON.getCodigocolaborador() != 0) {
                    u = usuarioService.obterPorAtributo(ctx, "professor.codigo", usuarioAppBasicoJSON.getCodigocolaborador());
                    break;
                }
            }
            if (u == null) {
                throw new ServiceException("Usuário não encontrado.");
            }
        } else if (!aluno) {
            try {
                u = usuarioService.validarUsuarioMovelApp(ctx, email, false);
            } catch (Exception ex) {
                u = usuarioService.validarUsuarioMovelApp(ctx, email, true);
                if (u != null && u.getCliente() == null) {
                    throw new ServiceException("Usuário não encontrado.");
                }
            }
        } else {
            try {
                u = usuarioService.validarUsuarioMovelApp(ctx, email, true);
                if (u != null && u.getCliente() == null) {
                    throw new ServiceException("Usuário de aluno não encontrado.");
                }
            } catch (Exception e) {
                if (u == null) {
                    throw new ServiceException("Usuário de aluno não encontrado.");
                }
                throw e;
            }
        }
        UsuarioJSON uJSON = prepararaUsuarioApp(request, ctx, u);
        return uJSON;
    }

    private UsuarioJSON prepararaUsuarioApp(HttpServletRequest request, String ctx, Usuario u) throws Exception {
        boolean usuarioIsCliente = u.getCliente() != null;
        if (usuarioIsCliente) {
            usuarioService.preencheFotoEVersaoFotoApp(ctx, u, false);
            u.setAvatar(u.getFotoKeyApp());
        } else if (UteisValidacao.emptyString(u.getFotoKeyApp())) {
            u.setAvatar(fotoService.defineURLFotoPessoa(request, u.getProfessor().getPessoa().getFotoKey(), u.getProfessor().getCodigoPessoa(), false, ctx, false));
        }
        UsuarioJSON uJSON = new UsuarioJSON(u, usuarioService.empresasApp(ctx, u));
        if (usuarioIsCliente && u.getEmpresaZW() != u.getCliente().getEmpresa()) {
            uJSON.setNomeEmpresa(usuarioService.nomeEmpresaClienteSintetico(ctx, u) != null ? usuarioService.nomeEmpresaClienteSintetico(ctx, u) : uJSON.getNomeEmpresa());
        }
        if (usuarioIsCliente
                && !UteisValidacao.emptyNumber(u.getCliente().getMatricula()) && !SuperControle.independente(ctx)) {
            String result = iCadWS.nomenclaturaVendaCredito(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, u.getCliente().getMatricula().toString());
            JSONObject json = new JSONObject(result);
            uJSON.setNomenclaturaVendaCredito(json.getString("nomenclatura"));
            uJSON.setSaldoCreditos(json.getInt("saldocreditotreino"));
            uJSON.setTotalCreditos(json.getInt("totalcreditotreino"));

            List<UsuarioDependenteDTO> dependentes = usuarioService.obterDependentes(ctx, u);
            uJSON.setDependenteDTOS(dependentes);
        }
        if(usuarioIsCliente && u.getCliente().getDia() != null) {
            uJSON.setDia(u.getCliente().getDia().getTime());
        }
        boolean usuarioIsColaboradorEExisteZW = uJSON.getCodigoColaborador() != null && uJSON.getCodigoColaborador() > 0 && !SuperControle.independente(ctx);
        if(usuarioIsColaboradorEExisteZW) {
            try {
                uJSON.setCodUsuario(usuarioService.consultarPorUsuarioZWAndEmpresaZW(ctx, u.getUsuarioZW(), u.getEmpresaZW()).getCodigo());
            } catch (ServiceException e) {
                throw new ServiceException("Usuário não existe no treino web");
            }
            uJSON.setSrcImg(Aplicacao.obterUrlFotoDaNuvem(usuarioService.urlFotoAppTreino(ctx, uJSON.getCodUsuarioZW())));
        }
        if(UteisValidacao.emptyString(u.getCpf())) {
            if(usuarioIsColaboradorEExisteZW) {
                uJSON.setCpf(usuarioService.consultaCPFColaboradorZW(ctx, u.getProfessor().getCodigoColaborador()));
            } else if(usuarioIsCliente && !UteisValidacao.emptyNumber(u.getCliente().getMatricula())) {
                uJSON.setCpf(UteisValidacao.emptyString(u.getCliente().getCPF()) ?
                        usuarioService.consultaCPFClienteZW(u.getCliente().getMatricula(), ctx) : u.getCliente().getCPF());
            }
        } else {
            uJSON.setCpf(u.getCpf());
        }

        if (SuperControle.independente(ctx)) {
            uJSON.setTreinoIndependente(true);
        } else {
            usuarioService.consultarUsernameEEmailZW(ctx, uJSON);
            uJSON.setTreinoIndependente(false);
        }

        return uJSON;
    }

    @ApiOperation(
            value = "Validar usuário de rede social com dados criptografados",
            notes = "Valida um usuário através de dados de rede social enviados de forma criptografada. " +
                    "Os dados são descriptografados internamente e o resultado é retornado criptografado. " +
                    "Utilizado para autenticação segura de usuários via redes sociais no aplicativo móvel.",
            tags = "Autenticação e Login"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Validação realizada com sucesso", response = ExemploRespostaValidarUsuarioRedeSocialCrypt.class)
    })
    @RequestMapping(value = "{ctx}/fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioRedeSocialCrypt(HttpServletRequest request,
                                          @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia123")
                                          @PathVariable String ctx,
                                          @ApiParam(value = "Dados criptografados contendo informações do usuário para validação. " +
                                                  "Deve conter os campos 'email' (e-mail do usuário) e 'aluno' (true para aluno, false para colaborador). " +
                                                  "Os dados devem estar criptografados usando o algoritmo interno do sistema.",
                                                  required = true,
                                                  defaultValue = "{\"email\":\"<EMAIL>\", \"aluno\":true}")
                                          @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            boolean aluno = o.optBoolean("aluno");
            String email = o.optString("email").trim();
            mm.addAttribute(RETURN, Uteis.encryptUserData(new JSONObject(obterUsuario(request, ctx, email, aluno)).toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Validar usuário de rede social por código com dados criptografados",
            notes = "Valida um usuário através do código do usuário enviado de forma criptografada. " +
                    "Os dados são descriptografados internamente e o resultado é retornado criptografado. " +
                    "Versão alternativa que utiliza o código do usuário ao invés do e-mail para identificação.",
            tags = "Autenticação e Login"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Validação realizada com sucesso", response = ExemploRespostaValidarUsuarioRedeSocialCryptPorCodUsuario.class)
    })
    @RequestMapping(value = "{ctx}/v2/fV7iK6fA1yP8pK0bQ4hI8wW4xY8yP2tU", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap validarUsuarioRedeSocialCryptPorCodUsuario(HttpServletRequest request,
                                                       @ApiParam(value = "Contexto da empresa", required = true, defaultValue = "academia123")
                                                       @PathVariable String ctx,
                                                       @ApiParam(value = "Dados criptografados contendo informações do usuário para validação. " +
                                                               "Deve conter os campos 'codUsuario' (código do usuário no sistema) e 'aluno' (true para aluno, false para colaborador). " +
                                                               "Os dados devem estar criptografados usando o algoritmo interno do sistema.",
                                                               required = true,
                                                               defaultValue = "{\"codUsuario\":12345, \"aluno\":true}")
                                                       @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            boolean aluno = o.optBoolean("aluno");
            Integer codUsuario = o.optInt("codUsuario");
            mm.addAttribute(RETURN, Uteis.encryptUserData(new JSONObject(obterUsuarioPorCodigoUsuarioTreino(request, ctx, codUsuario, aluno)).toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/historicoPontosAluno", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap historicoPontosAluno(@PathVariable String ctx,
                                  @RequestParam String matricula,
                                  @RequestParam boolean analitico) {
        ModelMap mm = new ModelMap();
        try {
            List<PontosAlunosTO> listaPontos = new ArrayList<PontosAlunosTO>();
            String result = iCadWS.listaAlunoPontosApp(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, matricula,analitico);
            JSONArray jsonArray = new JSONArray(result);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            if (!analitico) {
                PontosAlunosTO pontosAlunoSintetico = new PontosAlunosTO();
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jObj = jsonArray.optJSONObject(i);
                    pontosAlunoSintetico.setCodigoCliente(jObj.getInt("codigocliente"));
                    pontosAlunoSintetico.setTotalPontos(jObj.getInt("totalpontos"));
                    pontosAlunoSintetico.setNomeAluno(jObj.getString("nomecliente"));
                    listaPontos.add(pontosAlunoSintetico);
                }
                mm.addAttribute(RETURN, listaPontos);
            }else{
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jObj = jsonArray.optJSONObject(i);
                    PontosAlunosTO pontosAluno = new PontosAlunosTO();
                    pontosAluno.setCodigoCliente(jObj.getInt("codigocliente"));
                    pontosAluno.setNomeAluno(jObj.getString("nomecliente"));
                    pontosAluno.setDescricao(jObj.getString("descricao"));
                    pontosAluno.setCodigoHistorico(jObj.getInt("codigohistorico"));
                    pontosAluno.setPontos(jObj.getInt("pontos"));
                    pontosAluno.setDataConfirmacao(sdf.parse(jObj.getString("dataconfirmacao")));
                    pontosAluno.setDataAula(sdf.parse(jObj.getString("dataaula")));
                    pontosAluno.setNomeBrinde(jObj.getString("nomebrinde"));
                    pontosAluno.setOperacao(jObj.getString("operacao"));
                    pontosAluno.setDataAulaString(Uteis.getData(pontosAluno.getDataAula()));
                    pontosAluno.setDataConfirmacaoString(Uteis.getData(pontosAluno.getDataConfirmacao()));
                    listaPontos.add(pontosAluno);
                }
                mm.addAttribute(RETURN, listaPontos);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{key}/alterarFotoUsuarioAPP", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap alterarFotoUsuarioAPP(@PathVariable final String key,
                                   @RequestParam(required = false) Integer usuario,
                                   @RequestParam(required = false) String matricula,
                                   @RequestParam(required = false, defaultValue = "false") Boolean atualizaFotoZW,
                                   @RequestBody ImagemUsuarioDTO imagemUsuarioDTO) {
        ModelMap mm = new ModelMap();
        try {
            Usuario obj = null;
            boolean usuarioColaborador = usuario != null && usuario != 0;
            boolean usuarioCliente = !UteisValidacao.emptyString(matricula);
            if (usuarioColaborador) {
                obj = usuarioService.obterPorId(key, usuario);
                usuarioService.preencheFotoEVersaoFotoAppColaborador(key, obj);
            }
            if (usuarioCliente) {
                try {
                    Integer codMatricula = Integer.parseInt(matricula);
                    obj = usuarioService.consultarPorMatricula(key, codMatricula);
                    usuarioService.preencheFotoEVersaoFotoApp(key, obj, true);
                } catch (Exception e) {
                    throw new Exception("Erro na consulta do usuário a partir da matrícula!");
                }
            }

            if (obj == null) {
                String tipoUsuario = usuarioColaborador ? "colaborador" : "cliente";
                throw new Exception("Usuário " + tipoUsuario + " não encontrado!");
            } else {
                String timeStamp = "?time=" + System.currentTimeMillis();
                String fotoKeyApp = MidiaService.getInstanceWood().uploadObjectFromByteArray(key, MidiaEntidadeEnum.FOTO_USUARIO_APP,
                        MidiaEntidadeEnum.FOTO_USUARIO_APP.getNomeCampo() + "_" + obj.getCodigo() + timeStamp, imagemUsuarioDTO.getImagem());
                Integer versaoAntiga = obj.getVersaoFotoApp();
                obj.setVersaoFotoApp(obj.getVersaoFotoApp() + 1);
                String url = Aplicacao.obterUrlFotoDaNuvem(fotoKeyApp) + "?v=" + obj.getVersaoFotoApp();

                fotoKeyApp = (fotoKeyApp  + "?v=" + obj.getVersaoFotoApp());
                obj.setFotoKeyApp(fotoKeyApp);

                if (usuarioColaborador) {
                    usuarioService.alterarFotoUsuarioColaborador(key, fotoKeyApp, obj.getVersaoFotoApp(), obj.getUsuarioZW(), atualizaFotoZW);
                }
                if (usuarioCliente) {
                    usuarioService.alterarFotoCliente(key, fotoKeyApp, obj.getVersaoFotoApp(), obj.getCodigo(), matricula, atualizaFotoZW);
                }

                if(obj.getFotoKeyApp().contains("v=" + versaoAntiga)) {
                    throw new Exception("Não foi possível atualizar a foto em questão. Por favor, tente novamente.");
                }
                mm.addAttribute(RETURN, url);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @Deprecated
    private String alterarFotoUsuarioAppOAMD(String chave, String email, String url) {
        try {
            ExecuteRequestHttpService req = new ExecuteRequestHttpService();
            req.timeout = 15000;
            Map<String, String> header = new HashMap<String, String>();
            header.put("Content-Type", "application/x-www-form-urlencoded");
            Map<String, String> parametros = new HashMap<String, String>();
            parametros.put("email", email);
            parametros.put("url", url);
            String retorno = req.executeHttpRequestGenerico(Aplicacao.getProp(Aplicacao.urlOAMD) + "/prest/usuarioapp/" + chave + "/alterarFotoUsuarioAPP", header, parametros, ExecuteRequestHttpService.METODO_POST, "iso-8859-1", "iso-8859-1");
            JSONObject jsonObject = new JSONObject(retorno);
            if (jsonObject.has(RETURN)) {
                return jsonObject.getString(RETURN);
            } else {
                throw new Exception(jsonObject.getString(STATUS_ERRO));
            }
        } catch (Exception e) {
            return "ERRO: " + e.getMessage();
        }
    }

    @RequestMapping(value = "{ctx}/infoUsuario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap infoUsuario(@PathVariable String ctx,
                         @RequestParam Integer codUsuario,
                         HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, codUsuario);
            if (usuario == null) {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
            UsuarioVersaoJSON usuarioVersaoJSON = new UsuarioVersaoJSON();
            usuarioVersaoJSON.setCodUsuario(usuario.getCodigo());
            usuarioVersaoJSON.setUserName(usuario.getUserName());
            if (UteisValidacao.emptyString(usuario.getFotoKeyApp())) {
                usuarioVersaoJSON.setFotoAppUsuario(defineUrlFotoJSON(ctx, usuario.getIdPessoa(), request));
            } else {
                usuarioVersaoJSON.setFotoAppUsuario(usuario.getFotoKeyApp());
            }
            usuarioVersaoJSON.setVersaoFotoApp(usuario.getVersaoFotoApp());
            mm.addAttribute(RETURN, usuarioVersaoJSON);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/nu", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroColaborador(
            @PathVariable String ctx,
            @RequestBody ColaboradorTO colaboradorTO,
            @RequestParam String nomeEmpresa,
            HttpServletRequest request){
        try {
            empresaService.inicializarEmpresa(ctx, nomeEmpresa, colaboradorTO, request);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar o colaborador", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @RequestMapping(value = "{ctx}/obterPontosPorCliente", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterPontosPorCliente(@PathVariable String ctx,
                                   @RequestParam Integer cliente) {
        ModelMap mm = new ModelMap();
        String result;
        try {
            result = iCadWS.obterPontosPorCliente(Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg), ctx, cliente);

            if (result != null && result.contains("ERRO")) {
                throw new ServiceException(result);
            }
            mm.addAttribute("ponto", result);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarAlunosColaborador", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarAlunosColaboradorApp(@PathVariable String ctx,
                                   @RequestParam String userName, @RequestParam Integer statusAluno,
                                           @RequestParam Integer empresa, @RequestParam(required = false) String porNome,
                                           @RequestParam(required = false) Integer index,
                                           @RequestParam(value = "max", required = false) Integer maxResult,
                                           HttpServletRequest request){
    ModelMap mm = new ModelMap();
        try {
//            Date start = new Date();
            final StatusClienteEnum status = StatusClienteEnum.getFromId(statusAluno);
            final Usuario usuario = usuarioService.consultarColaboradorPorUsername(ctx,  userName);
            if (usuario == null) {
                return reportarErro(mm, "mobile.usuarioinvalido");
            }
//            Date end = new Date();
//            System.out.println("consulta de usuario: " + ((end.getTime() - start.getTime())) + " milisegundos" );
//            start = new Date();
            maxResult = maxResult == null || maxResult > 100 ? 100 : maxResult;
            final List<ClienteSintetico> clientesSinteticos =
                    clienteSinteticoService.filtrarStatusApp(ctx, usuario, status, porNome, maxResult, index == null ? 0 : index, empresa);

            if (clientesSinteticos == null) {
                return reportarErro(mm, "tabela.semregistros");
            }
//            end = new Date();
//            System.out.println("consulta de clientes: " + ((end.getTime() - start.getTime())) + " milisegundos" );
//            start = new Date();
            Empresa empresaEntity = empresaService.obterPorIdZW(ctx, empresa);
            Map<Integer, String> usernamesClientes = usuarioService.usernamesClientes(ctx, clientesSinteticos);
            List<ClienteAppJSON> clientesJson = new ArrayList<ClienteAppJSON>();
            int i = 0;
            for (ClienteSintetico cliente : clientesSinteticos) {
//                Date startCli = new Date();
                ClienteAppJSON clienteJSON = new ClienteAppJSON(cliente);
                if (empresaEntity != null) {
                    clienteJSON.setNomeEmpresa(empresaEntity.getNome());
                }
//                Date endCli = new Date();
                i++;
//                System.out.println(i + " consulta usuario cliente: " + ((endCli.getTime() - startCli.getTime())) + " milisegundos" );
//                startCli = new Date();
                if (usernamesClientes != null) {
                    clienteJSON.setUserName(usernamesClientes.get(cliente.getCodigo()));
                }
                clienteJSON.setUrlFoto(fotoService.defineURLFotoPessoa(request, cliente.getPessoa().getFotoKey(),
                        cliente.getCodigoPessoa(), false, ctx, false));
                clientesJson.add(clienteJSON);
//                endCli = new Date();
//                System.out.println(i + " montagem de foto: " + ((endCli.getTime() - startCli.getTime())) + " milisegundos" );
            }
//            end = new Date();
//            System.out.println("montagem de alunos: " + ((end.getTime() - start.getTime())) + " milisegundos" );
            mm.addAttribute("sucesso", Ordenacao.ordenarLista(clientesJson, "nome"));

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    //consulta por código de usuario
    @RequestMapping(value = "{ctx}/app/v2/consultarAlunosColaborador", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarAlunosColaboradorApp(@PathVariable String ctx,
                                           @RequestParam Integer codUsuario, @RequestParam Integer statusAluno,
                                           @RequestParam Integer empresa, @RequestParam(required = false) String porNome,
                                           @RequestParam(required = false) Integer index,
                                           @RequestParam(value = "max", required = false) Integer maxResult,
                                           HttpServletRequest request){
        ModelMap mm = new ModelMap();
        try {
//            Date start = new Date();
            final StatusClienteEnum status = StatusClienteEnum.getFromId(statusAluno);
            final Usuario usuario = usuarioService.consultaPorId(ctx,  codUsuario);
            if (usuario == null || usuario.getProfessor() == null || UteisValidacao.emptyNumber(usuario.getProfessor().getCodigo())) {
                return reportarErro(mm, "mobile.usuarioinvalido");
            }
//            Date end = new Date();
//            System.out.println("consulta de usuario: " + ((end.getTime() - start.getTime())) + " milisegundos" );
//            start = new Date();
            maxResult = maxResult == null || maxResult > 100 ? 100 : maxResult;
            final List<ClienteSintetico> clientesSinteticos =
                    clienteSinteticoService.filtrarStatusApp(ctx, usuario, status, porNome, maxResult, index == null ? 0 : index, empresa);

            if (clientesSinteticos == null) {
                return reportarErro(mm, "tabela.semregistros");
            }
//            end = new Date();
//            System.out.println("consulta de clientes: " + ((end.getTime() - start.getTime())) + " milisegundos" );
//            start = new Date();
            Empresa empresaEntity = empresaService.obterPorIdZW(ctx, empresa);
            Map<Integer, String> usernamesClientes = usuarioService.usernamesClientes(ctx, clientesSinteticos);
            List<ClienteAppJSON> clientesJson = new ArrayList<ClienteAppJSON>();
            int i = 0;
            for (ClienteSintetico cliente : clientesSinteticos) {
//                Date startCli = new Date();
                ClienteAppJSON clienteJSON = new ClienteAppJSON(cliente);
                if (empresaEntity != null) {
                    clienteJSON.setNomeEmpresa(empresaEntity.getNome());
                }
//                Date endCli = new Date();
                i++;
//                System.out.println(i + " consulta usuario cliente: " + ((endCli.getTime() - startCli.getTime())) + " milisegundos" );
//                startCli = new Date();
                if (usernamesClientes != null) {
                    clienteJSON.setUserName(usernamesClientes.get(cliente.getCodigo()));
                }
                clienteJSON.setUrlFoto(fotoService.defineURLFotoPessoa(request, cliente.getPessoa().getFotoKey(),
                        cliente.getCodigoPessoa(), false, ctx, false));
                clientesJson.add(clienteJSON);
//                endCli = new Date();
//                System.out.println(i + " montagem de foto: " + ((endCli.getTime() - startCli.getTime())) + " milisegundos" );
            }
//            end = new Date();
//            System.out.println("montagem de alunos: " + ((end.getTime() - start.getTime())) + " milisegundos" );
            mm.addAttribute("sucesso", Ordenacao.ordenarLista(clientesJson, "nome"));

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;

    }

    @RequestMapping(value = "{ctx}/atualizarAcessoZW", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    ModelMap atualizarAcessoZW(@PathVariable String ctx,
                               @RequestParam Integer codUsuario) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, codUsuario);
            if (usuario == null) {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
            String retorno = usuarioService.registrarZillyonWebLoginTreinoWeb(ctx, usuario);
            mm.addAttribute(RETURN, retorno);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/obterIdentidadeDadosBasicosCelular", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap obterIdentidadeDadosBasicosCelularApp(@PathVariable String ctx, @RequestParam String ddi,
                                           String ddd, String telefone){

        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute("sucesso", usuarioService.obterIdentidadeDadosBasicosCelularApp(ctx, ddi, ddd, telefone));
        }
        catch (Exception ex){
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/obterIdentidadeDadosBasicosEmail", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap obterIdentidadeDadosBasicosCelularApp(@PathVariable String ctx, @RequestParam String email){

        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute("sucesso", usuarioService.obterIdentidadeDadosBasicosEmailApp(ctx, email));
        }
        catch (Exception ex){
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/dash", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap dash(@PathVariable String ctx){

        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, biTreinoService.obterDash(ctx));
        }
        catch (Exception ex){
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/gerarUsuarioTreinoIndependente", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap gerarUsuarioTreinoIndepentendeApp(@PathVariable String ctx,
                                                   @RequestParam(required = false) Integer codigoColaborador,
                                                   @RequestParam(required = false) Integer codigoCliente,
                                                   @RequestParam String email, @RequestParam  String senha){
        ModelMap mm = new ModelMap();
        try {
            if(!UteisValidacao.emptyNumber(codigoColaborador)) {
                Usuario usuario = usuarioService.obterUsuarioPorColaborador(ctx, codigoColaborador);
                if (usuario != null) {
                    throw new ServiceException("Usuario já cadastrado");
                }
                ProfessorSintetico professor = professorSinteticoService.obterPorId(ctx, codigoColaborador);
                usuario = usuarioService.gerarUsuarioApp(ctx, email, senha, professor, null, TipoUsuarioEnum.PROFESSOR);
            }
            else{
                if(!UteisValidacao.emptyNumber(codigoCliente)){
                    Usuario usuario = usuarioService.consultarPorCliente(ctx, codigoCliente);
                    if (usuario != null){
                        usuario.setUserName(email);
                        usuario.setSenha(Uteis.encriptar(senha));
                        usuarioService.alterar(ctx, usuario);
                    }
                    else{
                        ClienteSintetico cliente = clienteSinteticoService.obterPorCodigoCliente(ctx, codigoCliente);
                        usuario = usuarioService.gerarUsuarioApp(ctx, email, senha, null, cliente, TipoUsuarioEnum.ALUNO);
                    }
                }else{
                    throw new ServiceException("O códico de cliente ou colaborador deve ser informado");
                }
            }
            mm.addAttribute("sucesso", "sucesso");
        }
        catch (Exception ex){
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/find", method = RequestMethod.GET)
    public @ResponseBody
    String findUsuario(@PathVariable String ctx, @RequestParam Integer id){
        try {
            Usuario usuario = usuarioService.obterPorId(ctx, id);
            return new UsuarioBaseJSON(usuario).toJSON();
        } catch (ServiceException e) {
            Logger.getLogger(ColaboradorController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar o colaborador", e);
            return e.getMessage();
        }
    }

    @RequestMapping(value = "{ctx}/alterarPerfil", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap statusAlterar(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            perfilService.restaurarPerfilCoordenador(ctx);
            return  mm.addAttribute("sucesso", "Restauradas Permissões para perfil Coordenador");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/corrigir-nome-usuario", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap corrigirNomeUsuarioPacto(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            return mm.addAttribute(RETURN, perfilService.corrigirNomeUsuarioPacto(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/find-by-usuariozw/{usuariozw}", method = RequestMethod.GET)
    public @ResponseBody ModelMap findByUsuarioZw(@PathVariable String ctx,
                                                  @PathVariable Integer usuariozw,
                                                  @RequestParam(required = false) Integer empresazw){
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.consultarPorUsuarioZWAndEmpresaZW(ctx, usuariozw, empresazw);
            if (usuario == null) {
                throw new ServiceException("Usuario não encontrado com o usuariozw " + usuariozw);
            }
            mm.addAttribute(RETURN, new UsuarioBaseJSON(usuario));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, "Erro ao obter usuário por usuariozw", e);
            mm.addAttribute(STATUS_ERRO, e.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/find-by-codigoexterno/{codigoExterno}", method = RequestMethod.GET)
    public @ResponseBody ModelMap findByCodigoExterno(@PathVariable String ctx,
                                                      @PathVariable String codigoExterno,
                                                      @RequestParam(required = false) Integer empresazw){
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = usuarioService.consultarPorCodigoExternoAndEmpresaZW(ctx, codigoExterno, empresazw);
            if (usuario == null) {
                throw new ServiceException("Usuario não encontrado com o codigoExterno " + codigoExterno);
            }
            mm.addAttribute(RETURN, new UsuarioBaseJSON(usuario));
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, "Erro ao obter usuário por codigo externo", e);
            mm.addAttribute(STATUS_ERRO, e.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/senha", method = RequestMethod.POST)
    public ResponseEntity<EnvelopeRespostaDTO> senhaAlterar(@PathVariable String ctx,
                                               @RequestBody AlterarSenhaDTO dto) {
        try {
            Usuario obj = null;
            if (!UteisValidacao.emptyNumber(dto.getUsuario_zw())) {
                obj = usuarioService.obterPorId(ctx, dto.getUsuario_zw());
            }

            if (obj == null) {
                throw new ServiceException("Usuário não encontrado");
            }

            String senhaCripto = dto.getSenha_cripto();
            if (UteisValidacao.emptyString(senhaCripto)) {
                if (UteisValidacao.emptyString(dto.getSenha())) {
                    throw new ServiceException("Senha não informada");
                }
                senhaCripto = Uteis.encriptar(dto.getSenha().toUpperCase());
            }
            obj.setSenha(senhaCripto);
            usuarioService.alterar(ctx, obj);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, "Erro ao alterar senha usuário", e);
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @RequestMapping(value = "{ctx}/registrar-uso-app", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap registrarUsoApp(@PathVariable String ctx, @RequestBody List<UsuarioAppDTO> usuariosAppDTOS) {
        ModelMap mm = new ModelMap();
        try {
            int countSucesso = 0;
            JSONArray falhas = new JSONArray();
            for (UsuarioAppDTO usuarioAppDTO: usuariosAppDTOS) {
                try {
                    usuarioService.registrarUsoApp(ctx, usuarioAppDTO);
                    countSucesso++;
                    mm.addAttribute(RETURN, STATUS_SUCESSO);
                } catch (Exception e) {
                    JSONObject json = new JSONObject();
                    json.put("idClienteApp", usuarioAppDTO.getIdClienteApp());
                    json.put("userName", usuarioAppDTO.getUserName());
                    json.put("motivoFalha", e.getMessage());
                    falhas.put(json);
                    mm.addAttribute(STATUS_ERRO, e.getMessage());
                }
            }
            mm.addAttribute("qtd_sucesso:", countSucesso);
            mm.addAttribute("qtd_falha:", falhas.length());
            mm.addAttribute("falhas:", falhas.toString());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(UsuarioJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/alterar-perfil-usuario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public @ResponseBody ModelMap alterarPerfilUsuario(@PathVariable String ctx,
                                                       @RequestParam("usuarioZW") Integer usuarioZW,
                                                       @RequestParam("perfil_codigo") Integer perfilCodigo) {
        ModelMap mm = new ModelMap();
        try {
            usuarioService.alterarPerfilUsuario(ctx, usuarioZW, perfilCodigo);
            mm.addAttribute("status", "Perfil do usuario alterado com sucesso");
        } catch (Exception e) {
            mm.addAttribute("status", "erro");
            mm.addAttribute("mensagem", e.getMessage());
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/alterar-tipotw", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public @ResponseBody ModelMap alterarTipoTw(@PathVariable String ctx,
                                                @RequestParam("usuarioZW") Integer usuarioZW,
                                                @RequestParam("tipoTw") Integer tipoTw) {
        ModelMap mm = new ModelMap();
        try {
            usuarioService.alterarTipoTw(ctx, usuarioZW, tipoTw);
            mm.addAttribute("status", "TipoTW do usuario alterado com sucesso");
        } catch (Exception e) {
            mm.addAttribute("status", "erro");
            mm.addAttribute("mensagem", e.getMessage());
        }
        return mm;
    }

    //retorna codigo de usuario do treino
    @RequestMapping(value = "{ctx}/app/codigoUsuarioTreino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public @ResponseBody ModelMap codigoUsuarioTreino(@PathVariable String ctx,
                                                      @RequestParam(required = false) Integer codigoColaborador,
                                                      @RequestParam(required = false) Integer matricula) {
        ModelMap mm = new ModelMap();
        try {
            if(codigoColaborador != null && matricula != null) {
                throw new ServiceException("Somente um dos dois parâmetros deve ser informado");
            }
            if(codigoColaborador == null && matricula == null) {
                throw new ServiceException("O códico de colaborador ou matrícula deve ser informado");
            }
            Integer codUsuario = null;
            if(codigoColaborador != null) {
                Usuario usuario = usuarioService.consultarPorCodigoColaborador(ctx, codigoColaborador);
                if (usuario != null) {
                    codUsuario = usuario.getCodigo();
                }
            }
            if(matricula != null) {
                Usuario usuario = usuarioService.consultarPorMatricula(ctx, matricula);
                if (usuario != null) {
                    codUsuario = usuario.getCodigo();
                }
            }
            mm.addAttribute("codigoUsuario", codUsuario);
        } catch (Exception e) {
            mm.addAttribute(STATUS_ERRO, e.getMessage());
        }
        return mm;
    }

}
