package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.empresa.Empresa;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by j<PERSON><PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações básicas da empresa/academia")
public class EmpresaBasicaResponseTO {

    @ApiModelProperty(value = "Código único identificador da empresa", example = "12")
    private Integer id;
    @ApiModelProperty(value = "Nome da empresa/academia", example = "Academia Pacto Fitness")
    private String nome;

    public EmpresaBasicaResponseTO(Empresa empresa) {
        this.id = empresa.getCodigo();
        this.nome = empresa.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

}
