package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Saldo de aulas coletivas disponíveis para um aluno")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaldoAulaColetivaVO {

    @ApiModelProperty(value = "Número de aulas coletivas disponíveis para agendamento", example = "5")
    private Integer disponiveis;

    @ApiModelProperty(value = "Número de aulas coletivas já utilizadas pelo aluno", example = "12")
    private Integer utilizadas;

    @ApiModelProperty(value = "Número de aulas coletivas que expiraram sem uso", example = "2")
    private Integer expiradas;

    public Integer getDisponiveis() {
        return disponiveis;
    }

    public void setDisponiveis(Integer disponiveis) {
        this.disponiveis = disponiveis;
    }

    public Integer getUtilizadas() {
        return utilizadas;
    }

    public void setUtilizadas(Integer utilizadas) {
        this.utilizadas = utilizadas;
    }

    public Integer getExpiradas() {
        return expiradas;
    }

    public void setExpiradas(Integer expiradas) {
        this.expiradas = expiradas;
    }
}
