package br.com.pacto.controller.json.turma;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Capacidade por categoria do horário da turma")
public class HorarioCapacidadeCategoriaDTO {
    @ApiModelProperty(value = "Código identificador da capacidade por categoria", example = "10")
    private Integer codigo;
    @ApiModelProperty(value = "Código identificador do horário da turma", example = "101")
    private Integer horarioTurma;
    @ApiModelProperty(value = "Tipo da categoria", example = "Adulto")
    private String tipoCategoria;
    @ApiModelProperty(value = "Capacidade máxima permitida para esta categoria", example = "20")
    private Integer capacidade;

    public HorarioCapacidadeCategoriaDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public String getTipoCategoria() {
        return tipoCategoria;
    }

    public void setTipoCategoria(String tipoCategoria) {
        this.tipoCategoria = tipoCategoria;
    }
}
