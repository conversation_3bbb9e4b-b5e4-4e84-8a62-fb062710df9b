package br.com.pacto.controller.json.tvGestor.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de Business Intelligence para TV Gestor contendo estatísticas de comparecimento")
public class BiTvGestorDTO {

    @ApiModelProperty(value = "Número de alunos esperados (agendados) para o período", example = "25")
    private int esperado;

    @ApiModelProperty(value = "Número de alunos que compareceram no período", example = "18")
    private int compareceu;

    public int getEsperado() {
        return esperado;
    }

    public void setEsperado(int esperado) {
        this.esperado = esperado;
    }

    public int getCompareceu() {
        return compareceu;
    }

    public void setCompareceu(int compareceu) {
        this.compareceu = compareceu;
    }
}
