package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 03/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do grupo muscular exercitado em uma atividade física")
public class AtividadeGrupoMuscularResponseTO {

    @ApiModelProperty(value = "Código único identificador do grupo muscular exercitado na atividade", example = "8")
    private Integer id;
    @ApiModelProperty(value = "Nome do grupo muscular exercitado na atividade", example = "Peitorais")
    private String nome;

    public AtividadeGrupoMuscularResponseTO(){

    }

    public AtividadeGrupoMuscularResponseTO(AtividadeGrupoMuscular agm){
        this.id = agm.getGrupoMuscular().getCodigo();
        this.nome = agm.getGrupoMuscular().getNome();
    }

    public AtividadeGrupoMuscularResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}


