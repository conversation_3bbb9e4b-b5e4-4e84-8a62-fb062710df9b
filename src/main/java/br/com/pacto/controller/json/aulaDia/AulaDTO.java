package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(description = "Informações da aula")
public class AulaDTO {
    @ApiModelProperty(value = "Código do ambiente que será utilizado para realização da aula", example = "1")
    private String ambienteId;
    //    ambienteId: "1"
    @ApiModelProperty(value = "Bonificação por realização da aula", example = "R$100")
    private String bonificacao;
    //    bonificacao: "R$150"
    @ApiModelProperty(value = "Capacidade de alunos para a aula", example = "30")
    private String capacidade;
    //    capacidade: "12"
    @ApiModelProperty(value = "Limite de vagas agregados", example = "30")
    private Integer limiteVagasAgregados;
    @ApiModelProperty(value = "Data final da aula (Em timestamp)", example = "1744570800")
    private Long dataFinal;
    //    dataFinal: 1540954800000
    @ApiModelProperty(value = "Data de início da aula (Em timestamp)", example = "1744567200")
    private Long dataInicio;
    //    dataInicio: 1538362800000
    @ApiModelProperty(value = "Lista de dias da semana que a aula acontece", example = "[\"Segunda\", \"Quarta\"]")
    private String[] diasSemana;
    //    diasSemana: ["domingo", "segunda"]
    @ApiModelProperty(value = "Lista de horários da aula")
    private HorarioDTO[] horarios;
    //    horarios: [{inicio: "12:00", fim: "13:00"}, {inicio: "14:00", fim: "15:00"}]
    @ApiModelProperty(value = "Mensagem da aula", example = "Aula de Spinning.")
    private String mensagem;
    //    mensagem: "Opa asaoisa"
    @ApiModelProperty(value = "URL do vídeo da aula no Youtube", example = "www.youtube.com/video/aula")
    private String urlVideoYoutube;
    //    link do youtube para o cliente transmitir aulas online
    @ApiModelProperty(value = "Meta", example = "80%")
    private String meta;
    //    meta: "80%"
    @ApiModelProperty(value = "Código da modalidade vinculada a aula", example = "1")
    private String modalidadeId;
    //    modalidadeId: "1"
    @ApiModelProperty(value = "Nome da aula", example = "Aula Spinning")
    private String nome;
    //    nome: "vfcvcvc"
    @ApiModelProperty(value = "Ocupação da aula", example = "ALTAFREQUENCIA")
    private String ocupacao;
    //    ocupacao: "ALTAFREQUENCIA"\
    @ApiModelProperty(value = "Pontuação bônus por realização da aula", example = "100")
    private String pontuacaoBonus;
    //    pontuacaoBonus: "122"
    @ApiModelProperty(value = "Código identificador do professor responsável pela aula", example = "1")
    private String professorId;
    //    professorId: "57"
    @ApiModelProperty(value = "Tempo de tolerância de checkin para a aula em minutos", example = "10")
    private String toleranciaMin;
    @ApiModelProperty(value = "Tipo de tolerância da aula.\n\n <strong>Valores disponíveis</strong>" +
            "- 1 (Após início)" +
            "- 2 (Antes do início)",
            example = "1",
            allowableValues = "1,2")
    private String tipoTolerancia;
    //    toleranciaMin: "12"
    @ApiModelProperty(value = "Indica se deve validar as restrições de marcação da aula", example = "false")
    private Boolean validarRestricoesMarcacao;
    @ApiModelProperty(value = "Indica se NÃO deve validar a modalidade do contrato para participação da aula", example = "false")
    private Boolean naoValidarModalidadeContrato;
    @ApiModelProperty(value = "Código do produto GymPass vinculado a aula", example = "1")
    private Integer produtoGymPass;
    @ApiModelProperty(value = "URL da turma virtual para a aula", example = "www.pactosolucoes.com.br/turma-virtual/aula")
    private String urlTurmaVirtual;
    @ApiModelProperty(value = "ID da classe no GymPass", example = "1")
    private Integer idClasseGymPass;
    @ApiModelProperty(value = "Indica se é possível visualizar o produto GymPass", example = "false")
    private boolean visualizarProdutosGympass;
    @ApiModelProperty(value = "Indica se é possível visualizar o produto Totalpass", example = "false")
    private boolean visualizarProdutosTotalpass;
    @ApiModelProperty(value = "Indica se permite fixar a aula", example = "true")
    private Boolean permiteFixar;
    @ApiModelProperty(value = "Indica se a aula tem integração com o Selfloops", example = "false")
    private Boolean aulaIntegracaoSelfloops;
    private byte[] image;
    @ApiModelProperty(value = "URL da imagem da aula", example = "www.pactosolucoes.com.br/imagens/aula-spinning.png")
    private String imageUrl;
    @ApiModelProperty(value = "Indica se deve manter a foto anterior", example = "true")
    private Boolean manterFotoAnterior;
    @ApiModelProperty(value = "Níveis vinculado a aula")
    private List<NivelTO> niveis;

    @ApiModelProperty(value = "Idade máxima em anos para participação da aula", example = "70")
    private Integer idadeMaximaAnos;
    @ApiModelProperty(value = "Idade máxima em meses para participação da aula", example = "840")
    private Integer idadeMaximaMeses;
    @ApiModelProperty(value = "Idade mínima necessária para participação da aula", example = "18")
    private Integer idadeMinimaAnos;
    @ApiModelProperty(value = "Idade mínima em meses necessária para participação da aula", example = "216")
    private Integer idadeMinimaMeses;
    @ApiModelProperty(value = "Lista de links dos vídeos da turma")
    private List<TurmaVideoDTO> linkVideos;

    @ApiModelProperty(value = "Código do horário da turma para edição", example = "1")
    private Integer idHorarioTurmaEdicao;
    @ApiModelProperty(value = "Dia do horário da turma para edição (Formato: yyyyMMdd)", example = "20250610")
    private String diaHorarioTurmaEdicao;
    @ApiModelProperty(value = "Dia limite para edição (Formato: yyyyMMdd)", example = "20250610")
    private String diaLimiteTurmaEdicao;
    @ApiModelProperty(value = "Tipo de edição", example = "ALTERACAO")
    private String tipoEscolhaEdicao;
    @ApiModelProperty(value = "Horário inicial da aula", example = "09:00")
    private String horarioInicial;
    @ApiModelProperty(value = "Horário final da aula", example = "10:00")
    private String horarioFinal;
    @ApiModelProperty(value = "Dia da semana que a aula ocorrerá", example = "Terça-feira")
    private String diaSemana;
    @ApiModelProperty(value = "Tipo da reserva de equipamento", example = "Reserva por aluno")
    private String tipoReservaEquipamento;
    @ApiModelProperty(value = "Mapa de equipamentos da aula", example = "Mapa de bicicletas Spinning Room")
    private String mapaEquipamentos;
    @ApiModelProperty(value = "Lista de equipamentos por turma")
    private List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho;

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public List<NivelTO> getNiveis() {
        if (niveis == null) {
            niveis = new ArrayList<>();
        }
        return niveis;
    }

    public void setNiveis(List<NivelTO> niveis) {
        this.niveis = niveis;
    }

    public String getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(String tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public String getAmbienteId() {
        return ambienteId;
    }

    public void setAmbienteId(String ambienteId) {
        this.ambienteId = ambienteId;
    }

    public String getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(String bonificacao) {
        this.bonificacao = bonificacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public String getModalidadeId() {
        return modalidadeId;
    }

    public void setModalidadeId(String modalidadeId) {
        this.modalidadeId = modalidadeId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(String ocupacao) {
        this.ocupacao = ocupacao;
    }

    public String getPontuacaoBonus() {
        return pontuacaoBonus;
    }

    public void setPontuacaoBonus(String pontuacaoBonus) {
        this.pontuacaoBonus = pontuacaoBonus;
    }

    public String getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(String capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public String[] getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String[] diasSemana) {
        this.diasSemana = diasSemana;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public HorarioDTO[] getHorarios() {
        return horarios;
    }

    public void setHorarios(HorarioDTO[] horarios) {
        this.horarios = horarios;
    }

    public Long getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Long dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getProfessorId() {
        return professorId;
    }

    public void setProfessorId(String professorId) {
        this.professorId = professorId;
    }

    public String getToleranciaMin() {
        return toleranciaMin;
    }

    public void setToleranciaMin(String toleranciaMin) {
        this.toleranciaMin = toleranciaMin;
    }

    public Boolean getValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(Boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public Boolean getNaoValidarModalidadeContrato() {
        if (naoValidarModalidadeContrato == null) {
            naoValidarModalidadeContrato = false;
        }
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(Boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public byte[] getImage() {
        return image;
    }

    public void setImage(byte[] image) {
        this.image = image;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean getVisualizarProdutosGympass() {
        return visualizarProdutosGympass;
    }

    public void setVisualizarProdutosGympass(boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public boolean getVisualizarProdutosTotalpass() {
        return visualizarProdutosTotalpass;
    }

    public void setVisualizarProdutosTotalpass(boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public Boolean getPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public Boolean getManterFotoAnterior() {
        return manterFotoAnterior;
    }

    public void setManterFotoAnterior(Boolean manterFotoAnterior) {
        this.manterFotoAnterior = manterFotoAnterior;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public boolean isVisualizarProdutosGympass() {
        return visualizarProdutosGympass;
    }

    public boolean isVisualizarProdutosTotalpass() {
        return visualizarProdutosTotalpass;
    }

    public Integer getIdHorarioTurmaEdicao() {
        return idHorarioTurmaEdicao;
    }

    public void setIdHorarioTurmaEdicao(Integer idHorarioTurmaEdicao) {
        this.idHorarioTurmaEdicao = idHorarioTurmaEdicao;
    }

    public String getDiaHorarioTurmaEdicao() {
        return diaHorarioTurmaEdicao;
    }

    public void setDiaHorarioTurmaEdicao(String diaHorarioTurmaEdicao) {
        this.diaHorarioTurmaEdicao = diaHorarioTurmaEdicao;
    }

    public String getTipoEscolhaEdicao() {
        return tipoEscolhaEdicao;
    }

    public void setTipoEscolhaEdicao(String tipoEscolhaEdicao) {
        this.tipoEscolhaEdicao = tipoEscolhaEdicao;
    }

    public String getDiaLimiteTurmaEdicao() {
        return diaLimiteTurmaEdicao;
    }

    public void setDiaLimiteTurmaEdicao(String diaLimiteTurmaEdicao) {
        this.diaLimiteTurmaEdicao = diaLimiteTurmaEdicao;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public List<TurmaMapaEquipamentoAparelhoDTO> getTurmaMapaEquipamentoAparelho() {
        return turmaMapaEquipamentoAparelho;
    }

    public void setTurmaMapaEquipamentoAparelho(List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho) {
        this.turmaMapaEquipamentoAparelho = turmaMapaEquipamentoAparelho;
    }

    public Boolean getAulaIntegracaoSelfloops() {
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(Boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }
}

