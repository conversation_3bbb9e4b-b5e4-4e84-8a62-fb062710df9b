package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de ocupação por dia da semana")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OcupacaoDTO {
    @ApiModelProperty(value = "Ocupação da segunda-feira por período")
    private DiaOcupacaoDTO segunda;

    @ApiModelProperty(value = "Ocupação da terça-feira por período")
    private DiaOcupacaoDTO terca;

    @ApiModelProperty(value = "Ocupação da quarta-feira por período")
    private DiaOcupacaoDTO quarta;

    @ApiModelProperty(value = "Ocupação da quinta-feira por período")
    private DiaOcupacaoDTO quinta;

    @ApiModelProperty(value = "Ocupação da sexta-feira por período")
    private DiaOcupacaoDTO sexta;

    @ApiModelProperty(value = "Ocupação do sábado por período")
    private DiaOcupacaoDTO sabado;

    @ApiModelProperty(value = "Ocupação do domingo por período")
    private DiaOcupacaoDTO domingo;

    public OcupacaoDTO() {
    }



    public DiaOcupacaoDTO getSegunda() {
        return segunda;
    }

    public void setSegunda(DiaOcupacaoDTO segunda) {
        this.segunda = segunda;
    }

    public DiaOcupacaoDTO getTerca() {
        return terca;
    }

    public void setTerca(DiaOcupacaoDTO terca) {
        this.terca = terca;
    }

    public DiaOcupacaoDTO getQuarta() {
        return quarta;
    }

    public void setQuarta(DiaOcupacaoDTO quarta) {
        this.quarta = quarta;
    }

    public DiaOcupacaoDTO getQuinta() {
        return quinta;
    }

    public void setQuinta(DiaOcupacaoDTO quinta) {
        this.quinta = quinta;
    }

    public DiaOcupacaoDTO getSexta() {
        return sexta;
    }

    public void setSexta(DiaOcupacaoDTO sexta) {
        this.sexta = sexta;
    }

    public DiaOcupacaoDTO getSabado() {
        return sabado;
    }

    public void setSabado(DiaOcupacaoDTO sabado) {
        this.sabado = sabado;
    }

    public DiaOcupacaoDTO getDomingo() {
        return domingo;
    }

    public void setDomingo(DiaOcupacaoDTO domingo) {
        this.domingo = domingo;
    }
}
