package br.com.pacto.controller.json.usuario;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.FiltroPerfilJSON;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.perfil.PerfilService;
import br.com.pacto.swagger.respostas.perfil.ExemploRespostaPerfilResponseTO;
import br.com.pacto.swagger.respostas.perfil.ExemploRespostaListPerfilResponseTO;
import br.com.pacto.swagger.respostas.perfil.ExemploRespostaListPerfilResponseTOPaginacao;
import br.com.pacto.swagger.respostas.perfil.ExemploRespostaPerfilDTO;
import br.com.pacto.swagger.respostas.perfil.ExemploRespostaListProfessorResponseTOPaginacao;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import springfox.documentation.annotations.ApiIgnore;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 24/08/2018.
 */
@Controller
@RequestMapping("/psec/perfis-acesso")
public class PerfilController {

    private PerfilService perfilService;

    @Autowired
    public PerfilController(PerfilService perfilService){
        Assert.notNull(perfilService, "O serviço de perfil não foi injetado corretamente");
        this.perfilService = perfilService;
    }


    @ApiOperation(
            value = "Criar perfil de acesso",
            notes = "Cria um novo perfil de acesso no sistema com permissões específicas. " +
                    "O perfil define quais recursos e funcionalidades um usuário pode acessar.",
            tags = "Perfil de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaPerfilResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarPerfil(
            @ApiParam(value = "Dados do perfil a ser criado contendo nome, tipo e permissões", required = true)
            @RequestBody HashMap<String, Object> perfilDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.cadastrarPerfil(perfilDTO));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Editar perfil de acesso por ID",
            notes = "Edita um perfil de acesso existente através do seu ID. " +
                    "Permite alterar nome, tipo e permissões do perfil.",
            tags = "Perfil de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaPerfilDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/{id}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarPerfil(
            @ApiParam(value = "ID único do perfil a ser editado", defaultValue = "1", required = true)
            @PathVariable("id") Integer perfilId,
            @ApiParam(value = "Dados atualizados do perfil contendo nome, tipo e permissões", required = true)
            @RequestBody HashMap<String, Object> perfilDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.editarPerfil(perfilDTO, perfilId, null));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar editar perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Editar perfil de acesso por nome",
            notes = "Edita um perfil de acesso existente através do seu nome. " +
                    "Permite alterar tipo e permissões do perfil identificado pelo nome.",
            tags = "Perfil de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaPerfilDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "porNome/{nome}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarPerfil(
            @ApiParam(value = "Nome do perfil a ser editado", defaultValue = "Professor", required = true)
            @PathVariable("nome") String perfilNome,
            @ApiParam(value = "Dados atualizados do perfil contendo tipo e permissões", required = true)
            @RequestBody HashMap<String, Object> perfilDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.editarPerfil(perfilDTO, null, perfilNome));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar editar perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar perfis de acesso",
            notes = "Lista os perfis de acesso cadastrados no sistema com suporte a filtros e paginação. " +
                    "Permite buscar perfis por nome e navegar pelos resultados de forma paginada.",
            tags = "Perfil de Acesso"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListPerfilResponseTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarPerfil(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do perfil.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Professor\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroPerfilJSON filtroPerfilJSON = new FiltroPerfilJSON(filtros);
            return ResponseEntityFactory.ok(perfilService.consultarPerfil(filtroPerfilJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar perfil", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar perfil de acesso por ID",
            notes = "Obtém os dados completos de um perfil de acesso específico através do seu ID. " +
                    "Retorna informações detalhadas incluindo recursos e funcionalidades disponíveis.",
            tags = "Perfil de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaPerfilDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterPerfil(
            @ApiParam(value = "ID único do perfil a ser consultado", defaultValue = "1", required = true)
            @PathVariable(value = "id") Integer perfilId) {
        try {
            return ResponseEntityFactory.ok(perfilService.obterPerfil(perfilId));
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao consultar perfil", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Remover perfil de acesso",
            notes = "Remove um perfil de acesso do sistema através do seu ID. " +
                    "A remoção só é permitida se não houver usuários vinculados ao perfil.",
            tags = "Perfil de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerPerfil(
            @ApiParam(value = "ID único do perfil a ser removido", defaultValue = "1", required = true)
            @PathVariable(value = "id") Integer perfilId) {
        try {
            perfilService.removerPerfil(perfilId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao remover perfil", e);
            if (e.getCause() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar todos os perfis de acesso",
            notes = "Consulta todos os perfis de acesso cadastrados no sistema sem filtros ou paginação. " +
                    "Retorna uma lista completa de todos os perfis disponíveis.",
            tags = "Perfil de Acesso"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListPerfilResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTodos() {
        try {
            return ResponseEntityFactory.ok(perfilService.consultarTodos());
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao consultar todos os perfis", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar professores por perfil",
            notes = "Consulta todos os professores que possuem um perfil específico com suporte a filtros e paginação. " +
                    "Permite buscar professores por nome e navegar pelos resultados de forma paginada.",
            tags = "Perfil de Acesso"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListProfessorResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PERFIL_USUARIO)
    @RequestMapping(value = "/consultar-professores-por-perfil/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProfessoresPorPerfil(
            @ApiParam(value = "ID único do perfil para consulta dos professores", defaultValue = "1", required = true)
            @PathVariable(value = "id") Integer perfilId,
            @ApiParam(value = "Filtro de busca rápida por nome do professor", defaultValue = "{\"quicksearchValue\":\"João\"}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "ID da empresa para filtrar professores", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaIdZw,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            return ResponseEntityFactory.ok(perfilService.consultarProfessoresPorPerfil(perfilId, paginadorDTO, filter, empresaIdZw), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(PerfilController.class.getName()).log(Level.SEVERE, "Erro ao listar os professores do perfil informado", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}


