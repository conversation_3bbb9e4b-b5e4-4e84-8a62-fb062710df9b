/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.acompanhamento;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
public class AcompanhamentoSimplesJSON extends SuperJSON {

    @ApiModelProperty(value = "Frequência de treinos realizados no formato 'X/Y dias', onde X representa os dias de treino executados e Y o total de dias previstos no programa", example = "15/30 dias")
    private String frequencia;

    @ApiModelProperty(value = "Percentual de assiduidade do aluno no programa de treino, calculado com base na frequência de execução dos treinos", example = "75.5")
    private Double assiduidade;

    @ApiModelProperty(value = "Frequência semanal recomendada para o aluno, indicando quantos dias por semana deve treinar", example = "3")
    private Integer frequenciaSemanal;

    @ApiModelProperty(value = "Número total de treinos realizados pelo aluno no programa atual", example = "15")
    private Integer nrTreinos;

    @ApiModelProperty(value = "Quantidade total de aulas previstas no programa de treino", example = "30")
    private Integer aulasPrevistas;

    public AcompanhamentoSimplesJSON() {
    }

    public AcompanhamentoSimplesJSON(String frequencia, Double assiduidade) {
        this.frequencia = frequencia;
        this.assiduidade = assiduidade;
    }

    public String getFrequencia() {
        if (frequencia == null) {
            frequencia = "";
        }
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    public Double getAssiduidade() {
        if (assiduidade == null) {
            assiduidade = 0.0;
        }
        return assiduidade;
    }

    public void setAssiduidade(Double assiduidade) {
        this.assiduidade = assiduidade;
    }

    public Integer getFrequenciaSemanal() {
        return frequenciaSemanal;
    }

    public void setFrequenciaSemanal(Integer frequenciaSemanal) {
        this.frequenciaSemanal = frequenciaSemanal;
    }

    public Integer getNrTreinos() {
        return nrTreinos;
    }

    public void setNrTreinos(Integer nrTreinos) {
        this.nrTreinos = nrTreinos;
    }

    public Integer getAulasPrevistas() {
        return aulasPrevistas;
    }

    public void setAulasPrevistas(Integer aulasPrevistas) {
        this.aulasPrevistas = aulasPrevistas;
    }
}
