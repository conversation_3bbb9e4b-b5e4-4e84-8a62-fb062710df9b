package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.ficha.Ficha;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR> 16/01/2019
 */
@ApiModel(description = "Informações sobre uma ficha de treino executada pelo aluno.")
public class FichaExecutada {

    @ApiModelProperty(value = "ID da ficha executada", example = "32")
    private Integer id;

    @ApiModelProperty(value = "Nome da ficha executada", example = "Ficha A - Segunda/Quinta")
    private String nome;

    @ApiModelProperty(value = "Data em que a ficha foi executada", example = "2024-05-10")
    private Date data;

    public FichaExecutada(Ficha ficha) {
        this.id = ficha.getCodigo();
        this.nome = ficha.getNome();
        this.data = ficha.getUltimaExecucao();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }
}
