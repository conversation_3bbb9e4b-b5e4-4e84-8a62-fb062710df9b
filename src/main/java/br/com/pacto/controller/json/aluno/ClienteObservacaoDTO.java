package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Observações cadastradas para o cliente.")
public class ClienteObservacaoDTO {

    @ApiModelProperty(value = "Código único identificador da observação", example = "128")
    private Integer id;

    @ApiModelProperty(value = "Indica se a observação é marcada como importante", example = "true")
    private Boolean importante;

    @ApiModelProperty(value = "Texto da observação feita ao cliente", example = "Cliente relatou dores lombares durante o treino de membros inferiores.")
    private String observacao;

    @ApiModelProperty(value = "Data de cadastro da observação", example = "2024-06-01")
    private Date data;

    @ApiModelProperty(value = "Indica se a observação está vinculada a uma avaliação física", example = "false")
    private Boolean avaliacaoFisica;

    @ApiModelProperty(value = "Nome do usuário que cadastrou a observação", example = "João Silva")
    private String usuario;

    public ClienteObservacaoDTO(ClienteObservacao clienteObservacao, Usuario usu) {
        this.id = clienteObservacao.getCodigo();
        this.importante = clienteObservacao.getImportante();
        this.observacao = clienteObservacao.getObservacao();
        this.data = clienteObservacao.getDataObservacao();
        this.avaliacaoFisica = clienteObservacao.getAvaliacaoFisica();
        this.usuario = usu != null ? usu.getNome() : "";
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }


    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }
}
