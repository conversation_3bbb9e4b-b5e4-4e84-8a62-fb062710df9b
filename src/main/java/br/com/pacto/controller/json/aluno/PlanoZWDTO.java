package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo on 09/07/2019.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "Informações do plano")
public class PlanoZWDTO {
    @ApiModelProperty(value = "Nome do plano", example = "MUSCULAÇÃO")
    private String nome;

    public PlanoZWDTO(String nome) {
        this.nome = nome;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
