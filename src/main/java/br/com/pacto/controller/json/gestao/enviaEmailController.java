package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.email.UteisEmail;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.swagger.respostas.email.ExemploRespostaEnvioEmail;
import br.com.pacto.util.ConfigsEmail;
import br.com.pacto.util.UtilContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Ramos
 * @since 29/11/2018
 */

@Controller
@RequestMapping("/enviar-email")
public class enviaEmailController extends SuperController {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private ConfiguracaoSistemaService configService;
    private transient IntegracaoCadastrosWSConsumer integracaoWS = UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

    @ApiOperation(
            value = "Enviar email de relatório",
            notes = "Envia um email com relatório de alunos. O sistema determina automaticamente se utiliza configurações locais " +
                    "(treino independente) ou integração com sistema ZW para o envio do email.",
            tags = "Email"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Email enviado com sucesso)", response = ExemploRespostaEnvioEmail.class)
    })
    @ResponseBody
    @RequestMapping(value = "/send", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alunosAtivosTreinoEnviarEmail(
            @ApiParam(value = "Conteúdo da mensagem do email a ser enviado", defaultValue = "Relatório de alunos inativos gerado em 15/01/2024", required = false)
            @RequestParam(value = "msgEmail", required = false) String msgEmail,
            @ApiParam(value = "Endereço de email de destino para envio do relatório", defaultValue = "<EMAIL>", required = false)
            @RequestParam(value = "email", required = false) String email) {
        try {
            String key = sessaoService.getUsuarioAtual().getChave();
            final String url = Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg);
            boolean treinoIndependente = SuperControle.independente(key);
            if (treinoIndependente){
                ConfigsEmail cfgEmail = configService.obterConfiguracoes(key);
                UteisEmail.enviarEmail("Relatório", "",
                        msgEmail, cfgEmail, email);
            }else{
                JSONObject json = new JSONObject();
                json.put("assunto", "Relatório de alunos inativos");
                json.put("email", email);
                json.put("mensagem",msgEmail);

                integracaoWS.enviarEmailAngular(url,key,json.toString());
            }
            return ResponseEntityFactory.ok(msgEmail);
        } catch (Exception e) {
            Logger.getLogger(enviaEmailController.class.getName()).log(Level.SEVERE, "Erro ao gerar o arquivo", e);
            return ResponseEntityFactory.erroInterno("erro ao gerar o arquivo", e.getMessage());
        }
    }

}
