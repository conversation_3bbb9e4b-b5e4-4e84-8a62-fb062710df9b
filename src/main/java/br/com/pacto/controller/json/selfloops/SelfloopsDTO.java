package br.com.pacto.controller.json.selfloops;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(description = "Objeto que representa as configurações de integração com Selfloops")
public class SelfloopsDTO {

    @ApiModelProperty(value = "ID do cliente no sistema Pacto", example = "PACTO_123456")
    private String pactoClientId;

    @ApiModelProperty(value = "Código da empresa no sistema de treino", example = "1")
    private Integer empresa; //codigo empresa do treino

    @ApiModelProperty(value = "Nome da integração", example = "Academia CrossFit Elite")
    private String nome;

    @ApiModelProperty(value = "Código de integração do Selfloops", example = "SLF123456")
    private String code;

    @ApiModelProperty(value = "Identificador da empresa no Selfloops", example = "SELFLOOPS_EMPRESA_001")
    private String empresaSelfloops;

    @ApiModelProperty(value = "Lista de equipes/times do Selfloops")
    private List<TeamsSelfloopsDTO> teams;

    public SelfloopsDTO() {}

    public String getPactoClientId() {
        return pactoClientId;
    }

    public void setPactoClientId(String pactoClientId) {
        this.pactoClientId = pactoClientId;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<TeamsSelfloopsDTO> getTeams() {
        return teams;
    }

    public void setTeams(List<TeamsSelfloopsDTO> teams) {
        this.teams = teams;
    }

    public String getEmpresaSelfloops() {
        return empresaSelfloops;
    }

    public void setEmpresaSelfloops(String empresaSelfloops) {
        this.empresaSelfloops = empresaSelfloops;
    }
}
