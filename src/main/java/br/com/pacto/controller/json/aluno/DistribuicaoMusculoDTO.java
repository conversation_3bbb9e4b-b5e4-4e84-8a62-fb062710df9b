package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.musculo.PontosMuscularEnum;
import br.com.pacto.objeto.Uteis;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR> 18/01/2019
 */
@ApiModel(description = "Distribuição muscular dos treinos do aluno, mostrando a proporção de trabalho de cada grupo muscular.")
public class DistribuicaoMusculoDTO {

    @ApiModelProperty(value = "Ponto muscular trabalhado. Referência ao enumerador PontosMuscularEnum com os grupos musculares disponíveis")
    private PontosMuscularEnum pontoMuscular;

    @ApiModelProperty(value = "Valor percentual da distribuição muscular (0.0 a 100.0)", example = "25.75")
    private Double valor;

    public DistribuicaoMusculoDTO(PontosMuscularEnum pontoMuscular, double quantEx, double quantAtividade) {
        this.pontoMuscular = pontoMuscular;
        this.valor = Uteis.arredondarForcando2CasasDecimais(quantEx / quantAtividade);
    }

    public PontosMuscularEnum getPontoMuscular() {
        return pontoMuscular;
    }

    public void setPontoMuscular(PontosMuscularEnum pontoMuscular) {
        this.pontoMuscular = pontoMuscular;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
