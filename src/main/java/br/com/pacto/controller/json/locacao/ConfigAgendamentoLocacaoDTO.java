package br.com.pacto.controller.json.locacao;

import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.turma.AmbienteDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "Configuração completa de agendamento de locação, incluindo dados do cliente, ambiente, valores e produtos.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfigAgendamentoLocacaoDTO {

    @ApiModelProperty(value = "Nome da locação", example = "Quadra de Tênis")
    private String nomeLocacao;

    @ApiModelProperty(value = "Lista de ambientes disponíveis para a locação")
    private List<AmbienteDTO> ambientes = new ArrayList<>();

    @ApiModelProperty(value = "Dados do aluno responsável pelo agendamento")
    private AlunoResponseTO aluno;

    @ApiModelProperty(value = "Ambiente selecionado para o agendamento")
    private AmbienteDTO ambiente;

    @ApiModelProperty(value = "Nome do responsável pela locação", example = "Maria Santos")
    private String responsavel;

    @ApiModelProperty(value = "Nome do produto/serviço da locação", example = "Locação de Quadra")
    private String produto;

    @ApiModelProperty(value = "Código do produto", example = "10")
    private Integer codigoProduto;

    @ApiModelProperty(value = "Valor por hora da locação", example = "50.0")
    private Double valorHora;

    @ApiModelProperty(value = "Valor base da locação", example = "150.0")
    private Double valorLocacao;

    @ApiModelProperty(value = "Valor dos extras obrigatórios", example = "25.0")
    private Double valorExtrasObrigatorios;

    @ApiModelProperty(value = "Valor dos extras adicionais", example = "15.0")
    private Double valorExtrasAdicionais;

    @ApiModelProperty(value = "Valor total do agendamento", example = "190.0")
    private Double valorTotal;

    @ApiModelProperty(value = "Lista de horários adicionados ao agendamento")
    private List<LocacaoHorarioTO> horariosAdicionados = new ArrayList<>();

    @ApiModelProperty(value = "Lista de horários disponíveis para o agendamento")
    private List<LocacaoHorarioTO> horarios = new ArrayList<>();

    @ApiModelProperty(value = "Lista de produtos obrigatórios para a locação")
    private List<LocacaoProdutoSugeridoTO> produtosObrigatorios = new ArrayList<>();

    @ApiModelProperty(value = "Lista de produtos sugeridos para a locação")
    private List<LocacaoProdutoSugeridoTO> produtosSugeridos = new ArrayList<>();

    @ApiModelProperty(value = "Data do agendamento", example = "15/01/2024")
    private String data;

    @ApiModelProperty(value = "Tipo de horário da locação. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- LIVRE (Livre)\n" +
            "- PLAY (Play)\n" +
            "- PRE_DEFINIDO (Pré-definido)\n", example = "PRE_DEFINIDO")
    private TipoHorarioLocacaoEnum tipoHorario;

    @ApiModelProperty(value = "Indica se o agendamento foi cancelado", example = "false")
    private boolean isCancelado;

    @ApiModelProperty(value = "Justificativa do cancelamento", example = "Cliente solicitou cancelamento")
    private String justificativa;

    @ApiModelProperty(value = "Data do cancelamento", example = "15/01/2024 14:30")
    private String dataCancelamento;

    @ApiModelProperty(value = "Usuário que realizou o cancelamento", example = "Maria Santos")
    private String usuarioCancelamento;

    @ApiModelProperty(value = "Indica se o agendamento foi finalizado", example = "false")
    private boolean isFinalizado;

    @ApiModelProperty(value = "Tempo mínimo em minutos para a locação", example = "60")
    private Integer tempoMinimoMinutos;

    @ApiModelProperty(value = "Código da pessoa cliente no sistema ZW", example = "123")
    private Integer codigoPessoaClienteZW;

    @ApiModelProperty(value = "Indica se a locação está bloqueada", example = "false")
    private Boolean bloqueado;

    public String getNomeLocacao() {
        return nomeLocacao;
    }

    public void setNomeLocacao(String nomeLocacao) {
        this.nomeLocacao = nomeLocacao;
    }

    public List<AmbienteDTO> getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(List<AmbienteDTO> ambientes) {
        this.ambientes = ambientes;
    }

    public Double getValorHora() {
        if(this.valorHora == null){
            this.valorHora = 0.0;
        }
        return valorHora;
    }

    public void setValorHora(Double valorHora) {
        this.valorHora = valorHora;
    }

    public Double getValorLocacao() {
        if(this.valorLocacao == null){
            this.valorLocacao = 0.0;
        }
        return valorLocacao;
    }

    public void setValorLocacao(Double valorLocacao) {
        this.valorLocacao = valorLocacao;
    }

    public Double getValorExtrasObrigatorios() {
        if(this.valorExtrasObrigatorios == null){
            this.valorExtrasObrigatorios = 0.0;
        }
        return valorExtrasObrigatorios;
    }

    public void setValorExtrasObrigatorios(Double valorExtrasObrigatorios) {
        this.valorExtrasObrigatorios = valorExtrasObrigatorios;
    }

    public Double getValorExtrasAdicionais() {
        return valorExtrasAdicionais;
    }

    public void setValorExtrasAdicionais(Double valorExtrasAdicionais) {
        this.valorExtrasAdicionais = valorExtrasAdicionais;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public List<LocacaoHorarioTO> getHorariosAdicionados() {
        return horariosAdicionados;
    }

    public void setHorariosAdicionados(List<LocacaoHorarioTO> horariosAdicionados) {
        this.horariosAdicionados = horariosAdicionados;
    }

    public List<LocacaoProdutoSugeridoTO> getProdutosObrigatorios() {
        return produtosObrigatorios;
    }

    public void setProdutosObrigatorios(List<LocacaoProdutoSugeridoTO> produtosObrigatorios) {
        this.produtosObrigatorios = produtosObrigatorios;
    }

    public String getProduto() {
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public List<LocacaoHorarioTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<LocacaoHorarioTO> horarios) {
        this.horarios = horarios;
    }

    public List<LocacaoProdutoSugeridoTO> getProdutosSugeridos() {
        return produtosSugeridos;
    }

    public void setProdutosSugeridos(List<LocacaoProdutoSugeridoTO> produtosSugeridos) {
        this.produtosSugeridos = produtosSugeridos;
    }

    public AmbienteDTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteDTO ambiente) {
        this.ambiente = ambiente;
    }

    public AlunoResponseTO getAluno() {
        return aluno;
    }

    public void setAluno(AlunoResponseTO aluno) {
        this.aluno = aluno;
    }

    public String getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public TipoHorarioLocacaoEnum getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(TipoHorarioLocacaoEnum tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public boolean isCancelado() {
        return isCancelado;
    }

    public void setCancelado(boolean cancelado) {
        isCancelado = cancelado;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(String dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getUsuarioCancelamento() {
        return usuarioCancelamento;
    }

    public void setUsuarioCancelamento(String usuarioCancelamento) {
        this.usuarioCancelamento = usuarioCancelamento;
    }

    public boolean isFinalizado() {
        return isFinalizado;
    }

    public void setFinalizado(boolean finalizado) {
        isFinalizado = finalizado;
    }

    public Integer getTempoMinimoMinutos() {
        return tempoMinimoMinutos;
    }

    public void setTempoMinimoMinutos(Integer tempoMinimoMinutos) {
        this.tempoMinimoMinutos = tempoMinimoMinutos;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Integer getCodigoPessoaClienteZW() {
        return codigoPessoaClienteZW;
    }

    public void setCodigoPessoaClienteZW(Integer codigoPessoaClienteZW) {
        this.codigoPessoaClienteZW = codigoPessoaClienteZW;
    }

    public Boolean getBloqueado() {
        return bloqueado;
    }

    public void setBloqueado(Boolean bloqueado) {
        this.bloqueado = bloqueado;
    }
}
