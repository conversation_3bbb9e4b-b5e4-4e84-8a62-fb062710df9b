package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados simplificados para cadastro de aluno")
public class AlunoCadastroSimplesDTO {

    @ApiModelProperty(value = "Nome completo do aluno", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "Data de nascimento em timestamp (milissegundos)", example = "631152000000")
    private Long dataNascimento;

    @ApiModelProperty(value = "Sexo do aluno. Valores possíveis: 'M' (<PERSON><PERSON><PERSON><PERSON>), 'F' (<PERSON><PERSON><PERSON>)", example = "M", allowableValues = "M,F")
    private String sexo;

    @ApiModelProperty(value = "Número do celular do aluno", example = "(11) 99999-9999")
    private String celular;

    @ApiModelProperty(value = "Endereço de email do aluno", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Dados da imagem em base64 para foto do aluno", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...")
    private String imagemData;

    public AlunoCadastroSimplesDTO() {

    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Long getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Long dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }
}
