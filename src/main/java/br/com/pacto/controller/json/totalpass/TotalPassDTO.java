package br.com.pacto.controller.json.totalpass;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de integração TotalPass")
public class TotalPassDTO {

    @ApiModelProperty(value = "Código único da configuração TotalPass", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Código da empresa no sistema Treino", example = "123")
    private Integer empresa;

    @ApiModelProperty(value = "Nome da empresa", example = "Academia Fitness Center")
    private String nome;

    @ApiModelProperty(value = "Token de API para integração TotalPass", example = "totalpass_api_token_abc123")
    private String tokenApiTotalpass;

    @ApiModelProperty(value = "Código identificador no sistema TotalPass", example = "TP_CODE_789")
    private String codigoTotalPass;

    @ApiModelProperty(value = "Limite de acessos permitidos por dia", example = "10")
    private Integer limiteDeAcessosPorDia;

    @ApiModelProperty(value = "Limite de aulas permitidas por dia", example = "3")
    private Integer limiteDeAulasPorDia;

    public TotalPassDTO() {}

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
         this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTokenApiTotalpass() {
        return tokenApiTotalpass;
    }

    public void setTokenApiTotalpass(String tokenApiTotalpass) {
        this.tokenApiTotalpass = tokenApiTotalpass;
    }

    public String getCodigoTotalPass() {
        return codigoTotalPass;
    }

    public void setCodigoTotalPass(String codigoTotalPass) {
        this.codigoTotalPass = codigoTotalPass;
    }

    public Integer getLimiteDeAcessosPorDia() {
        return limiteDeAcessosPorDia;
    }

    public void setLimiteDeAcessosPorDia(Integer limiteDeAcessosPorDia) {
        this.limiteDeAcessosPorDia = limiteDeAcessosPorDia;
    }

    public Integer getLimiteDeAulasPorDia() {
        return limiteDeAulasPorDia;
    }

    public void setLimiteDeAulasPorDia(Integer limiteDeAulasPorDia) {
        this.limiteDeAulasPorDia = limiteDeAulasPorDia;
    }
}
