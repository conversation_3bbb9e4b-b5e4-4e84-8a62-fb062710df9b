package br.com.pacto.controller.json.gestao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.wod.WodController;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.crossfit.BICrossfitService;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/crossfit-bi")
public class BICrossfitController{


    private final BICrossfitService biService;

    @Autowired
    public BICrossfitController(BICrossfitService bi){
        Assert.notNull(bi, "O serviço de wod não foi injetado corretamente");
        this.biService = bi;
    }


    @ApiOperation(
            value = "Gerar dados de Business Intelligence do Crossfit",
            notes = "Obtém dados consolidados de Business Intelligence para Crossfit incluindo número de alunos, resultados lançados, agendamentos e ocupação por período",
            tags = "BI Cross"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Dados de BI obtidos com sucesso", response = br.com.pacto.swagger.respostas.crossfit.ExemploRespostaBICrossfitDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/dados",params = {"idProfessor","mes","ano"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dados(
            @ApiParam(value = "Código da empresa", example = "1")
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Código do professor para filtrar os dados", required = true, example = "10")
            @RequestParam("idProfessor") Integer idProfessor,
            @ApiParam(value = "Mês para consulta dos dados (1-12)", required = true, example = "6")
            @RequestParam("mes") Integer mes,
            @ApiParam(value = "Ano para consulta dos dados", required = true, example = "2024")
            @RequestParam("ano") Integer ano) throws Exception {
        try {
            return ResponseEntityFactory.ok(biService.gerarBI(mes, ano, idProfessor, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BU", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter alunos por resultados lançados",
            notes = "Obtém lista paginada de alunos que possuem resultados de treinos lançados no sistema, com informações de matrícula, nome, situação e data de lançamento",
            tags = "BI Cross"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de alunos com resultados obtida com sucesso", response = br.com.pacto.swagger.respostas.crossfit.ExemploRespostaListDetalhesIndicadorBICrossfitDTOPaginacao.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/alunos-resultados", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunosPorResultados(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>mes:</strong> Mês para filtrar os resultados (1-12).<br/>" +
                    "- <strong>ano:</strong> Ano para filtrar os resultados.<br/>" +
                    "- <strong>idProfessor:</strong> Código do professor para filtrar os resultados.<br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno.<br/>" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"mes\":6, \"ano\":2024, \"idProfessor\":10, \"quicksearchValue\":\"João\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore
            PaginadorDTO paginadorDTO
    ) throws JSONException {
        FiltrosBiCrossfitJSON filtrosBiCrossfitJSON = new FiltrosBiCrossfitJSON(filters);
        try {
            return ResponseEntityFactory.ok(biService.alunosComResultadosLancados(filtrosBiCrossfitJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter alunos com resultados", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter alunos por agendamentos de aula",
            notes = "Obtém lista paginada de alunos que possuem agendamentos de aulas de Crossfit, com informações de matrícula, nome, modalidade e data da aula",
            tags = "BI Cross"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de alunos com agendamentos obtida com sucesso", response = br.com.pacto.swagger.respostas.crossfit.ExemploRespostaListAgendamentosAlunosDTOPaginacao.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/alunos-agendamentos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunosPorAgendamentosAula (
            @ApiParam(value = "Código da empresa", example = "1")
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>mes:</strong> Mês para filtrar os agendamentos (1-12).<br/>" +
                    "- <strong>ano:</strong> Ano para filtrar os agendamentos.<br/>" +
                    "- <strong>idProfessor:</strong> Código do professor para filtrar os agendamentos.<br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno.<br/>" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"mes\":6, \"ano\":2024, \"idProfessor\":10, \"quicksearchValue\":\"Maria\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore
            PaginadorDTO paginadorDTO
    ) throws JSONException {
        FiltrosBiCrossfitJSON filtrosBiCrossfitJSON = new FiltrosBiCrossfitJSON(filters);
        try {
            return ResponseEntityFactory.ok(biService.alunosPorAgendamentos(filtrosBiCrossfitJSON, empresaId, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter alunos por agendamentos de aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter alunos que frequentam aulas do professor",
            notes = "Obtém lista paginada de alunos que frequentam aulas de um professor específico, com informações de matrícula, nome, situação e data da aula, permitindo filtros por período e turno",
            tags = "BI Cross"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de alunos que frequentam aulas obtida com sucesso", response = br.com.pacto.swagger.respostas.crossfit.ExemploRespostaListDetalhesIndicadorBICrossfitDTOPaginacao.class)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/alunos-frequentam-aula/professor", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunosFrequentamAulaProfessor (
            @ApiParam(value = "Código da empresa", example = "1")
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>mes:</strong> Mês para filtrar as aulas (1-12).<br/>" +
                    "- <strong>ano:</strong> Ano para filtrar as aulas.<br/>" +
                    "- <strong>idProfessor:</strong> Código do professor para filtrar as aulas.<br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno.<br/>" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).<br/>" +
                    "- <strong>diasSemana:</strong> Dia da semana para filtrar as aulas (domingo, segunda, terca, quarta, quinta, sexta, sabado).<br/>" +
                    "- <strong>turno:</strong> Turno para filtrar as aulas (manha, tarde, noite).",
                    defaultValue = "{\"mes\":6, \"ano\":2024, \"idProfessor\":10, \"quicksearchValue\":\"Carlos\", \"quicksearchFields\":[\"nome\"], \"diasSemana\":\"segunda\", \"turno\":\"manha\"}")
            @RequestParam(value = "filters", required = false) JSONObject filters,
            @ApiIgnore
            PaginadorDTO paginadorDTO) throws JSONException {
        FiltrosBiCrossfitJSON filtrosBiCrossfitJSON = new FiltrosBiCrossfitJSON(filters);
        try {
            return ResponseEntityFactory.ok(biService.alunosFrequentaAulaProfessor(empresaId, filtrosBiCrossfitJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter alunos que frequentaram as aulas do professor", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter ranking geral de alunos",
            notes = "Obtém ranking geral de alunos baseado no aproveitamento em treinos de Crossfit durante o período especificado, calculando pontuação média por participação",
            tags = "BI Cross"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Ranking geral obtido com sucesso", response = br.com.pacto.swagger.respostas.crossfit.ExemploRespostaListRankingGeralDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.WOD)
    @RequestMapping(value = "/ranking", params = {"idProfessor","mes","ano"}, method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> rankingGeral(
            @ApiParam(value = "Código da empresa", example = "1")
            @RequestHeader(value = "empresaId", required = false) Integer empresaId,
            @ApiParam(value = "Código do professor para filtrar o ranking", required = true, example = "10")
            @RequestParam("idProfessor") Integer idProfessor,
            @ApiParam(value = "Mês para consulta do ranking (1-12)", required = true, example = "6")
            @RequestParam("mes") Integer mes,
            @ApiParam(value = "Ano para consulta do ranking", required = true, example = "2024")
            @RequestParam("ano") Integer ano,
            @ApiIgnore
            HttpServletRequest request) {
        try {
            try {
                Date dia = Calendario.getInstance(ano, mes, 1).getTime();
                Date ultimaHora = Uteis.obterUltimoDiaMesUltimaHora(dia);
                return ResponseEntityFactory.ok(biService.rankingGeral(dia, ultimaHora, request, empresaId));
            }catch (Exception e){
                throw new ServiceException(e);
            }

        } catch (ServiceException e) {
            Logger.getLogger(WodController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter BU", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
