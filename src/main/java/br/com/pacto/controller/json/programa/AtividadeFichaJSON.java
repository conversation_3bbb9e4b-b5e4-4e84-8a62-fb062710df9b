package br.com.pacto.controller.json.programa;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de uma atividade de ficha para importação")
public class AtividadeFichaJSON extends SuperJSON {

    @ApiModelProperty(value = "Código da ficha de treino", example = "456")
    private Integer codigoFicha;

    @ApiModelProperty(value = "Código da atividade", example = "123")
    private Integer codigoAtividade;

    @ApiModelProperty(value = "Nome da atividade", example = "Supino Reto com Barra")
    private String nomeAtividade;

    @ApiModelProperty(value = "Ordem de execução na sequência", example = "1")
    private Integer sequenciaOrdem;

    @ApiModelProperty(value = "Número de repetições", example = "12")
    private String repeticoes;

    @ApiModelProperty(value = "Carga em quilogramas", example = "80")
    private Integer carga;

    @ApiModelProperty(value = "Cadência de execução", example = "2")
    private Integer cadencia;

    @ApiModelProperty(value = "Duração do exercício", example = "00:02:30")
    private String duracao;

    @ApiModelProperty(value = "Velocidade de execução", example = "Moderada")
    private String velocidade;

    @ApiModelProperty(value = "Distância percorrida", example = "1000m")
    private String distancia;

    @ApiModelProperty(value = "Tempo de descanso entre séries em segundos", example = "90")
    private Integer descancoIntervalo;

    @ApiModelProperty(value = "Quantidade de séries", example = "3")
    private Integer qtdSeries;

    @ApiModelProperty(value = "Observações complementares", example = "Manter postura ereta durante execução")
    private String obsComplemento;

    public AtividadeFichaJSON() {
    }

    public Integer getCodigoFicha() {
        return codigoFicha;
    }

    public void setCodigoFicha(Integer codigoFicha) {
        this.codigoFicha = codigoFicha;
    }

    public Integer getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(Integer codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public String getNomeAtividade() {
        return nomeAtividade;
    }

    public void setNomeAtividade(String nomeAtividade) {
        this.nomeAtividade = nomeAtividade;
    }

    public Integer getSequenciaOrdem() {
        return sequenciaOrdem;
    }

    public void setSequenciaOrdem(Integer sequenciaOrdem) {
        this.sequenciaOrdem = sequenciaOrdem;
    }

    public String getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(String repeticoes) {
        this.repeticoes = repeticoes;
    }

    public Integer getCarga() {
        return carga;
    }

    public void setCarga(Integer carga) {
        this.carga = carga;
    }

    public Integer getCadencia() {
        return cadencia;
    }

    public void setCadencia(Integer cadencia) {
        this.cadencia = cadencia;
    }

    public String getDuracao() {
        return duracao;
    }

    public void setDuracao(String duracao) {
        this.duracao = duracao;
    }

    public String getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(String velocidade) {
        this.velocidade = velocidade;
    }

    public String getDistancia() {
        return distancia;
    }

    public void setDistancia(String distancia) {
        this.distancia = distancia;
    }

    public Integer getDescancoIntervalo() {
        return descancoIntervalo;
    }

    public void setDescancoIntervalo(Integer descancoIntervalo) {
        this.descancoIntervalo = descancoIntervalo;
    }

    public Integer getQtdSeries() {
        return qtdSeries;
    }

    public void setQtdSeries(Integer qtdSeries) {
        this.qtdSeries = qtdSeries;
    }

    public String getObsComplemento() {
        return obsComplemento;
    }

    public void setObsComplemento(String obsComplemento) {
        this.obsComplemento = obsComplemento;
    }
}
