package br.com.pacto.controller.json.colaborador;

import br.com.pacto.bean.perfil.permissao.Permissao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

@ApiModel(description = "DTO para representar as funcionalidades específicas disponíveis para o usuário")
public class FuncionalidadesDTO {

    @ApiModelProperty(value = "Nome da funcionalidade do sistema", example = "tela_prescricao_treino")
    private String nome;

    @ApiModelProperty(value = "Indica se o usuário possui acesso à funcionalidade", example = "true")
    private Boolean possuiFuncionalidade;

    public FuncionalidadesDTO(Permissao permissao) {
        this.nome = permissao.getRecurso().name().toLowerCase();
        this.possuiFuncionalidade = permissao.getTipoPermissoes().size() > 0;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getPossuiFuncionalidade() {
        return possuiFuncionalidade;
    }

    public void setPossuiFuncionalidade(Boolean possuiFuncionalidade) {
        this.possuiFuncionalidade = possuiFuncionalidade;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FuncionalidadesDTO that = (FuncionalidadesDTO) o;
        return nome.equals(that.nome);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nome);
    }
}
