package br.com.pacto.controller.json.ambiente;

import br.com.pacto.bean.locacao.AmbienteHorarioLocacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Ambiente associado a um horário de locação, incluindo informações do espaço físico.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmbienteHorarioLocacaoTO {

    @ApiModelProperty(value = "Código único da associação ambiente-horário.", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Dados do ambiente físico.")
    private AmbienteTO ambiente;

    public AmbienteHorarioLocacaoTO() {}

    public AmbienteHorarioLocacaoTO(AmbienteHorarioLocacao ambiente) {
        this.codigo = ambiente.getCodigo();
        this.ambiente = new AmbienteTO(ambiente.getAmbiente());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public AmbienteTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteTO ambiente) {
        this.ambiente = ambiente;
    }
}
