package br.com.pacto.controller.json.benchmark;

import br.com.pacto.bean.benchmark.Benchmark;
import br.com.pacto.bean.benchmark.TipoBenchmarkTO;
import br.com.pacto.enumerador.crossfit.TipoWodEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by joao moita on 27/09/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de resposta do benchmark")
public class BenchmarkResponseTO {

    @ApiModelProperty(value = "Informações do tipo de benchmark")
    private TipoBenchmarkTO tipoBenchmark;

    @ApiModelProperty(value = "ID único do benchmark", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Indica se o benchmark é pré-definido pelo sistema", example = "false")
    private Boolean preDefinido;

    @ApiModelProperty(value = "Nome do benchmark", example = "Fran")
    private String nome;

    @ApiModelProperty(value = "Tipo de exercício do benchmark. Valores possíveis: 'FOR_TIME', 'FOR_REPS', 'FOR_WEIGHT', 'AMRAP'", example = "FOR_TIME")
    private TipoWodEnum tipoExercicio;

    @ApiModelProperty(value = "Descrição detalhada dos exercícios do benchmark", example = "21-15-9 repetições de:\\n- Thrusters (43kg)\\n- Pull-ups")
    private String exercicios;

    @ApiModelProperty(value = "Observações adicionais sobre o benchmark", example = "Benchmark clássico do CrossFit, criado em homenagem a Fran Berkowitz")
    private String observacao;

    @ApiModelProperty(value = "URI da imagem demonstrativa", example = "https://arquivos.empresa.com/benchmarks/fran_123.jpg")
    private String imagemUri;

    @ApiModelProperty(value = "URI do vídeo demonstrativo", example = "https://www.youtube.com/watch?v=abc123")
    private String videoUri;

    public BenchmarkResponseTO(Benchmark benchmark) {
        this.tipoBenchmark = new TipoBenchmarkTO(benchmark.getTipoBenchmark());
        this.preDefinido = benchmark.getPreDefinido();
        this.id = benchmark.getCodigo();
        this.nome = benchmark.getNome();
        this.tipoExercicio = benchmark.getTipoWod();
        this.exercicios = benchmark.getDescricaoExercicios();
        this.observacao = benchmark.getObservacao();
        if(benchmark.isMidiaImagem()) {
            this.imagemUri = benchmark.getUrlMidia();
        } else {
            this.videoUri = benchmark.getUrlMidia();
        }
    }

    public TipoBenchmarkTO getTipoBenchmark() {
        return tipoBenchmark;
    }

    public void setTipoBenchmark(TipoBenchmarkTO tipoBenchmark) {
        this.tipoBenchmark = tipoBenchmark;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getPreDefinido() {
        return preDefinido;
    }

    public void setPreDefinido(Boolean preDefinido) {
        this.preDefinido = preDefinido;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public TipoWodEnum getTipoExercicio() {
        return tipoExercicio;
    }

    public void setTipoExercicio(TipoWodEnum tipoExercicio) {
        this.tipoExercicio = tipoExercicio;
    }

    public String getExercicios() {
        return exercicios;
    }

    public void setExercicios(String exercicios) {
        this.exercicios = exercicios;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getImagemUri() {
        return imagemUri;
    }

    public void setImagemUri(String imagemUri) {
        this.imagemUri = imagemUri;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }
}
