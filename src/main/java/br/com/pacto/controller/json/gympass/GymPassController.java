package br.com.pacto.controller.json.gympass;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import br.com.pacto.service.intf.gympass.LogGymPassService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.gympass.*;
import br.com.pacto.util.UtilContext;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 30/03/2020
 */
@Api(tags = "Gympass")
@Controller
@RequestMapping("/gympass")
public class GymPassController extends SuperController {

    @Autowired
    private AgendaService agendaService;
    @Autowired
    private GymPassBookingService gymPassBookingService;
    @Autowired
    private LogGymPassService logService;
    @Autowired
    private ClienteSinteticoService clienteSinteticoService;

    @ApiOperation(
            value = "Sincronizar turma com GymPass Booking",
            notes = "Sincroniza uma turma específica com a plataforma GymPass Booking. " +
                    "Realiza a integração dos dados da turma para disponibilização na plataforma GymPass.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaSincronizarTurma.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/sincronizar", method = {RequestMethod.GET, RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizar(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa no sistema ZW", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaZW") Integer empresaZW,
            @ApiParam(value = "Código da empresa no sistema TW", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaTW") Integer empresaTW,
            @ApiParam(value = "Código identificador da turma a ser sincronizada", defaultValue = "123", required = true)
            @RequestHeader(value = "turmaId") Integer turmaId) {
        try {
            return ResponseEntityFactory.ok(agendaService.sincronizarTurmaGympassBooking(ctx, empresaZW, empresaTW, turmaId));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Excluir horários GymPass Booking",
            notes = "Exclui horários de uma turma específica na plataforma GymPass Booking. " +
                    "Permite excluir todos os horários ou apenas horários específicos da turma.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaExcluirHorarios.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/excluir", method = {RequestMethod.DELETE}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluir(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa no sistema ZW", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaZW") Integer empresaZW,
            @ApiParam(value = "Código da empresa no sistema TW", defaultValue = "1", required = true)
            @RequestHeader(value = "empresaTW") Integer empresaTW,
            @ApiParam(value = "Indica se deve excluir todos os horários da turma", defaultValue = "false", required = true)
            @RequestHeader(value = "excluirTodas") boolean excluirTodas,
            @ApiParam(value = "Código identificador da turma", defaultValue = "123", required = true)
            @RequestHeader(value = "turmaId") Integer turmaId) {
        try {
            return ResponseEntityFactory.ok(agendaService.excluirHorariosGympassBooking(ctx, empresaZW, empresaTW, turmaId, excluirTodas));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar turma com gympass booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Processar booking GymPass",
            notes = "Processa um booking (agendamento) recebido da plataforma GymPass. " +
                    "Realiza a validação e processamento do agendamento no sistema interno.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBooking.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/booking", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> booking(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "JSON contendo os dados do booking recebido do GymPass", defaultValue = "{\"booking_id\":\"BK123\",\"user_id\":\"USR456\",\"class_id\":\"CLS789\"}", required = true)
            @RequestBody String booking,
            @ApiParam(value = "Indica se está apenas validando o booking sem processar", defaultValue = "false")
            @RequestParam(required = false) Boolean validando) {
        try {
            return ResponseEntityFactory.ok(agendaService.booking(ctx, booking, validando == null ? Boolean.FALSE : validando));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Verificar saúde da integração GymPass",
            notes = "Verifica o status de saúde da integração com a plataforma GymPass para uma empresa específica. " +
                    "Retorna informações sobre a conectividade e funcionamento da integração.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaHealthGympass.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/healthGympass", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> healthGympass(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa no sistema TR", defaultValue = "1", required = true)
            @RequestParam Integer empresaTR) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.healthGympass(ctx, empresaTR));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao consultar healthGympass", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao consultar healthGympass", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar logs do GymPass",
            notes = "Consulta os logs mais recentes da integração com o GymPass. " +
                    "Retorna uma lista com os últimos registros de log para monitoramento e diagnóstico.",
            tags = "Logs"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListLogGymPass.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/log", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> log(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx) {
        try {
            return ResponseEntityFactory.ok(logService.ultimos(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }


    @ApiOperation(
            value = "Listar aulas disponíveis",
            notes = "Lista todas as aulas disponíveis para uma empresa específica na plataforma GymPass. " +
                    "Retorna informações detalhadas sobre as aulas que podem ser agendadas pelos usuários.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListClassesShowDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/aulas/{empresa}", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulas(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa para consulta das aulas", defaultValue = "1", required = true)
            @PathVariable Integer empresa) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.aulas(ctx, empresa));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar horários por empresa e dia",
            notes = "Consulta os horários disponíveis para agendamento em uma empresa específica em um determinado dia. " +
                    "Permite filtrar por classe específica e retorna os slots de horários disponíveis.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaMapHorarios.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/horarios/{empresa}/{dia}", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> horarios(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Data para consulta dos horários no formato string", defaultValue = "2025-06-20", required = true)
            @PathVariable String dia,
            @ApiParam(value = "Código da empresa para consulta dos horários", defaultValue = "1", required = true)
            @PathVariable Integer empresa,
            @ApiParam(value = "Código da classe para filtrar horários específicos", defaultValue = "123")
            @RequestParam(required = false) Integer idClasse) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.horarios(ctx, empresa, dia, idClasse));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar últimas sincronizações",
            notes = "Consulta as últimas sincronizações de horários realizadas com a plataforma GymPass. " +
                    "Retorna informações sobre os horários que foram sincronizados recentemente.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListHorariosSincronizados.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/ultimas", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ultimas(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx) {
        try {
            return ResponseEntityFactory.ok(agendaService.horariosSincronizados(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Inativar uma aula específica",
            notes = "Inativa uma aula específica na plataforma GymPass através de sua referência. " +
                    "Permite excluir uma aula específica mantendo outras ativas.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListClassesShowDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/inativar/{empresa}/{reference}/{exceto}", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inativar(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Referência da aula a ser inativada", defaultValue = "ZW-B-123", required = true)
            @PathVariable String reference,
            @ApiParam(value = "Código da aula a ser excluída da inativação", defaultValue = "456", required = true)
            @PathVariable Integer exceto,
            @ApiParam(value = "Código da empresa", defaultValue = "1", required = true)
            @PathVariable Integer empresa) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.inativarUmaAula(ctx, empresa, reference, exceto));
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseEntityFactory.ok("serviço desabilitado procure a squad");
    }

    @ApiOperation(
            value = "Normalizar dados da empresa",
            notes = "Normaliza os dados de uma empresa específica na integração com o GymPass. " +
                    "Realiza a padronização e correção dos dados para garantir consistência na plataforma.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/normalizar/{empresa}", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> normalizar(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa a ser normalizada", defaultValue = "1", required = true)
            @PathVariable Integer empresa) {
        try {
            gymPassBookingService.normalizar(ctx, empresa);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Excluir slots de um dia específico",
            notes = "Exclui todos os slots de agendamento de uma turma específica em um dia determinado. " +
                    "Remove os horários disponíveis para agendamento na plataforma GymPass.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/delete/slot/{empresa}/{turmaid}/{dia}", method = {RequestMethod.POST}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirSlotsDia(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Código da empresa", defaultValue = "1", required = true)
            @PathVariable Integer empresa,
            @ApiParam(value = "Código identificador da turma", defaultValue = "123", required = true)
            @PathVariable Integer turmaid,
            @ApiParam(value = "Data no formato ddMMyyyy para exclusão dos slots", defaultValue = "20062025", required = true)
            @PathVariable String dia) {
        try {
            gymPassBookingService.excluirSlotsDeUmDia(ctx, empresa, Uteis.getDate(dia, "ddMMyyyy"), turmaid);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar slots por período",
            notes = "Consulta todos os slots de agendamento disponíveis em um período específico. " +
                    "Retorna informações detalhadas sobre os horários disponíveis entre as datas informadas.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListSlotDiaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/slots-dia", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> slotsDia(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Data inicial do período no formato dd/MM/yyyy", defaultValue = "01/06/2025", required = true)
            @RequestParam String inicio,
            @ApiParam(value = "Data final do período no formato dd/MM/yyyy", defaultValue = "30/06/2025", required = true)
            @RequestParam String fim) {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.consultarSlotsGympass(ctx,
                    Uteis.getDate(inicio, "dd/MM/yyyy"),
                    Uteis.getDate(fim, "dd/MM/yyyy")));
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Sincronização geral",
            notes = "Realiza uma sincronização geral de todas as turmas com a plataforma GymPass Booking. " +
                    "Executa a sincronização completa dos dados sem especificar uma turma específica.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/sync", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sync(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx) {
        try {
            AgendaService agendaService = UtilContext.getBean(AgendaService.class);
            agendaService.sincronizarTurmasGympassBooking(ctx, 0, null);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar sincronizar booking", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter aluno por token GymPass",
            notes = "Obtém informações de um aluno através do token único fornecido pelo GymPass. " +
                    "Utilizado para identificar e validar usuários GymPass no sistema.",
            tags = "Gympass"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListString.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/obter-aluno-por-token/{token}", method = {RequestMethod.GET}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterAlunoPorToken(
            @ApiParam(value = "Contexto da empresa", defaultValue = "empresa1", required = true)
            @PathVariable String ctx,
            @ApiParam(value = "Token único do usuário GymPass", defaultValue = "GP_TOKEN_123456", required = true)
            @PathVariable String token) {
        try {
            return ResponseEntityFactory.ok(clienteSinteticoService.obterAlunoGympassPorToken(ctx, token));
        } catch (Exception ex) {
            Logger.getLogger(GymPassController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o aluno gympass", ex);
            return ResponseEntityFactory.erroInterno("erro_interno", ex.getMessage());
        }
    }

}
