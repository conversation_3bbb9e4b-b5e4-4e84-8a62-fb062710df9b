package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoAjuste;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by joao moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações detalhadas do aparelho incluindo ajustes e atividades relacionadas")
public class AparelhoResponseTO {

    @ApiModelProperty(value = "Código único identificador do aparelho", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do aparelho", example = "Esteira Ergométrica")
    private String nome;

    @ApiModelProperty(value = "Sigla ou abreviação do aparelho", example = "EST")
    private String sigla;

    @ApiModelProperty(value = "Nome do ícone representativo do aparelho", example = "treadmill-icon")
    private String icone;

    @ApiModelProperty(value = "Indica se o aparelho pode ser usado em reservas de equipamento", example = "true")
    private Boolean usarEmReservaEquipamentos;

    @ApiModelProperty(value = "Lista de ajustes disponíveis para o aparelho")
    private List<AparelhoAjusteResponseTO> ajustes = new ArrayList<>();

    @ApiModelProperty(value = "Lista de atividades que podem ser realizadas no aparelho")
    private List<AtividadeAparelhoResponseTO> atividades = new ArrayList<>();

    @ApiModelProperty(value = "Código do sensor Selfloop associado ao aparelho", example = "SL001")
    private String sensorSelfloops;

    public AparelhoResponseTO(){

    }

    public AparelhoResponseTO(Aparelho aparelho){
        this.id = aparelho.getCodigo();
        this.nome = aparelho.getNome();
        this.sensorSelfloops = aparelho.getSensorSelfloops();
        for (AparelhoAjuste aparelhoAjuste: aparelho.getAjustes()){
            getAjustes().add(new AparelhoAjusteResponseTO(aparelhoAjuste));
        }
        for (AtividadeAparelho atividadeAparelho: aparelho.getAtividades()){
            getAtividades().add(new AtividadeAparelhoResponseTO(atividadeAparelho.getAtividade()));
        }
        this.sigla = aparelho.getSigla();
        this.icone = aparelho.getIcone();
        this.usarEmReservaEquipamentos = aparelho.getUsarEmReservaEquipamentos();
    }

    public AparelhoResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AparelhoAjusteResponseTO> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AparelhoAjusteResponseTO> ajustes) {
        this.ajustes = ajustes;
    }

    public List<AtividadeAparelhoResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeAparelhoResponseTO> atividades) {
        this.atividades = atividades;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getIcone() {
        return icone;
    }

    public void setIcone(String icone) {
        this.icone = icone;
    }

    public Boolean getUsarEmReservaEquipamentos() {
        return usarEmReservaEquipamentos;
    }

    public void setUsarEmReservaEquipamentos(Boolean usarEmReservaEquipamentos) {
        this.usarEmReservaEquipamentos = usarEmReservaEquipamentos;
    }

    public String getSensorSelfloops() {
        return sensorSelfloops;
    }

    public void setSensorSelfloops(String sensorSelfloops) {
        this.sensorSelfloops = sensorSelfloops;
    }
}
