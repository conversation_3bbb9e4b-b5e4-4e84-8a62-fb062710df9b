package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo 12/11/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações de telefone")
public class TelefoneDTO {
    @ApiModelProperty(value = "Número do telefone", example = "(99)999991234")
    private String numero;
    @ApiModelProperty(value = "Tipo do telefone.\n\n" +
            "<strong>Valores disponíveis:<strong/>" +
            "- FIXO" +
            "- CELULAR", allowableValues = "FIXO,CELULAR")
    private TipoTelefoneEnum tipo;

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public TipoTelefoneEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoTelefoneEnum tipo) {
        this.tipo = tipo;
    }
}
