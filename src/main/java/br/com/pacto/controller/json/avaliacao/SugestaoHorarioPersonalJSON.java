package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Sugestão de horário para agendamento de serviço personalizado")
public class SugestaoHorarioPersonalJSON {

    @ApiModelProperty(value = "Código identificador do tipo de evento/serviço sugerido", example = "4")
    public Integer tipoEvento;

    @ApiModelProperty(value = "Nome descritivo do tipo de evento/serviço", example = "Avaliação Física")
    public String nomeEvento;

    @ApiModelProperty(value = "Código identificador do agendamento, se já existir", example = "1001")
    public Integer idAgendamento;

    @ApiModelProperty(value = "Código identificador do professor disponível para o horário", example = "25")
    public Integer idProfessor;

    @ApiModelProperty(value = "Informações detalhadas do horário sugerido incluindo início e fim")
    public HorarioPersonalJSON horario;

    public SugestaoHorarioPersonalJSON(Integer tipoEvento, String nomeEvento, HorarioPersonalJSON horario, Integer idAgendamento, Integer idProfessor) {
        this.tipoEvento = tipoEvento;
        this.nomeEvento = nomeEvento;
        this.idAgendamento = idAgendamento;
        this.horario = horario;
        this.idProfessor = idProfessor;
    }

    public Integer getTipoEvento() {
        return tipoEvento;
    }

    public void setTipoEvento(Integer tipoEvento) {
        this.tipoEvento = tipoEvento;
    }

    public HorarioPersonalJSON getHorario() {
        return horario;
    }

    public void setHorario(HorarioPersonalJSON horario) {
        this.horario = horario;
    }

    public String getNomeEvento() {
        return nomeEvento;
    }

    public void setNomeEvento(String nomeEvento) {
        this.nomeEvento = nomeEvento;
    }

    public Integer getIdAgendamento() {
        return idAgendamento;
    }

    public void setIdAgendamento(Integer idAgendamento) {
        this.idAgendamento = idAgendamento;
    }

    public Integer getIdProfessor() {
        return idProfessor;
    }

    public void setIdProfessor(Integer idProfessor) {
        this.idProfessor = idProfessor;
    }
}
