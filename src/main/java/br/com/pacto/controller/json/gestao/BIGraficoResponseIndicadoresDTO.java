package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 18/10/2019
 */
@ApiModel(description = "Valores dos indicadores de Business Intelligence para gráficos, contendo métricas de alunos, agendamentos, avaliações e outros dados estatísticos")
public class BIGraficoResponseIndicadoresDTO {
    //o Humberto que falou que assim que funciona
    @ApiModelProperty(value = "Total de alunos da academia", example = "150")
    @JsonProperty("TOTAL")
    private Integer total;

    @ApiModelProperty(value = "Número de alunos ativos", example = "120")
    @JsonProperty("ATIVOS")
    private Integer ativos;

    @ApiModelProperty(value = "Número de alunos inativos", example = "30")
    @JsonProperty("INATIVOS")
    private Integer inativos;

    @ApiModelProperty(value = "Número de alunos ativos com treino", example = "100")
    @JsonProperty("ATIVOSTREINO")
    private Integer ativostreino;

    @ApiModelProperty(value = "Número de treinos em dia", example = "80")
    @JsonProperty("EMDIA")
    private Integer emdia;

    @ApiModelProperty(value = "Número de treinos vencidos", example = "20")
    @JsonProperty("TREINOSVENCIDOS")
    private Integer treinosvencidos;

    @ApiModelProperty(value = "Número de treinos a renovar", example = "15")
    @JsonProperty("TREINOSRENOVAR")
    private Integer treinosrenovar ;

    @ApiModelProperty(value = "Total de agendamentos realizados", example = "200")
    @JsonProperty("AGENDAMENTOS")
    private Integer agendamentos;

    @ApiModelProperty(value = "Número de agendamentos executados", example = "180")
    @JsonProperty("AGEXECUTARAM")
    private Integer agexecutaram;

    @ApiModelProperty(value = "Número de agendamentos em que o aluno faltou", example = "15")
    @JsonProperty("AGFALTARAM")
    private Integer agfaltaram;

    @ApiModelProperty(value = "Número de agendamentos cancelados", example = "5")
    @JsonProperty("AGCANCELARAM")
    private Integer agcancelaram;
    @ApiModelProperty(value = "Número de alunos que renovaram", example = "90")
    @JsonProperty("RENOVADOS")
    private Integer renovados;

    @ApiModelProperty(value = "Número de alunos que não renovaram", example = "10")
    @JsonProperty("NAORENOVADOS")
    private Integer naorenovados;

    @ApiModelProperty(value = "Número de contratos a vencer", example = "25")
    @JsonProperty("AVENCER")
    private Integer avencer;

    @ApiModelProperty(value = "Número de novos alunos na carteira", example = "12")
    @JsonProperty("NOVOSCARTEIRA")
    private Integer novoscarteira;

    @ApiModelProperty(value = "Número de alunos que trocaram de carteira", example = "3")
    @JsonProperty("TROCARAMCARTEIRA")
    private Integer trocaramcarteira;

    @ApiModelProperty(value = "Tempo médio dos alunos na carteira (em dias)", example = "180")
    @JsonProperty("MEDIOCARTEIRA")
    private Integer mediocarteira;

    @ApiModelProperty(value = "Número de alunos sem treino", example = "20")
    @JsonProperty("SEMTREINO")
    private Integer semtreino;

    @ApiModelProperty(value = "Percentual de treinos em dia", example = "80")
    @JsonProperty("PERCENTUALEMDIA")
    private Integer percentualemdia;

    @ApiModelProperty(value = "Percentual de treinos vencidos", example = "20")
    @JsonProperty("PERCENTUALVENCIDOS")
    private Integer percentualvencidos;

    @ApiModelProperty(value = "Tempo médio do programa de treino (em dias)", example = "90")
    @JsonProperty("MEDIORPROGRAMA")
    private Integer mediorprograma;

    @ApiModelProperty(value = "Número total de avaliações realizadas", example = "45")
    @JsonProperty("NRAVALIACOES")
    private Integer nravaliacoes;

    @ApiModelProperty(value = "Média das avaliações de treino", example = "4.2")
    @JsonProperty("AVALIACOES")
    private Double avaliacoes;
    @ApiModelProperty(value = "Número de avaliações com 1 estrela", example = "2")
    @JsonProperty("ESTRELAUM")
    private Integer estrelaum;

    @ApiModelProperty(value = "Número de avaliações com 2 estrelas", example = "3")
    @JsonProperty("ESTRELADOIS")
    private Integer estreladois;

    @ApiModelProperty(value = "Número de avaliações com 3 estrelas", example = "8")
    @JsonProperty("ESTRELATRES")
    private Integer estrelatres;

    @ApiModelProperty(value = "Número de avaliações com 4 estrelas", example = "15")
    @JsonProperty("ESTRELAQUATRO")
    private Integer estrelaquatro;

    @ApiModelProperty(value = "Número de avaliações com 5 estrelas", example = "17")
    @JsonProperty("ESTRELACINCO")
    private Integer estrelacinco;

    @ApiModelProperty(value = "Número de alunos com avaliação física", example = "85")
    @JsonProperty("COMAVALIACAO")
    private Integer comavaliacao;

    @ApiModelProperty(value = "Número de alunos sem avaliação física", example = "35")
    @JsonProperty("SEMAVALIACAO")
    private Integer semavaliacao;

    @ApiModelProperty(value = "Número de professores com agendamentos", example = "8")
    @JsonProperty("AGPROFESSORES")
    private Integer agprofessores;

    @ApiModelProperty(value = "Total de horas de disponibilidade dos professores", example = "320")
    @JsonProperty("HORASDISPONIBILIDADE")
    private Integer horasdisponibilidade;

    @ApiModelProperty(value = "Total de horas executadas pelos professores", example = "280")
    @JsonProperty("HORASEXECUTADAS")
    private Integer horasexecutadas;

    @ApiModelProperty(value = "Percentual de ocupação da agenda dos professores", example = "87")
    @JsonProperty("PERCOCUPACAO")
    private Integer percocupacao;
    @ApiModelProperty(value = "Número de agendamentos para novos treinos", example = "25")
    @JsonProperty("AGNOVOSTREINOS")
    private Integer agnovostreinos;

    @ApiModelProperty(value = "Número de agendamentos para renovação de treino", example = "18")
    @JsonProperty("AGTREINOSRENOVADOS")
    private Integer agtreinosrevisados;

    @ApiModelProperty(value = "Número de agendamentos para revisão de treino", example = "12")
    @JsonProperty("AGTREINOSREVISADOS")
    private Integer agtreinosrenovados;

    @ApiModelProperty(value = "Número de agendamentos para avaliação física", example = "8")
    @JsonProperty("AGAVALIACAOFISICA")
    private Integer agavaliacaofisica;

    @ApiModelProperty(value = "Percentual de renovação de contratos", example = "90")
    @JsonProperty("PERCENTUALRENOVACAO")
    private Integer percentualrenovacao;

    public BIGraficoResponseIndicadoresDTO( ){

     }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public Integer getInativos() {
        return inativos;
    }

    public void setInativos(Integer inativos) {
        this.inativos = inativos;
    }

    public Integer getAtivostreino() {
        return ativostreino;
    }

    public void setAtivostreino(Integer ativostreino) {
        this.ativostreino = ativostreino;
    }

    public Integer getEmdia() {
        return emdia;
    }

    public void setEmdia(Integer emdia) {
        this.emdia = emdia;
    }

    public Integer getTreinosvencidos() {
        return treinosvencidos;
    }

    public void setTreinosvencidos(Integer treinosvencidos) {
        this.treinosvencidos = treinosvencidos;
    }

    public Integer getTreinosrenovar() {
        return treinosrenovar;
    }

    public void setTreinosrenovar(Integer treinosrenovar) {
        this.treinosrenovar = treinosrenovar;
    }

    public Integer getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(Integer agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getAgexecutaram() {
        return agexecutaram;
    }

    public void setAgexecutaram(Integer agexecutaram) {
        this.agexecutaram = agexecutaram;
    }

    public Integer getAgfaltaram() {
        return agfaltaram;
    }

    public void setAgfaltaram(Integer agfaltaram) {
        this.agfaltaram = agfaltaram;
    }

    public Integer getAgcancelaram() {
        return agcancelaram;
    }

    public void setAgcancelaram(Integer agcancelaram) {
        this.agcancelaram = agcancelaram;
    }

    public Integer getRenovados() {
        return renovados;
    }

    public void setRenovados(Integer renovados) {
        this.renovados = renovados;
    }

    public Integer getNaorenovados() {
        return naorenovados;
    }

    public void setNaorenovados(Integer naorenovados) {
        this.naorenovados = naorenovados;
    }

    public Integer getAvencer() {
        return avencer;
    }

    public void setAvencer(Integer avencer) {
        this.avencer = avencer;
    }

    public Integer getNovoscarteira() {
        return novoscarteira;
    }

    public void setNovoscarteira(Integer novoscarteira) {
        this.novoscarteira = novoscarteira;
    }

    public Integer getTrocaramcarteira() {
        return trocaramcarteira;
    }

    public void setTrocaramcarteira(Integer trocaramcarteira) {
        this.trocaramcarteira = trocaramcarteira;
    }

    public Integer getMediocarteira() {
        return mediocarteira;
    }

    public void setMediocarteira(Integer mediocarteira) {
        this.mediocarteira = mediocarteira;
    }

    public Integer getSemtreino() {
        return semtreino;
    }

    public void setSemtreino(Integer semtreino) {
        this.semtreino = semtreino;
    }

    public Integer getPercentualemdia() {
        return percentualemdia;
    }

    public void setPercentualemdia(Integer percentualemdia) {
        this.percentualemdia = percentualemdia;
    }

    public Integer getPercentualvencidos() {
        return percentualvencidos;
    }

    public void setPercentualvencidos(Integer percentualvencidos) {
        this.percentualvencidos = percentualvencidos;
    }

    public Integer getMediorprograma() {
        return mediorprograma;
    }

    public void setMediorprograma(Integer mediorprograma) {
        this.mediorprograma = mediorprograma;
    }

    public Integer getNravaliacoes() {
        return nravaliacoes;
    }

    public void setNravaliacoes(Integer nravaliacoes) {
        this.nravaliacoes = nravaliacoes;
    }

    public Double getAvaliacoes() {
        return avaliacoes;
    }

    public void setAvaliacoes(Double avaliacoes) {
        this.avaliacoes = avaliacoes;
    }

    public Integer getEstrelaum() {
        return estrelaum;
    }

    public void setEstrelaum(Integer estrelaum) {
        this.estrelaum = estrelaum;
    }

    public Integer getEstreladois() {
        return estreladois;
    }

    public void setEstreladois(Integer estreladois) {
        this.estreladois = estreladois;
    }

    public Integer getEstrelatres() {
        return estrelatres;
    }

    public void setEstrelatres(Integer estrelatres) {
        this.estrelatres = estrelatres;
    }

    public Integer getEstrelaquatro() {
        return estrelaquatro;
    }

    public void setEstrelaquatro(Integer estrelaquatro) {
        this.estrelaquatro = estrelaquatro;
    }

    public Integer getEstrelacinco() {
        return estrelacinco;
    }

    public void setEstrelacinco(Integer estrelacinco) {
        this.estrelacinco = estrelacinco;
    }

    public Integer getComavaliacao() {
        return comavaliacao;
    }

    public void setComavaliacao(Integer comavaliacao) {
        this.comavaliacao = comavaliacao;
    }

    public Integer getSemavaliacao() {
        return semavaliacao;
    }

    public void setSemavaliacao(Integer semavaliacao) {
        this.semavaliacao = semavaliacao;
    }

    public Integer getAgprofessores() {
        return agprofessores;
    }

    public void setAgprofessores(Integer agprofessores) {
        this.agprofessores = agprofessores;
    }

    public Integer getHorasdisponibilidade() {
        return horasdisponibilidade;
    }

    public void setHorasdisponibilidade(Integer horasdisponibilidade) {
        this.horasdisponibilidade = horasdisponibilidade;
    }

    public Integer getHorasexecutadas() {
        return horasexecutadas;
    }

    public void setHorasexecutadas(Integer horasexecutadas) {
        this.horasexecutadas = horasexecutadas;
    }

    public Integer getPercocupacao() {
        return percocupacao;
    }

    public void setPercocupacao(Integer percocupacao) {
        this.percocupacao = percocupacao;
    }

    public Integer getAgnovostreinos() {
        return agnovostreinos;
    }

    public void setAgnovostreinos(Integer agnovostreinos) {
        this.agnovostreinos = agnovostreinos;
    }

    public Integer getAgtreinosrevisados() {
        return agtreinosrevisados;
    }

    public void setAgtreinosrevisados(Integer agtreinosrevisados) {
        this.agtreinosrevisados = agtreinosrevisados;
    }

    public Integer getAgtreinosrenovados() {
        return agtreinosrenovados;
    }

    public void setAgtreinosrenovados(Integer agtreinosrenovados) {
        this.agtreinosrenovados = agtreinosrenovados;
    }

    public Integer getAgavaliacaofisica() {
        return agavaliacaofisica;
    }

    public void setAgavaliacaofisica(Integer agavaliacaofisica) {
        this.agavaliacaofisica = agavaliacaofisica;
    }

    public Integer getPercentualrenovacao() {
        return percentualrenovacao;
    }

    public void setPercentualrenovacao(Integer percentualrenovacao) {
        this.percentualrenovacao = percentualrenovacao;
    }
}
