package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.DiasSemanaDashboardBI;
import br.com.pacto.bean.bi.MicroCharts;
import br.com.pacto.bean.bi.TipoEventoDisponibilidadeJSON;
import br.com.pacto.bean.cliente.ClienteSintetico;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 29/11/2018
 */
@ApiModel(description = "DTO contendo dados consolidados de Business Intelligence relacionados a treinos, incluindo indicadores de alunos, agendamentos, avaliações e estatísticas de treinamento.")
public class BITreinoResponseDTO {

    @ApiModelProperty(value = "Tempo médio de permanência no treino em dias.", example = "45")
    private Integer tempoMedioPermanenciaTreino = 0;

    @ApiModelProperty(value = "Total de alunos ativos sem treino ativo.", example = "12")
    private Integer totalAlunosSemTreino = 0;

    @ApiModelProperty(value = "Total de alunos ativos com treino ativo.", example = "85")
    private Integer totalAlunosComTreino = 0;

    @ApiModelProperty(value = "Percentual de alunos que utilizam o aplicativo móvel.", example = "67.5")
    private Double percUtilizamApp = 0.0;

    @ApiModelProperty(value = "Total geral de alunos.", example = "120")
    private Integer totalAlunos = 0;

    @ApiModelProperty(value = "Total de alunos com situação ativa.", example = "97")
    private Integer totalAlunosAtivos = 0;

    @ApiModelProperty(value = "Total de alunos com situação inativa.", example = "18")
    private Integer totalAlunosInativos = 0;

    @ApiModelProperty(value = "Total de alunos visitantes.", example = "5")
    private Integer totalAlunosVisitantes = 0;

    @ApiModelProperty(value = "Percentual de treinos em dia.", example = "78")
    private Integer percentualEmDia = 0;

    @ApiModelProperty(value = "Total de treinos vencidos.", example = "8")
    private Integer totalTreinosVencidos = 0;

    @ApiModelProperty(value = "Total de treinos que precisam ser renovados.", example = "15")
    private Integer totalTreinosRenovar = 0;

    @ApiModelProperty(value = "Total de treinos em dia.", example = "62")
    private Integer totalTreinosEmdia = 0;

    @ApiModelProperty(value = "Total de agendamentos realizados.", example = "245")
    private Integer agendamentos = 0;

    @ApiModelProperty(value = "Total de alunos que compareceram aos agendamentos.", example = "198")
    private Integer compareceram = 0;

    @ApiModelProperty(value = "Total de agendamentos confirmados.", example = "220")
    private Integer confirmados = 0;

    @ApiModelProperty(value = "Total de agendamentos aguardando confirmação.", example = "25")
    private Integer aguardandoConfirmacao = 0;

    @ApiModelProperty(value = "Percentual de avaliações realizadas.", example = "85")
    private Integer percentualAvaliacoes = 0;

    @ApiModelProperty(value = "Total de faltas em agendamentos.", example = "32")
    private Integer faltaram = 0;

    @ApiModelProperty(value = "Total de agendamentos cancelados.", example = "15")
    private Integer cancelaram = 0;

    @ApiModelProperty(value = "Número de avaliações com 5 estrelas.", example = "45")
    private Integer nr5estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 4 estrelas.", example = "38")
    private Integer nr4estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 3 estrelas.", example = "22")
    private Integer nr3estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 2 estrelas.", example = "8")
    private Integer nr2estrelas = 0;

    @ApiModelProperty(value = "Número de avaliações com 1 estrela.", example = "3")
    private Integer nr1estrelas = 0;

    @ApiModelProperty(value = "Total de professores ativos.", example = "12")
    private Integer professores = 0;

    @ApiModelProperty(value = "Total de horas de disponibilidade dos professores.", example = "480")
    private Integer horasDisponibilidade = 0;

    @ApiModelProperty(value = "Total de horas de atendimento efetivo.", example = "385")
    private Integer horasAtendimento = 0;

    @ApiModelProperty(value = "Percentual de ocupação das disponibilidades.", example = "80")
    private Integer ocupacao = 0;

    @ApiModelProperty(value = "Total de novos treinos criados.", example = "28")
    private Integer novosTreinos = 0;

    @ApiModelProperty(value = "Total de treinos revisados.", example = "15")
    private Integer treinosRevisados = 0;

    @ApiModelProperty(value = "Total de treinos renovados.", example = "42")
    private Integer treinosRenovados = 0;

    @ApiModelProperty(value = "Total de avaliações físicas realizadas.", example = "67")
    private Integer avaliacoesFisicas = 0;
    @ApiModelProperty(value = "Lista de tipos de eventos de disponibilidade com estatísticas.")
    private List<TipoEventoDisponibilidadeJSON> listaTipos;

    @ApiModelProperty(value = "Dados de micro gráficos para visualização.")
    private MicroCharts micro;

    @ApiModelProperty(value = "Dias de permanência na carteira.", example = "15")
    private Integer diaPermanenciaCarteira  = 0;

    @ApiModelProperty(value = "Meses de permanência na carteira.", example = "2")
    private Integer mesPremanenciaCarteira =0;

    @ApiModelProperty(value = "Lista de execuções por dias da semana.")
    private List<DiasSemanaDashboardBI> listDiasExecucao;

    @ApiModelProperty(value = "Percentual de renovações de contratos.", example = "75")
    private Integer percentualRenovacoes = 0;

    @ApiModelProperty(value = "Total de alunos com contratos a vencer.", example = "18")
    private Integer totalAlunosAvencer;

     public BITreinoResponseDTO (DashboardBI bi, List<TipoEventoDisponibilidadeJSON> listaTipos, MicroCharts micro,List<DiasSemanaDashboardBI> listDiasExecucao ){

        this.tempoMedioPermanenciaTreino = bi.getTempoMedioPermanenciaTreino();
        this.totalAlunosSemTreino = bi.getTotalAlunosSemTreino();
        this.totalAlunosComTreino = bi.getTotalAlunosTreino();
        this.percUtilizamApp = bi.getPercUtilizamApp();
        this.totalAlunos = bi.getTotalAlunos();
        this.totalAlunosAtivos = bi.getTotalAlunosAtivos();
        this.totalAlunosInativos = bi.getTotalAlunosInativos();
        this.totalAlunosVisitantes = bi.getTotalAlunosVisitantes();
        this.percentualEmDia = bi.getPercentualEmDia();
        this.totalTreinosVencidos = bi.getTotalTreinosVencidos();
        this.totalTreinosRenovar = bi.getTotalTreinosRenovar();
        this.totalTreinosEmdia = bi.getTotalTreinosEmdia();
        this.agendamentos = bi.getAgendamentos();
        this.compareceram = bi.getCompareceram();
        this.faltaram = bi.getFaltaram();
        this.cancelaram = bi.getCancelaram();
        this.nr5estrelas = bi.getNr5estrelas();
        this.nr4estrelas = bi.getNr4estrelas();
        this.nr3estrelas = bi.getNr3estrelas();
        this.nr2estrelas = bi.getNr2estrelas();
        this.nr1estrelas = bi.getNr1estrelas();
        this.professores = bi.getProfessores();
        this.horasDisponibilidade = bi.getHorasDisponibilidade();
        this.horasAtendimento = bi.getHorasAtendimento();
        this.ocupacao = bi.getOcupacao();
        this.novosTreinos = bi.getNovosTreinos();
        this.treinosRevisados = bi.getTreinosRevisados();
        this.treinosRenovados = bi.getTreinosRenovados();
        this.avaliacoesFisicas = bi.getAvaliacoesFisicas();
        this.listaTipos = listaTipos;
        this.micro = micro;
        this.totalAlunosAvencer =  bi.getTotalAlunosAvencer();
        this.percentualRenovacoes = bi.getPercentualRenovacoes();
        if(bi.getTempoMedianaPermanenciaCarteira() != null){
            this.diaPermanenciaCarteira = bi.getDiaPermanenciaCarteira();
            this.mesPremanenciaCarteira = bi.getMesPermanenciaCarteira();
        }
        this.listDiasExecucao = listDiasExecucao;
        this.setConfirmados(bi.getConfirmados());
        this.setAguardandoConfirmacao(bi.getAguardandoConfirmacao());
     }

    public Integer getTotalAlunosAvencer() {
        return totalAlunosAvencer;
    }

    public void setTotalAlunosAvencer(Integer totalAlunosAvencer) {
        this.totalAlunosAvencer = totalAlunosAvencer;
    }

    public Integer getPercentualRenovacoes() {
        return percentualRenovacoes;
    }

    public void setPercentualRenovacoes(Integer percentualRenovacoes) {
        this.percentualRenovacoes = percentualRenovacoes;
    }

    public List<DiasSemanaDashboardBI> getListDiasExecucao() {
        return listDiasExecucao;
    }

    public void setListDiasExecucao(List<DiasSemanaDashboardBI> listDiasExecucao) {
        this.listDiasExecucao = listDiasExecucao;
    }

    public Integer getDiaPermanenciaCarteira() {
        return diaPermanenciaCarteira;
    }

    public void setDiaPermanenciaCarteira(Integer diaPermanenciaCarteira) {
        this.diaPermanenciaCarteira = diaPermanenciaCarteira;
    }

    public Integer getMesPremanenciaCarteira() {
        return mesPremanenciaCarteira;
    }

    public void setMesPremanenciaCarteira(Integer mesPremanenciaCarteira) {
        this.mesPremanenciaCarteira = mesPremanenciaCarteira;
    }

    public MicroCharts getMicro() {
        return micro;
    }

    public void setMicro(MicroCharts micro) {
        this.micro = micro;
    }

    public List<TipoEventoDisponibilidadeJSON> getListaTipos() {
        return listaTipos;
    }

    public void setListaTipos(List<TipoEventoDisponibilidadeJSON> listaTipos) {
        this.listaTipos = listaTipos;
    }

    public Integer getProfessores() {
        return professores;
    }

    public void setProfessores(Integer professores) {
        this.professores = professores;
    }

    public Integer getHorasDisponibilidade() {
        return horasDisponibilidade;
    }

    public void setHorasDisponibilidade(Integer horasDisponibilidade) {
        this.horasDisponibilidade = horasDisponibilidade;
    }

    public Integer getHorasAtendimento() {
        return horasAtendimento;
    }

    public void setHorasAtendimento(Integer horasAtendimento) {
        this.horasAtendimento = horasAtendimento;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getNovosTreinos() {
        return novosTreinos;
    }

    public void setNovosTreinos(Integer novosTreinos) {
        this.novosTreinos = novosTreinos;
    }

    public Integer getTreinosRevisados() {
        return treinosRevisados;
    }

    public void setTreinosRevisados(Integer treinosRevisados) {
        this.treinosRevisados = treinosRevisados;
    }

    public Integer getTreinosRenovados() {
        return treinosRenovados;
    }

    public void setTreinosRenovados(Integer treinosRenovados) {
        this.treinosRenovados = treinosRenovados;
    }

    public Integer getAvaliacoesFisicas() {
        return avaliacoesFisicas;
    }

    public void setAvaliacoesFisicas(Integer avaliacoesFisicas) {
        this.avaliacoesFisicas = avaliacoesFisicas;
    }


    public Integer getPercentualAvaliacoes() {
        return percentualAvaliacoes;
    }

    public void setPercentualAvaliacoes(Integer percentualAvaliacoes) {
        this.percentualAvaliacoes = percentualAvaliacoes;
    }


    public Integer getTempoMedioPermanenciaTreino() {
        return tempoMedioPermanenciaTreino;
    }

    public void setTempoMedioPermanenciaTreino(Integer tempoMedioPermanenciaTreino) {
        this.tempoMedioPermanenciaTreino = tempoMedioPermanenciaTreino;
    }

    public Integer getTotalAlunosSemTreino() {
        return totalAlunosSemTreino;
    }

    public void setTotalAlunosSemTreino(Integer totalAlunosSemTreino) {
        this.totalAlunosSemTreino = totalAlunosSemTreino;
    }

    public Double getPercUtilizamApp() {
        return percUtilizamApp;
    }

    public void setPercUtilizamApp(Double percUtilizamApp) {
        this.percUtilizamApp = percUtilizamApp;
    }

    public Integer getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(Integer totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public Integer getTotalAlunosAtivos() {
        return totalAlunosAtivos;
    }

    public void setTotalAlunosAtivos(Integer totalAlunosAtivos) {
        this.totalAlunosAtivos = totalAlunosAtivos;
    }

    public Integer getTotalAlunosInativos() {
        return totalAlunosInativos;
    }

    public void setTotalAlunosInativos(Integer totalAlunosInativos) {
        this.totalAlunosInativos = totalAlunosInativos;
    }

    public Integer getPercentualEmDia() {
        return percentualEmDia;
    }

    public void setPercentualEmDia(Integer percentualEmDia) {
        this.percentualEmDia = percentualEmDia;
    }

    public Integer getTotalTreinosVencidos() {
        return totalTreinosVencidos;
    }

    public void setTotalTreinosVencidos(Integer totalTreinosVencidos) {
        this.totalTreinosVencidos = totalTreinosVencidos;
    }

    public Integer getTotalTreinosRenovar() {
        return totalTreinosRenovar;
    }

    public void setTotalTreinosRenovar(Integer totalTreinosRenovar) {
        this.totalTreinosRenovar = totalTreinosRenovar;
    }

    public Integer getTotalTreinosEmdia() {
        return totalTreinosEmdia;
    }

    public void setTotalTreinosEmdia(Integer totalTreinosEmdia) {
        this.totalTreinosEmdia = totalTreinosEmdia;
    }

    public Integer getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(Integer agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getCompareceram() {
        return compareceram;
    }

    public void setCompareceram(Integer compareceram) {
        this.compareceram = compareceram;
    }

    public Integer getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Integer faltaram) {
        this.faltaram = faltaram;
    }

    public Integer getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Integer cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Integer getNr5estrelas() {
        return nr5estrelas;
    }

    public void setNr5estrelas(Integer nr5estrelas) {
        this.nr5estrelas = nr5estrelas;
    }

    public Integer getNr4estrelas() {
        return nr4estrelas;
    }

    public void setNr4estrelas(Integer nr4estrelas) {
        this.nr4estrelas = nr4estrelas;
    }

    public Integer getNr3estrelas() {
        return nr3estrelas;
    }

    public void setNr3estrelas(Integer nr3estrelas) {
        this.nr3estrelas = nr3estrelas;
    }

    public Integer getNr2estrelas() {
        return nr2estrelas;
    }

    public void setNr2estrelas(Integer nr2estrelas) {
        this.nr2estrelas = nr2estrelas;
    }

    public Integer getNr1estrelas() {
        return nr1estrelas;
    }

    public void setNr1estrelas(Integer nr1estrelas) {
        this.nr1estrelas = nr1estrelas;
    }

    public Integer getTotalAlunosComTreino() {
        return totalAlunosComTreino;
    }

    public void setTotalAlunosComTreino(Integer totalAlunosComTreino) {
        this.totalAlunosComTreino = totalAlunosComTreino;
    }

    public Integer getTotalAlunosVisitantes() {
        return totalAlunosVisitantes;
    }

    public void setTotalAlunosVisitantes(Integer totalAlunosVisitantes) {
        this.totalAlunosVisitantes = totalAlunosVisitantes;
    }

    public Integer getConfirmados() {
        return confirmados;
    }

    public void setConfirmados(Integer confirmados) {
        this.confirmados = confirmados;
    }

    public Integer getAguardandoConfirmacao() {
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Integer aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }
}
