package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.avaliacao.AvaliacaoIntegradaDTOUptade;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoIntegradaService;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaAvaliacaoIntegradaDTO;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaListAvaliacaoIntegradaDTO;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaInteger;
import br.com.pacto.swagger.respostas.ExemploRespostaString;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaPDFAvaliacaoIntegradaIndividual;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaPDFAvaliacaoIntegradaGrupo;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaBoolean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.logging.Level;
import java.util.logging.Logger;

@Api(tags = "Avaliação Física")
@Controller
@RequestMapping("/psec/avaliacao-integrada")
public class AvaliacaoIntegradaController extends SuperControle {

    private final SessaoService sessaoService;
    private final AvaliacaoIntegradaService avaliacaoIntegradaService;

    @Autowired
    private ServletContext sc;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    @Autowired
    public AvaliacaoIntegradaController(SessaoService sessaoService,
                                        AvaliacaoIntegradaService avaliacaoIntegradaService) {
        this.sessaoService = sessaoService;
        this.avaliacaoIntegradaService = avaliacaoIntegradaService;
    }

    @ApiOperation(
            value = "Consultar todas as avaliações integradas do aluno",
            notes = "Retorna uma lista completa com todas as avaliações integradas realizadas pelo aluno especificado. A avaliação integrada combina testes de mobilidade, estabilidade e qualidade de vida, fornecendo uma visão holística da condição física do aluno.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista de avaliações integradas do aluno)", response = ExemploRespostaListAvaliacaoIntegradaDTO.class),
    })
    @ResponseBody
    @RequestMapping(value = "/aluno/{id}/all-av-integradas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> allByAluno(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.allByAluno(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível obter as avaliações integradas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar avaliação integrada por identificador",
            notes = "Retorna os dados completos de uma avaliação integrada específica através do seu identificador único. Inclui resultados de mobilidade, estabilidade, qualidade de movimento e qualidade de vida, além das respostas da anamnese.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação integrada encontrada)", response = ExemploRespostaAvaliacaoIntegradaDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findAvaliacaoIntegradaById(
            @ApiParam(value = "Identificador único da avaliação integrada", defaultValue = "456", required = true)
            @PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.findAvIntegradaById(ctx, id));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível obter a avaliação integrada", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Criar ou atualizar avaliação integrada do aluno",
            notes = "Cria uma nova avaliação integrada ou atualiza uma existente para o aluno especificado. A avaliação integrada inclui testes de mobilidade (cadeia anterior, posterior, lateral, rotacional) e estabilidade (controle, fechamento, abertura), além de anamnese específica. O sistema calcula automaticamente as classificações de qualidade de movimento e vida.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação integrada salva com sucesso)", response = ExemploRespostaInteger.class),
    })
    @ResponseBody
    @RequestMapping(value = "/aluno/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable(value = "id") Integer id,
            @ApiParam(value = "Dados da avaliação integrada a ser criada ou atualizada", required = true)
            @RequestBody AvaliacaoIntegradaDTOUptade avaliacaoIntegrada) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.saveOrUpdate(ctx, id, idUsuario, avaliacaoIntegrada));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível salvar a avaliaçõe integrada", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Excluir avaliação integrada",
            notes = "Remove permanentemente uma avaliação integrada do sistema. Esta operação não pode ser desfeita e remove todos os dados associados, incluindo resultados de mobilidade, estabilidade e respostas da anamnese.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Avaliação integrada excluída com sucesso)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> delete(
            @ApiParam(value = "Identificador único da avaliação integrada a ser excluída", defaultValue = "456", required = true)
            @PathVariable(value = "id") Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            avaliacaoIntegradaService.delete(ctx, id);
            return ResponseEntityFactory.ok(true);
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível excluir a avaliaçõe integrada", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Gerar relatório PDF individual da avaliação integrada",
            notes = "Gera um relatório completo em PDF de uma avaliação integrada específica, incluindo resultados de mobilidade, estabilidade, classificações de qualidade de movimento e vida, gráficos e recomendações personalizadas.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (URL do relatório PDF gerado)", response = ExemploRespostaPDFAvaliacaoIntegradaIndividual.class),
    })
    @ResponseBody
    @RequestMapping(value = "/{id}/print-individual-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> printIndividualAvIntegrada(
            @ApiParam(value = "Identificador único da avaliação integrada", defaultValue = "456", required = true)
            @PathVariable("id") final Integer id,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.printIndividualAvIntegrada(ctx, id, idUsuario, request));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível gerar o pdf da avaliação integrada individual", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Gerar relatório PDF em grupo de avaliações integradas",
            notes = "Gera um relatório consolidado em PDF contendo múltiplas avaliações integradas. O relatório inclui comparativos entre as avaliações, evolução dos resultados de mobilidade e estabilidade, e análise da progressão da qualidade de movimento e vida ao longo do tempo.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (URL do relatório PDF em grupo gerado)", response = ExemploRespostaPDFAvaliacaoIntegradaGrupo.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{codigos}/print-group-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> printGroupAvIntegrada(
            @ApiParam(value = "Códigos das avaliações integradas separados por vírgula para geração do relatório em grupo", defaultValue = "456,789,123", required = true)
            @PathVariable("codigos") final String codigos,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            return ResponseEntityFactory.ok(avaliacaoIntegradaService.printGroupAvIntegrada(ctx, idUsuario, codigos, request));
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível gerar o pdf da avaliação integrada em grupo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Enviar relatório individual da avaliação integrada por email",
            notes = "Envia por email o relatório completo de uma avaliação integrada específica para o aluno. O email é enviado para os endereços cadastrados do aluno e inclui o relatório em PDF com todos os resultados e recomendações.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Email enviado com sucesso)", response = ExemploRespostaBoolean.class),
    })
    @ResponseBody
    @RequestMapping(value = "/aluno/{idAluno}/{idAvaliacao}/send-individual-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sendIndividualAvIntegrada(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable(value = "idAluno") Integer idAluno,
            @ApiParam(value = "Identificador único da avaliação integrada", defaultValue = "456", required = true)
            @PathVariable(value = "idAvaliacao") Integer idAvaliacao,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            avaliacaoIntegradaService.sendIndividualAvIntegrada(ctx, idAluno, idAvaliacao, idUsuario, request, sc);
            return ResponseEntityFactory.ok(true);
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível enviar a avaliação integrada por email", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Enviar relatório em grupo de avaliações integradas por email",
            notes = "Envia por email um relatório consolidado contendo múltiplas avaliações integradas do aluno. O email é enviado para os endereços cadastrados do aluno e inclui o relatório em PDF com comparativos entre as avaliações, evolução dos resultados e análise da progressão ao longo do tempo.",
            tags = "Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Email com relatório em grupo enviado com sucesso)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @RequestMapping(value = "/aluno/{idAluno}/{idAvaliacoes}/send-grupo-av-integrada", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sendGrupoAvIntegrada(
            @ApiParam(value = "Identificador único do aluno", defaultValue = "12345", required = true)
            @PathVariable(value = "idAluno") Integer idAluno,
            @ApiParam(value = "Identificadores das avaliações integradas separados por vírgula", defaultValue = "456,789,123", required = true)
            @PathVariable(value = "idAvaliacoes") String idAvaliacoes,
            HttpServletRequest request) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Integer idUsuario = sessaoService.getUsuarioAtual().getId();
            avaliacaoIntegradaService.sendGrupoAvIntegrada(ctx, idAluno, idAvaliacoes, idUsuario, request, sc);
            return ResponseEntityFactory.ok(true);
        } catch (Exception e) {
            Logger.getLogger(AvaliacaoFisicaController.class.getName()).log(Level.SEVERE, "Não foi possível enviar as avaliações integradas por email", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

}
