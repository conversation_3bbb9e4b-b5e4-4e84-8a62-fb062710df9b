package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do cliente exibidas na lista rápida de acesso.")
public class ListaRapidaAcessoDTO {

    private String idMemcached;
    @ApiModelProperty(value = "Código do cliente.", example = "12345")
    private Integer codigoCliente;

    @ApiModelProperty(value = "Matrícula do cliente.", example = "67890")
    private Integer matricula;

    @ApiModelProperty(value = "Horário previsto ou real de acesso.", example = "08:30")
    private String hora;

    @ApiModelProperty(value = "Nome do cliente.", example = "João <PERSON>")
    private String nome;

    @ApiModelProperty(value = "Indica se o cliente possui o PAR-Q.", example = "true")
    private boolean parq;

    @ApiModelProperty(value = "Informação sobre a pendência do PAR-Q assinado.")
    private PendenciaAcessoDTO parqAssinado;

    @ApiModelProperty(value = "Nome do plano do cliente.", example = "Plano Mensal")
    private String plano;

    @ApiModelProperty(value = "URL da foto do cliente.", example = "https://dominio.com/imagens/cliente.jpg")
    private String foto;

    @ApiModelProperty(value = "Cor de destaque para exibição na lista.", example = "#FF0000")
    private String cor;

    @ApiModelProperty(value = "Indica se é aniversariante do dia.", example = "false")
    private boolean aniversariante;

    @ApiModelProperty(value = "Indica se o item é um divisor visual na lista.", example = "false")
    private boolean divisor;

    @ApiModelProperty(value = "Lista de avisos de pendências vinculadas ao cliente.")
    private List<PendenciaAcessoDTO> avisos = new ArrayList<>();

    @ApiModelProperty(value = "Aviso principal ou mais crítico do cliente.")
    private PendenciaAcessoDTO aviso;

    @ApiModelProperty(value = "Situação do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)\n", example = "AT")
    private String situacao;


    @ApiModelProperty(value = "Situação do contrato.\n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NO - Normal\n" +
            "- AV - A vencer\n" +
            "- ATM - Atestado Médico\n" +
            "- CR - Carência\n" +
            "- CA - Cancelado\n" +
            "- DE - Desistente\n" +
            "- VE - Inativo Vencido\n" +
            "- TV - Trancado Vencido\n" +
            "- DI - Diária\n" +
            "- PE - Free Pass\n" +
            "- AA - Aula Avulsa", example = "NO")
    private String situacaoContrato;

    @ApiModelProperty(value = "Indica se o cliente está associado ao Gympass.", example = "false")
    private boolean gympass;

    @ApiModelProperty(value = "Indica se o cliente está associado ao TotalPass.", example = "false")
    private boolean totalpass;

    @ApiModelProperty(value = "Indica se o cliente possui diária ativa.", example = "false")
    private boolean diaria;

    @ApiModelProperty(value = "Indica se o cliente possui Free Pass.", example = "false")
    private boolean freepass;

    @ApiModelProperty(value = "Indica se o cliente possui Aula Avulsa ativa.", example = "false")
    private boolean aulaAvulsa;


    public void addPendencia(PendenciasAcessoEnum pendencia) {
        PendenciaAcessoDTO dto = new PendenciaAcessoDTO();
        dto.setChave(pendencia.name());
        dto.setDescricao(pendencia.getDescricao());
        if(pendencia.equals(PendenciasAcessoEnum.PARQ_NEGATIVO) || pendencia.equals(PendenciasAcessoEnum.PARQ_NAO_RESPONDIDO)
                || pendencia.equals(PendenciasAcessoEnum.PARQ_POSITIVO)){
            parqAssinado = dto;
        } else if(aviso == null){
            aviso = dto;
        } else {
            avisos.add(dto);
        }
    }

    public boolean isDivisor() {
        return divisor;
    }

    public void setDivisor(boolean divisor) {
        this.divisor = divisor;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isParq() {
        return parq;
    }

    public void setParq(boolean parq) {
        this.parq = parq;
    }

    public PendenciaAcessoDTO getParqAssinado() {
        return parqAssinado;
    }

    public void setParqAssinado(PendenciaAcessoDTO parqAssinado) {
        this.parqAssinado = parqAssinado;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public boolean isAniversariante() {
        return aniversariante;
    }

    public void setAniversariante(boolean aniversariante) {
        this.aniversariante = aniversariante;
    }


    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public List<PendenciaAcessoDTO> getAvisos() {
        return avisos;
    }

    public void setAvisos(List<PendenciaAcessoDTO> avisos) {
        this.avisos = avisos;
    }

    public PendenciaAcessoDTO getAviso() {
        return aviso;
    }

    public void setAviso(PendenciaAcessoDTO aviso) {
        this.aviso = aviso;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public boolean isGympass() {
        return gympass;
    }

    public void setGympass(boolean gympass) {
        this.gympass = gympass;
    }

    public boolean isTotalpass() {
        return totalpass;
    }

    public void setTotalpass(boolean totalpass) {
        this.totalpass = totalpass;
    }

    public boolean isDiaria() {
        return diaria;
    }

    public void setDiaria(boolean diaria) {
        this.diaria = diaria;
    }

    public boolean isFreepass() {
        return freepass;
    }

    public void setFreepass(boolean freepass) {
        this.freepass = freepass;
    }

    public boolean isAulaAvulsa() {
        return aulaAvulsa;
    }

    public void setAulaAvulsa(boolean aulaAvulsa) {
        this.aulaAvulsa = aulaAvulsa;
    }

    public String getIdMemcached() {
        return idMemcached;
    }

    public void setIdMemcached(String idMemcached) {
        this.idMemcached = idMemcached;
    }
}
