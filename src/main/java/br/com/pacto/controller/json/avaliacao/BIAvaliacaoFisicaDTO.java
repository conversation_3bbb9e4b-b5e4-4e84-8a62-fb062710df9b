package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.service.impl.avaliacao.BIAvaliacaoFisicaTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Collection;

/**
 * <AUTHOR> Si<PERSON> 16/01/2019
 */
@ApiModel(description = "Dados consolidados de Business Intelligence para avaliações físicas, contendo indicadores estatísticos e gráficos de acompanhamento.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BIAvaliacaoFisicaDTO {

    @ApiModelProperty(value = "Número total de avaliações físicas realizadas no período.", example = "245")
    private Integer avaliacoes;

    @ApiModelProperty(value = "Número de avaliações físicas novas (primeira avaliação do aluno).", example = "68")
    private Integer novas;

    @ApiModelProperty(value = "Número de reavaliações físicas realizadas no período.", example = "177")
    private Integer reavaliacoes;

    @ApiModelProperty(value = "Número de avaliações físicas previstas para o período.", example = "280")
    private Integer previstas;

    @ApiModelProperty(value = "Número de avaliações físicas efetivamente realizadas.", example = "245")
    private Integer realizadas;

    @ApiModelProperty(value = "Número de avaliações físicas em atraso (não realizadas na data prevista).", example = "35")
    private Integer atrasadas;

    @ApiModelProperty(value = "Número de avaliações físicas agendadas para datas futuras.", example = "42")
    private Integer futuras;

    @ApiModelProperty(value = "Número de alunos ativos que não possuem avaliação física.", example = "18")
    private Integer semAvaliacao;

    @ApiModelProperty(value = "Número de alunos ativos com avaliação física em atraso.", example = "25")
    private Integer ativosAtrasada;

    @ApiModelProperty(value = "Número de alunos que perderam peso na última reavaliação.", example = "89")
    private Integer perderamPeso;

    @ApiModelProperty(value = "Número de alunos que ganharam massa magra na última reavaliação.", example = "156")
    private Integer ganharamMassaMagra;

    @ApiModelProperty(value = "Número de alunos que perderam percentual de gordura na última reavaliação.", example = "134")
    private Integer perderamGordura;

    @ApiModelProperty(value = "Número de alunos com resultado positivo no questionário PAR-Q.", example = "12")
    private Integer alunosParq;

    @ApiModelProperty(value = "Coleção de dados para geração de gráficos de acompanhamento das avaliações físicas.")
    private Collection<GraficoDTO> grafico;

    public BIAvaliacaoFisicaDTO(final BIAvaliacaoFisicaTO obj, final Collection<GraficoDTO> grafico) {
        this.avaliacoes = obj.getAvaliacoes();
        this.novas = obj.getNovas();
        this.reavaliacoes = obj.getReavaliacoes();
        this.previstas = obj.getPrevistas();
        this.realizadas = obj.getRealizadas();
        this.atrasadas = obj.getAtrasadas();
        this.futuras = obj.getFuturas();
        this.semAvaliacao = obj.getSemAvaliacao();
        this.ativosAtrasada = obj.getAtivosAtrasada();
        this.perderamPeso = obj.getPerderamPeso();
        this.ganharamMassaMagra = obj.getGanharamMassaMagra();
        this.perderamGordura = obj.getPerderamGordura();
        this.alunosParq = obj.getAlunosParq();
        this.grafico = grafico;
    }

    public Integer getAvaliacoes() {
        return avaliacoes;
    }

    public void setAvaliacoes(Integer avaliacoes) {
        this.avaliacoes = avaliacoes;
    }

    public Integer getNovas() {
        return novas;
    }

    public void setNovas(Integer novas) {
        this.novas = novas;
    }

    public Integer getReavaliacoes() {
        return reavaliacoes;
    }

    public void setReavaliacoes(Integer reavaliacoes) {
        this.reavaliacoes = reavaliacoes;
    }

    public Integer getPrevistas() {
        return previstas;
    }

    public void setPrevistas(Integer previstas) {
        this.previstas = previstas;
    }

    public Integer getRealizadas() {
        return realizadas;
    }

    public void setRealizadas(Integer realizadas) {
        this.realizadas = realizadas;
    }

    public Integer getAtrasadas() {
        return atrasadas;
    }

    public void setAtrasadas(Integer atrasadas) {
        this.atrasadas = atrasadas;
    }

    public Integer getFuturas() {
        return futuras;
    }

    public void setFuturas(Integer futuras) {
        this.futuras = futuras;
    }

    public Integer getSemAvaliacao() {
        return semAvaliacao;
    }

    public void setSemAvaliacao(Integer semAvaliacao) {
        this.semAvaliacao = semAvaliacao;
    }

    public Integer getAtivosAtrasada() {
        return ativosAtrasada;
    }

    public void setAtivosAtrasada(Integer ativosAtrasada) {
        this.ativosAtrasada = ativosAtrasada;
    }

    public Integer getPerderamPeso() {
        return perderamPeso;
    }

    public void setPerderamPeso(Integer perderamPeso) {
        this.perderamPeso = perderamPeso;
    }

    public Integer getGanharamMassaMagra() {
        return ganharamMassaMagra;
    }

    public void setGanharamMassaMagra(Integer ganharamMassaMagra) {
        this.ganharamMassaMagra = ganharamMassaMagra;
    }

    public Integer getPerderamGordura() {
        return perderamGordura;
    }

    public void setPerderamGordura(Integer perderamGordura) {
        this.perderamGordura = perderamGordura;
    }

    public Integer getAlunosParq() {
        return alunosParq;
    }

    public void setAlunosParq(Integer alunosParq) {
        this.alunosParq = alunosParq;
    }

    public Collection<GraficoDTO> getGrafico() {
        return grafico;
    }

    public void setGrafico(Collection<GraficoDTO> grafico) {
        this.grafico = grafico;
    }

    @ApiModel(description = "Dados para geração de gráficos de acompanhamento das avaliações físicas, contendo pares de nome-valor para visualização estatística.")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GraficoDTO {

        @ApiModelProperty(value = "Nome/rótulo do ponto de dados no gráfico (ex: nome do objetivo, período).", example = "Emagrecimento")
        private String nome;

        @ApiModelProperty(value = "Valor numérico correspondente ao ponto de dados no gráfico.", example = "45")
        private Integer valor;

        public GraficoDTO(String nome, Integer valor) {
            this.nome = nome;
            this.valor = valor;
        }

        public String getNome() {
            return nome;
        }

        public void setNome(String nome) {
            this.nome = nome;
        }

        public Integer getValor() {
            return valor;
        }

        public void setValor(Integer valor) {
            this.valor = valor;
        }
    }
}
