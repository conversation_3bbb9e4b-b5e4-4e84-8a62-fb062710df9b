package br.com.pacto.controller.json.locacao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Tipo de horário de locação com código e descrição.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TipoHorarioLocacaoDTO {

    @ApiModelProperty(value = "Código do tipo de horário. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- 0 (LIVRE)\n" +
            "- 1 (PLAY)\n" +
            "- 2 (PRE_DEFINIDO)\n", example = "2")
    private Integer codigo;

    @ApiModelProperty(value = "Descrição do tipo de horário.", example = "Pré-definido")
    private String descricao;

    public TipoHorarioLocacaoDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
