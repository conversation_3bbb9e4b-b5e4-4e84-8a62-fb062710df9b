package br.com.pacto.controller.json.turma;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para cadastro e alteração de nível de turma")
public class NivelTurmaDTO {

    @ApiModelProperty(value = "Código único identificador do nível", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do nível da turma", example = "Intermediário")
    private String  nome;
    @ApiModelProperty(value = "Código MGB do nível", example = "MGB001")
    private String  codigoMgb;

    public NivelTurmaDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCodigoMgb() {
        return codigoMgb;
    }

    public void setCodigoMgb(String codigoMgb) {
        this.codigoMgb = codigoMgb;
    }
}
