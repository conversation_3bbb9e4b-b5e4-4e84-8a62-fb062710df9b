package br.com.pacto.controller.json.objetivos;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.programa.ObjetivoPredefinido;
import br.com.pacto.bean.programa.ObjetivoPredefinidoDTO;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import br.com.pacto.swagger.respostas.objetivos.ExemploRespostaListObjetivoPredefinidoDTO;
import br.com.pacto.swagger.respostas.objetivos.ExemploRespostaListObjetivoPredefinidoDTOPaginacao;
import br.com.pacto.swagger.respostas.objetivos.ExemploRespostaObjetivoPredefinidoDTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/avaliacao-objetivos")
public class ObjetivosPredefinidosController {

    private final SessaoService sessaoService;
    private final ObjetivoPredefinidoService objService;

    @Autowired
    public ObjetivosPredefinidosController(ObjetivoPredefinidoService objService, SessaoService sessaoService) {
        Assert.notNull(objService, "O serviço de objetivos não foi injetado corretamente");
        Assert.notNull(sessaoService, "O serviço de sessaoService não foi injetado corretamente");
        this.objService = objService;
        this.sessaoService = sessaoService;
    }

    @ApiOperation(value = "Consultar objetivos predefinidos com paginação",
            notes = "Retorna uma lista paginada de objetivos predefinidos com possibilidade de filtro por nome",
            tags = "Objetivos")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Consulta realizada com sucesso", response = ExemploRespostaListObjetivoPredefinidoDTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamneses(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do objetivo predefinido.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Perda de Peso\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroObjetivosJSON filters = new FiltroObjetivosJSON(filtros);
            return ResponseEntityFactory.ok(objService.consultar(filters.getParametro(), paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os objetivos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Excluir objetivo predefinido",
            notes = "Remove um objetivo predefinido do sistema através do seu ID",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo predefinido excluído com sucesso", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluir(
            @ApiParam(value = "Código identificador único do objetivo predefinido a ser excluído", required = true, defaultValue = "1")
            @PathVariable("id") final Integer id) {
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();
            objService.excluir(chave, objService.obterPorId(chave, id));
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }


    @ApiOperation(value = "Consultar objetivo predefinido por ID",
            notes = "Retorna os dados de um objetivo predefinido específico através do seu ID",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo predefinido encontrado com sucesso", response = ExemploRespostaObjetivoPredefinidoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamnese(
            @ApiParam(value = "Código identificador único do objetivo predefinido", required = true, defaultValue = "1")
            @PathVariable("id") final Integer id) {
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();
            return ResponseEntityFactory.ok(new ObjetivoPredefinidoDTO(objService.obterPorId(chave, id)));
        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Incluir novo objetivo predefinido",
            notes = "Cria um novo objetivo predefinido no sistema",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo predefinido criado com sucesso", response = ExemploRespostaObjetivoPredefinidoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAnamneses(
            @ApiParam(value = "Dados do objetivo predefinido a ser criado", required = true)
            @RequestBody ObjetivoPredefinidoDTO obj) {
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();
            ObjetivoPredefinido objetivoPredefinido = new ObjetivoPredefinido();
            objetivoPredefinido.setNome(obj.getNome().trim());

            objetivoPredefinido = objService.inserir(chave, objetivoPredefinido);

            return ResponseEntityFactory.ok(new ObjetivoPredefinidoDTO(objetivoPredefinido));
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(value = "Alterar objetivo predefinido",
            notes = "Atualiza os dados de um objetivo predefinido existente",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Objetivo predefinido alterado com sucesso", response = ExemploRespostaObjetivoPredefinidoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAnamneses(
            @ApiParam(value = "Código identificador único do objetivo predefinido a ser alterado", required = true, defaultValue = "1")
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados atualizados do objetivo predefinido", required = true)
            @RequestBody ObjetivoPredefinidoDTO obj) {
        try {
            UsuarioSimplesDTO usuarioAtual = sessaoService.getUsuarioAtual();
            String chave = usuarioAtual.getChave();

            ObjetivoPredefinido objetivoPredefinido = objService.obterPorId(chave, id);
            objetivoPredefinido.setNome(obj.getNome().trim());
            objService.alterar(chave, objetivoPredefinido);

            return ResponseEntityFactory.ok(new ObjetivoPredefinidoDTO(objetivoPredefinido));

        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar objetivo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Obter todos os objetivos predefinidos",
            notes = "Retorna uma lista completa de todos os objetivos predefinidos disponíveis no sistema",
            tags = "Objetivos")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de objetivos predefinidos obtida com sucesso", response = ExemploRespostaListObjetivoPredefinidoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/todos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasAnamneses() {
        try {
            return ResponseEntityFactory.ok(objService.obterTodos());
        } catch (ServiceException e) {
            Logger.getLogger(ObjetivosPredefinidosController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamneses", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


}
