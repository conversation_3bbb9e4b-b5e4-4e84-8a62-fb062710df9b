package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações de uma aula confirmada pelo aluno")
public class AulaConfirmadaVO {

   @ApiModelProperty(value = "Nome completo do professor responsável pela aula", example = "<PERSON> Silva")
   private String nomeProfessor;

   @ApiModelProperty(value = "Nome ou descrição da aula/modalidade", example = "Spinning Avançado")
   private String nomeAula;

   @ApiModelProperty(value = "Data da aula no formato dd/MM/yyyy", example = "15/01/2024")
   private String dataAula;

    public String getNomeProfessor() {
        return nomeProfessor;
    }

    public void setNomeProfessor(String nomeProfessor) {
        this.nomeProfessor = nomeProfessor;
    }

    public String getNomeAula() {
        return nomeAula;
    }

    public void setNomeAula(String nomeAula) {
        this.nomeAula = nomeAula;
    }

    public String getDataAula() {
        return dataAula;
    }

    public void setDataAula(String dataAula) {
        this.dataAula = dataAula;
    }
}
