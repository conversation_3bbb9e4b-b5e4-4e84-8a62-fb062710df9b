package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.AcessosExecucoesBI;
import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.DiasSemanaDashboardBI;
import br.com.pacto.bean.bi.ItemMediaBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApiModel(description = "DTO contendo dados de Business Intelligence relacionados aos treinamentos, incluindo estatísticas de alunos, programas e execuções.")
public class BITreinoTreinamentoDTO {

    @ApiModelProperty(value = "Total de alunos ativos que possuem treino ativo.", example = "85")
    private Integer alunosAtivosComTreino;

    @ApiModelProperty(value = "Total de alunos ativos com programa de treino em dia.", example = "62")
    private Integer alunosAtivosProgramaEmDia;

    @ApiModelProperty(value = "Total de alunos com programa de treino que precisa ser renovado.", example = "15")
    private Integer alunosProgramaRenovar;

    @ApiModelProperty(value = "Total de alunos com programa de treino vencido.", example = "8")
    private Integer alunosProgramaVencidos;

    @ApiModelProperty(value = "Total de alunos ativos sem treino ativo.", example = "12")
    private Integer alunosAtivosSemTreino;

    @ApiModelProperty(value = "Percentual de treinos que estão em dia.", example = "78")
    private Integer porcentagemTreinosEmDia;

    @ApiModelProperty(value = "Total de treinos que precisam ser renovados em 30 dias.", example = "15")
    private Integer treinosRenovarEm30Dias;

    @ApiModelProperty(value = "Dados de tempo de permanência no programa de treino.")
    private TempoPermanenciaDTO tempoPermanenciaPrograma = new TempoPermanenciaDTO();

    @ApiModelProperty(value = "Dados de média de execução por dias da semana.")
    private BITreinoDiaSemanaExecucaoDTO mediaExecucao = new BITreinoDiaSemanaExecucaoDTO();

    @ApiModelProperty(value = "Mapa de acessos e execuções de treino por data.")
    private Map<Long, ExecucoesTreinoDTO> acessoExecucoesZW = new HashMap<>();

    public BITreinoTreinamentoDTO() {
    }

    public BITreinoTreinamentoDTO(DashboardBI dash,
                                  List<DiasSemanaDashboardBI> dias,
                                  List<AcessosExecucoesBI> listaAcessosBi,
                                  ItemMediaBI maior,
                                  ItemMediaBI menor) {
        this.treinosRenovarEm30Dias = dash.getTotalTreinosRenovar();
        this.alunosAtivosComTreino = dash.getTotalAlunosTreino();
        this.alunosAtivosProgramaEmDia = dash.getTotalTreinosEmdia();
        this.alunosProgramaRenovar = dash.getTotalTreinosRenovar();
        this.alunosProgramaVencidos = dash.getTotalTreinosVencidos();
        this.alunosAtivosSemTreino = dash.getTotalAlunosSemTreino();
        this.porcentagemTreinosEmDia = dash.getPercentualEmDia();
        this.tempoPermanenciaPrograma = new TempoPermanenciaDTO(dash, maior, menor);
        this.mediaExecucao = new BITreinoDiaSemanaExecucaoDTO(dias);
        montarMapaExecucoes(listaAcessosBi);
    }

    public BITreinoTreinamentoDTO(DashboardBI dash,
                                  List<DiasSemanaDashboardBI> dias,
                                  List<AcessosExecucoesBI> listaAcessosBi,
                                  ItemMediaBI maior,
                                  ItemMediaBI menor, List<TreinoRealizadoAppDTO> treinosRealizados) {
        this.treinosRenovarEm30Dias = dash.getTotalTreinosRenovar();
        this.alunosAtivosComTreino = dash.getTotalAlunosTreino();
        this.alunosAtivosProgramaEmDia = dash.getTotalTreinosEmdia();
        this.alunosProgramaRenovar = dash.getTotalTreinosRenovar();
        this.alunosProgramaVencidos = dash.getTotalTreinosVencidos();
        this.alunosAtivosSemTreino = dash.getTotalAlunosSemTreino();
        this.porcentagemTreinosEmDia = dash.getPercentualEmDia();
        this.tempoPermanenciaPrograma = new TempoPermanenciaDTO(dash, maior, menor);
        this.mediaExecucao = new BITreinoDiaSemanaExecucaoDTO(dias, treinosRealizados);
        montarMapaExecucoes(listaAcessosBi);
    }

    private void montarMapaExecucoes(List<AcessosExecucoesBI> listaAcessosBi){
        for(AcessosExecucoesBI a : listaAcessosBi){
            acessoExecucoesZW.put(a.getDia().getTime(), new ExecucoesTreinoDTO(a));
        }
    }

    public Map<Long, ExecucoesTreinoDTO> getAcessoExecucoesZW() {
        return acessoExecucoesZW;
    }

    public void setAcessoExecucoesZW(Map<Long, ExecucoesTreinoDTO> acessoExecucoesZW) {
        this.acessoExecucoesZW = acessoExecucoesZW;
    }

    public Integer getAlunosAtivosComTreino() {
        return alunosAtivosComTreino;
    }

    public void setAlunosAtivosComTreino(Integer alunosAtivosComTreino) {
        this.alunosAtivosComTreino = alunosAtivosComTreino;
    }

    public Integer getAlunosAtivosProgramaEmDia() {
        return alunosAtivosProgramaEmDia;
    }

    public void setAlunosAtivosProgramaEmDia(Integer alunosAtivosProgramaEmDia) {
        this.alunosAtivosProgramaEmDia = alunosAtivosProgramaEmDia;
    }

    public Integer getAlunosProgramaRenovar() {
        return alunosProgramaRenovar;
    }

    public void setAlunosProgramaRenovar(Integer alunosProgramaRenovar) {
        this.alunosProgramaRenovar = alunosProgramaRenovar;
    }

    public Integer getAlunosProgramaVencidos() {
        return alunosProgramaVencidos;
    }

    public void setAlunosProgramaVencidos(Integer alunosProgramaVencidos) {
        this.alunosProgramaVencidos = alunosProgramaVencidos;
    }

    public Integer getAlunosAtivosSemTreino() {
        return alunosAtivosSemTreino;
    }

    public void setAlunosAtivosSemTreino(Integer alunosAtivosSemTreino) {
        this.alunosAtivosSemTreino = alunosAtivosSemTreino;
    }

    public Integer getPorcentagemTreinosEmDia() {
        return porcentagemTreinosEmDia;
    }

    public void setPorcentagemTreinosEmDia(Integer porcentagemTreinosEmDia) {
        this.porcentagemTreinosEmDia = porcentagemTreinosEmDia;
    }

    public Integer getTreinosRenovarEm30Dias() {
        return treinosRenovarEm30Dias;
    }

    public void setTreinosRenovarEm30Dias(Integer treinosRenovarEm30Dias) {
        this.treinosRenovarEm30Dias = treinosRenovarEm30Dias;
    }

    public TempoPermanenciaDTO getTempoPermanenciaPrograma() {
        return tempoPermanenciaPrograma;
    }

    public void setTempoPermanenciaPrograma(TempoPermanenciaDTO tempoPermanenciaPrograma) {
        this.tempoPermanenciaPrograma = tempoPermanenciaPrograma;
    }

    public BITreinoDiaSemanaExecucaoDTO getMediaExecucao() {
        return mediaExecucao;
    }

    public void setMediaExecucao(BITreinoDiaSemanaExecucaoDTO mediaExecucao) {
        this.mediaExecucao = mediaExecucao;
    }
}
