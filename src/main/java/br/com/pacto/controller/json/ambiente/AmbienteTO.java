package br.com.pacto.controller.json.ambiente;

import br.com.pacto.bean.aula.Ambiente;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by <PERSON><PERSON> on 26/09/2018.
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do ambiente")
public class AmbienteTO {
    @ApiModelProperty(value = "Código único identificador do ambiente", example = "5")
    private Integer codigo;
    @ApiModelProperty(value = "Código único identificador do ambiente no ZW", example = "5")
    private Integer codigoZW;
    @ApiModelProperty(value = "Nome do ambiente que está sendo cadastrado", example = "PISCINA OLÍMPICA")
    private String nome;
    @ApiModelProperty(value = "Capacidade de pessoas no ambiente", example = "8")
    private Integer capacidade;

    public AmbienteTO() {}

    public AmbienteTO(Ambiente ambiente) {
        this.codigo = ambiente.getCodigo();
        this.codigoZW = ambiente.getCodigoZW();
        this.nome = ambiente.getNome();
        this.capacidade = ambiente.getCapacidade();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoZW() {
        return codigoZW;
    }

    public void setCodigoZW(Integer codigoZW) {
        this.codigoZW = codigoZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }
}
