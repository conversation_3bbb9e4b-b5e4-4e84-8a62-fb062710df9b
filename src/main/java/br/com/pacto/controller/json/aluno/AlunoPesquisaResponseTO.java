package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClientePesquisa;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados básicos do aluno retornados na pesquisa.")
public class AlunoPesquisaResponseTO {

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "123456")
    private Integer matricula;

    @ApiModelProperty(value = "Nome completo do aluno", example = "João da <PERSON>")
    private String nome;

    @ApiModelProperty(value = "URL da imagem do aluno", example = "https://arquivos.empresa.com/imagens/aluno123.jpg")
    private String imageUri;

    public AlunoPesquisaResponseTO() {
    }

    public AlunoPesquisaResponseTO(ClientePesquisa clientePesquisa) {
        this.matricula = clientePesquisa.getMatricula();
        this.nome = clientePesquisa.getNome();
        this.imageUri = clientePesquisa.getUrlFoto();
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }
}
