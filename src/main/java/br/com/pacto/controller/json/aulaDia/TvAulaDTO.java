package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.aluno.AlunoResponseTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeSimplesDTO;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.util.json.TurmaAulaCheiaJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by paulo on 22/10/2019.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações TV Aula")
public class TvAulaDTO implements Comparable<TvAulaDTO> {

    @ApiModelProperty(value = "Código identificador do horário de aula", example = "1")
    private Integer aulaHorarioId;
    @ApiModelProperty(value = "Nome da aula", example = "Aula Spinning")
    private String nome;
    @ApiModelProperty(value = "Mensagem da aula", example = "Aula de Spinning.")
    private String mensagem;
    @ApiModelProperty(value = "Ambiente que será utilizado para realização da aula")
    private AmbienteResponseTO ambiente;
    @ApiModelProperty(value = "Modalidade da aula")
    private ModalidadeSimplesDTO modalidade;
    @ApiModelProperty(value = "Professor responsável pela aula")
    private ColaboradorSimplesTO professor;
    @ApiModelProperty(value = "Horário de início da aula", example = "19:00")
    private String horarioInicio;
    @ApiModelProperty(value = "Horário de término da aula", example = "200:00")
    private String horarioFim;
    @ApiModelProperty(value = "Lista de alunos que irão participar da aula")
    private List<AlunoResponseTO> alunos;

    public TvAulaDTO() {
    }

    public TvAulaDTO(TurmaAulaCheiaJSON aulaCheia) {
        this.aulaHorarioId = aulaCheia.getHorarioCodigo();
        this.nome = aulaCheia.getNome();
        this.mensagem = aulaCheia.getMensagem();
        this.ambiente = new AmbienteResponseTO(aulaCheia.getAmbiente(), aulaCheia.getNomeAmbiente());
        getAmbiente().setCapacidade(aulaCheia.getCapacidade());
        this.modalidade = new ModalidadeSimplesDTO(aulaCheia.getModalidade(), aulaCheia.getNomeModalidade(), null);
        if (StringUtils.isNotBlank(aulaCheia.getModalidadeFotokey())) {
            getModalidade().setImageUri(Aplicacao.obterUrlFotoDaNuvem(aulaCheia.getModalidadeFotokey()));
        }
        String[] horarios = aulaCheia.getHorarios().split("-");
        this.horarioInicio = horarios[0].trim();
        this.horarioFim = horarios[1].trim();

    }

    public Integer getAulaHorarioId() {
        return aulaHorarioId;
    }

    public void setAulaHorarioId(Integer aulaHorarioId) {
        this.aulaHorarioId = aulaHorarioId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public AmbienteResponseTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteResponseTO ambiente) {
        this.ambiente = ambiente;
    }

    public ModalidadeSimplesDTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeSimplesDTO modalidade) {
        this.modalidade = modalidade;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public String getHorarioInicio() {
        return horarioInicio;
    }

    public void setHorarioInicio(String horarioInicio) {
        this.horarioInicio = horarioInicio;
    }

    public String getHorarioFim() {
        return horarioFim;
    }

    public void setHorarioFim(String horarioFim) {
        this.horarioFim = horarioFim;
    }

    public List<AlunoResponseTO> getAlunos() {
        if (alunos == null) {
            alunos = new ArrayList<>();
        }
        return alunos;
    }

    public void setAlunos(List<AlunoResponseTO> alunos) {
        this.alunos = alunos;
    }

    @Override
    public int compareTo(TvAulaDTO tvAula) {
        if (Calendario.getDataComHora(new Date(), getHorarioInicio()) == null || Calendario.getDataComHora(new Date(), tvAula.getHorarioInicio()) == null) {
            return 0;
        }
        return Calendario.getDataComHora(new Date(), getHorarioInicio()).compareTo(Calendario.getDataComHora(new Date(), tvAula.getHorarioInicio()));
    }
}
