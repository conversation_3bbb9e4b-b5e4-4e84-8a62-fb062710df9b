package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar indicadores de agenda por aluno, contendo informações sobre agendamentos específicos.")
public class IndicadorAgendaResponseDTO {

    @ApiModelProperty(value = "Nome do aluno agendado", example = "Maria Santos")
    private String nome;

    @ApiModelProperty(value = "Tipo de evento agendado", example = "Avaliação física")
    private String evento;

    @ApiModelProperty(value = "Horário do agendamento", example = "14:30")
    private String horario;

    @ApiModelProperty(value = "Situação atual do agendamento", example = "Confirmado")
    private String situacao;

    public IndicadorAgendaResponseDTO() {
        // Construtor padrão necessário para serialização
    }

    public IndicadorAgendaResponseDTO(Agendamento agendamento) {
        this.nome = agendamento.getNomeAluno();
        if (!UteisValidacao.emptyString(agendamento.getEvento())) {
            this.evento = agendamento.getEvento();
        } else if (agendamento.getHorarioDisponibilidade() != null) {
            try {
                // nova disponibilidade não possui tipo evento, utilizar comportamento
                Integer idComportamento = agendamento.getHorarioDisponibilidade().getDisponibilidade().getComportamento();
                switch (idComportamento) {
                    case 0:
                        this.evento = "Contato interpessoal";
                        break;
                    case 1:
                        this.evento = "Prescrição de treino";
                        break;
                    case 2:
                        this.evento = "Revisão de treino";
                        break;
                    case 3:
                        this.evento = "Renovar treino";
                        break;
                    case 4:
                        this.evento = "Avaliação física";
                        break;
                    default:
                        this.evento = "";
                }
            } catch (Exception e) {
                this.evento = "";
                Uteis.logar(e, IndicadorAgendaResponseDTO.class);
            }
        } else {
            this.evento = "";
        }
        this.horario = agendamento.getHorario();
        this.situacao = agendamento.getSituacao();
    }


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEvento() {
        return evento;
    }

    public void setEvento(String evento) {
        this.evento = evento;
    }

    public String getHorario() {
        return horario;
    }

    public void setHorario(String horario) {
        this.horario = horario;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}
