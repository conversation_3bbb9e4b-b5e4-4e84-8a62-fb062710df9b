package br.com.pacto.controller.json.gestao;

import br.com.pacto.controller.json.empresa.EmpresaDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude
@ApiModel(description = "Informações do colaborador vinculado a um cliente.")
public class ClienteColaboradoresDTO {

    @ApiModelProperty(value = "Nome do cliente", example = "João da Silva")
    private String nome;

    @ApiModelProperty(value = "Matrícula do cliente", example = "123456")
    private String matricula;

    @ApiModelProperty(value = "URL da foto do cliente", example = "https://cdn.exemplo.com/fotos/joao.jpg")
    private String urlFoto;

    @ApiModelProperty(value = "ID da pessoa vinculada", example = "1001")
    private Integer pessoa;

    @ApiModelProperty(value = "ID do cliente", example = "2001")
    private Integer cliente;

    @ApiModelProperty(value = "ID do colaborador", example = "3001")
    private Integer colaborador;

    @ApiModelProperty(value = "Situação do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)\n", example = "AT")
    private String situacaoCliente;

    @ApiModelProperty(value = "Situação do contrato.\n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NO - Normal\n" +
            "- AV - A vencer\n" +
            "- ATM - Atestado Médico\n" +
            "- CR - Carência\n" +
            "- CA - Cancelado\n" +
            "- DE - Desistente\n" +
            "- VE - Inativo Vencido\n" +
            "- TV - Trancado Vencido\n" +
            "- DI - Diária\n" +
            "- PE - Free Pass\n" +
            "- AA - Aula Avulsa", example = "NO")
    private String situacaoClienteContrato;

    @ApiModelProperty(value = "Situação do colaborador.\n\n" +
            "<strong>Valores disponíveis</strong>" +
            "- AT (Ativo)\n" +
            "- IN (Inativo)", example = "AT")
    private String situacaoColaborador;

    @ApiModelProperty(value = "Indica se possui Gympass", example = "true")
    private boolean gympass = false;

    @ApiModelProperty(value = "Indica se possui TotalPass", example = "false")
    private boolean totalpass = false;

    @ApiModelProperty(value = "Lista de tipos", example = "[\"Tipo1\", \"Tipo2\"]")
    private List<String> tipos;

    @ApiModelProperty(value = "Lista de e-mails do aluno", example = "[\"<EMAIL>\"]")
    private List<String> emails;

    @ApiModelProperty(value = "Lista de telefones do aluno", example = "[\"(11)99999-9999\"]")
    private List<String> telefones;

    @ApiModelProperty(value = "Situações adicionais do colaborador e aluno")
    private List<ClienteColaboradoresSituacaoDTO> situacao;

    @ApiModelProperty(value = "Lista de empresas associadas ao aluno")
    private List<EmpresaDTO> empresas;

    @ApiModelProperty(value = "Indica se há programa de treino vigente", example = "true")
    private boolean programaVigente;

    @ApiModelProperty(value = "Data de nascimento do aluno", example = "1988-05-20")
    private String dataNascimento;

    @ApiModelProperty(value = "Timestamp do último acesso", example = "1718030630000")
    private long dataUltimoAcesso;

    @ApiModelProperty(value = "Timestamp do início do programa de treino", example = "1716201600000")
    private long dataInicioProgramaTreino;

    @ApiModelProperty(value = "Timestamp do fim do programa de treino", example = "1718880000000")
    private long dataFimProgramaTreino;

    @ApiModelProperty(value = "Peso de risco calculado para o aluno", example = "2")
    private Integer pesoRisco;

    @ApiModelProperty(value = "Quantidade de acompanhamentos realizados", example = "5")
    private Integer qtdAcompanhamentos;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<ClienteColaboradoresSituacaoDTO> getSituacao() {
        return situacao;
    }

    public void setSituacao(List<ClienteColaboradoresSituacaoDTO> situacao) {
        this.situacao = situacao;
    }

    public List<EmpresaDTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<EmpresaDTO> empresas) {
        this.empresas = empresas;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Integer getColaborador() {
        return colaborador;
    }

    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    public List<String> getTelefones() {
        return telefones;
    }

    public void setTelefones(List<String> telefones) {
        this.telefones = telefones;
    }

    public List<String> getTipos() {
        return tipos;
    }

    public void setTipos(List<String> tipos) {
        this.tipos = tipos;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public String getSituacaoClienteContrato() {
        return situacaoClienteContrato;
    }

    public void setSituacaoClienteContrato(String situacaoClienteContrato) {
        this.situacaoClienteContrato = situacaoClienteContrato;
    }

    public String getSituacaoColaborador() {
        return situacaoColaborador;
    }

    public void setSituacaoColaborador(String situacaoColaborador) {
        this.situacaoColaborador = situacaoColaborador;
    }

    public boolean isGympass() {
        return gympass;
    }

    public void setGympass(boolean gympass) {
        this.gympass = gympass;
    }

    public boolean isTotalpass() {
        return totalpass;
    }

    public void setTotalpass(boolean totalpass) {
        this.totalpass = totalpass;
    }

    public void setProgramaVigente(boolean programaVigente) {
        this.programaVigente = programaVigente;
    }

    public boolean isProgramaVigente() {
        return programaVigente;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataUltimoAcesso(long dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public long getDataInicioProgramaTreino() {
        return dataInicioProgramaTreino;
    }

    public void setDataInicioProgramaTreino(long dataInicioProgramaTreino) {
        this.dataInicioProgramaTreino = dataInicioProgramaTreino;
    }

    public long getDataFimProgramaTreino() {
        return dataFimProgramaTreino;
    }

    public void setDataFimProgramaTreino(long dataFimProgramaTreino) {
        this.dataFimProgramaTreino = dataFimProgramaTreino;
    }

    public long getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public Integer getPesoRisco() {
        return pesoRisco;
    }

    public void setPesoRisco(Integer pesoRisco) {
        this.pesoRisco = pesoRisco;
    }

    public Integer getQtdAcompanhamentos() {
        return qtdAcompanhamentos;
    }

    public void setQtdAcompanhamentos(Integer qtdAcompanhamentos) {
        this.qtdAcompanhamentos = qtdAcompanhamentos;
    }
}
