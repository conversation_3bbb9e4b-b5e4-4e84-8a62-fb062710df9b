package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteObservacao;
import br.com.pacto.bean.usuario.Usuario;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Representa uma observação do cliente com dados de anexo.")
public class ClienteObservacaoAnexosDTO {

    @ApiModelProperty(value = "ID da observação", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Indica se a observação é considerada importante", example = "true")
    private Boolean importante;

    @ApiModelProperty(value = "Texto da observação realizada", example = "Cliente mencionou dor no ombro durante a aula.")
    private String observacao;

    @ApiModelProperty(value = "Data da observação", example = "2024-04-10T10:15:30.000Z")
    private Date data;

    @ApiModelProperty(value = "Indica se está relacionada a uma avaliação física", example = "false")
    private Boolean avaliacaoFisica;

    @ApiModelProperty(value = "Anexo", example = "ombro-aula.pdf")
    private String anexo;

    @ApiModelProperty(value = "Nome do usuário que registrou a observação", example = "instrutor.joao")
    private String usuario;

    @ApiModelProperty(value = "Nome original do arquivo anexado", example = "ombro-aula.pdf")
    private String nomeArquivo;

    @ApiModelProperty(value = "Formato/MIME type do arquivo", example = "application/pdf")
    private String formatoArquivo;

    @ApiModelProperty(value = "Origem da observação", example = "APP_MOBILE")
    private String origem;


    public ClienteObservacaoAnexosDTO() {
    }

    public ClienteObservacaoAnexosDTO(ClienteObservacao clienteObservacao, Usuario usu) {

        this.id = clienteObservacao.getCodigo();
        this.importante = clienteObservacao.getImportante();
        this.observacao = clienteObservacao.getObservacao();
        this.data = clienteObservacao.getDataObservacao();
        this.avaliacaoFisica = clienteObservacao.getAvaliacaoFisica();
        this.anexo = clienteObservacao.getAnexoKey();
        this.usuario = usu == null ? "" : usu.getNome();
        this.nomeArquivo = "";
        if (anexo != null && !anexo.isEmpty()) {
            this.nomeArquivo = clienteObservacao.getNomeArquivo();
            this.formatoArquivo = clienteObservacao.getFormatoArquivo();
            try {
                String anexo = this.anexo.split("/")[this.anexo.split("/").length - 1];
                if (this.nomeArquivo == null || this.nomeArquivo.isEmpty()) {
                    this.nomeArquivo = anexo.split("\\.")[0];
                }
                if (this.formatoArquivo == null || this.formatoArquivo.isEmpty()) {
                    this.formatoArquivo = anexo.substring(anexo.lastIndexOf("."));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getImportante() {
        return importante;
    }

    public void setImportante(Boolean importante) {
        this.importante = importante;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Boolean getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(Boolean avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public String getAnexo() {
        return anexo;
    }

    public void setAnexo(String anexo) {
        this.anexo = anexo;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoArquivo() {
        return formatoArquivo;
    }

    public void setFormatoArquivo(String formatoArquivo) {
        this.formatoArquivo = formatoArquivo;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }
}