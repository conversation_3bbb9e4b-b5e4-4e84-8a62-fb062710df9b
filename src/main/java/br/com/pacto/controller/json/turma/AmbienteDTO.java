package br.com.pacto.controller.json.turma;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do ambiente onde pode ser realizado o agendamento")
public class AmbienteDTO {

    @ApiModelProperty(value = "Código único identificador do ambiente", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Nome do ambiente", example = "Sala de Musculação A")
    private String nome;

    @ApiModelProperty(value = "Código do tipo de ambiente", example = "1")
    private Integer tipoAmbiente;

    @ApiModelProperty(value = "Capacidade máxima de convidados no ambiente", example = "5")
    private Integer capacidadeMaximaConvidados;

    @ApiModelProperty(value = "Código da situação do ambiente", example = "1")
    private Integer situacao;

    @ApiModelProperty(value = "Capacidade total do ambiente", example = "30")
    private Integer capacidade;

    @ApiModelProperty(value = "Tipo do módulo do ambiente", example = "MUSCULACAO")
    private String tipoModulo;

    @ApiModelProperty(value = "Código do coletor associado ao ambiente", example = "456")
    private Integer coletor;

    @ApiModelProperty(value = "Indica se o ambiente está ativo", example = "true")
    private Boolean ativo;

    public AmbienteDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getTipoAmbiente() {
        return tipoAmbiente;
    }

    public void setTipoAmbiente(Integer tipoAmbiente) {
        this.tipoAmbiente = tipoAmbiente;
    }

    public Integer getCapacidadeMaximaConvidados() {
        return capacidadeMaximaConvidados;
    }

    public void setCapacidadeMaximaConvidados(Integer capacidadeMaximaConvidados) {
        this.capacidadeMaximaConvidados = capacidadeMaximaConvidados;
    }

    public Integer getSituacao() {
        return situacao;
    }

    public void setSituacao(Integer situacao) {
        this.situacao = situacao;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public String getTipoModulo() {
        return tipoModulo;
    }

    public void setTipoModulo(String tipoModulo) {
        this.tipoModulo = tipoModulo;
    }

    public Integer getColetor() {
        return coletor;
    }

    public void setColetor(Integer coletor) {
        this.coletor = coletor;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
