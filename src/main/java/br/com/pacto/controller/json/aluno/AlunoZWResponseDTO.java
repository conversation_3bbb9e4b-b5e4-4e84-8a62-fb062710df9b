package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.base.SuperControle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

/**
 * Created by paulo in 08/05/2019
 */


@ApiModel(description = "Dados do aluno na integração com o sistema ZW.")
public class AlunoZWResponseDTO {

    @ApiModelProperty(value = "Identificador do aluno no sistema ZW", example = "1024")
    private Integer alunoZWId;

    @ApiModelProperty(value = "Número de matrícula do aluno no sistema ZW", example = "998877")
    private Integer matriculaZW;

    @ApiModelProperty(value = "Identificador do treino vinculado ao aluno", example = "456")
    private Integer treinoId;

    @ApiModelProperty(value = "Nome completo do aluno", example = "<PERSON><PERSON>")
    private String nome;

    @ApiModelProperty(value = "Endereço de e-mail do aluno", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "URL da imagem de perfil do aluno", example = "https://academia.com/images/alunos/joana.png")
    private String imageUri;

    public AlunoZWResponseDTO(ClienteSintetico cliente, Integer clienteTreinoCodigo) {
        this.alunoZWId = cliente.getCodigoCliente();
        this.matriculaZW = cliente.getMatricula();
        this.treinoId = clienteTreinoCodigo;
        this.nome = cliente.getNome();
        this.email = cliente.getEmail();
        this.imageUri = cliente.getUrlFoto();
    }

    public Integer getAlunoZWId() {
        return alunoZWId;
    }

    public void setAlunoZWId(Integer alunoZWId) {
        this.alunoZWId = alunoZWId;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public Integer getTreinoId() {
        return treinoId;
    }

    public void setTreinoId(Integer treinoId) {
        this.treinoId = treinoId;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }
}
