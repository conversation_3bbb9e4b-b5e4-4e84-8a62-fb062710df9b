/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.atividade.read;

import br.com.pacto.bean.atividade.AtividadeVideoTO;
import br.com.pacto.bean.atividade.ImgMediumUrlsTO;
import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Informações detalhadas de uma atividade física")
public class AtividadeJSON extends SuperJSON {

    @ApiModelProperty(value = "Código único da atividade", example = "123")
    private Integer cod;

    @ApiModelProperty(value = "Identificador da atividade no sistema de IA", example = "IA_ACT_456")
    private String  iaID;
    private Integer  iaID2;

    @ApiModelProperty(value = "Código alternativo da atividade", example = "789")
    private Integer codigoAtividade;

    @ApiModelProperty(value = "Indica se a atividade está ativa no sistema", example = "true")
    private boolean ativo = true;

    @ApiModelProperty(value = "Nome da atividade", example = "Supino Reto com Barra")
    private String nome;

    @ApiModelProperty(value = "Descrição detalhada da atividade", example = "Exercício para desenvolvimento do peitoral maior, realizado em banco horizontal")
    private String descricao = "";

    @ApiModelProperty(value = "Versão da atividade", example = "1.0")
    private String versao = "";

    @ApiModelProperty(value = "Tipo da atividade (código numérico)", example = "1")
    private Integer tipo;

    @ApiModelProperty(value = "URL da imagem miniatura da atividade", example = "https://exemplo.com/thumb_supino.jpg")
    private String thumb = "";

    @ApiModelProperty(value = "URL da imagem principal da atividade", example = "https://exemplo.com/supino_reto.jpg")
    private String img = "";

    @ApiModelProperty(value = "Ordem de exibição da atividade", example = "1")
    private Integer ordem = 0;

    @ApiModelProperty(value = "URL da imagem em tamanho médio", example = "https://exemplo.com/medium_supino.jpg")
    private String imgMedium = "";

    @ApiModelProperty(value = "URL do vídeo demonstrativo da atividade", example = "https://exemplo.com/video_supino.mp4")
    private String urlVideo = "";

    @ApiModelProperty(value = "Lista de URLs de vídeos relacionados à atividade")
    private List<String> urlVideos;

    @ApiModelProperty(value = "Aparelhos utilizados na atividade (texto)", example = "Banco horizontal, Barra olímpica")
    private String aparelhos = "";

    @ApiModelProperty(value = "Identificador único da atividade", example = "SUPINO_RETO_001")
    private String identificador = "";

    @ApiModelProperty(value = "Campos utilizados para filtros de busca", example = "peitoral|peito|supino|barra")
    private String camposFiltro = "";

    @ApiModelProperty(value = "Lista de URLs de imagens em tamanho médio")
    private List<String> imgMediumUrls;

    @ApiModelProperty(value = "Complemento do nome da atividade", example = "Variação com pegada fechada")
    private String complementoNomeAtividade;

    @ApiModelProperty(value = "Lista de ajustes disponíveis para a atividade")
    private List<AjusteJSON> ajustes;

    @ApiModelProperty(value = "Lista de níveis de dificuldade da atividade")
    private List<String> niveis;

    @ApiModelProperty(value = "Lista de categorias da atividade")
    private List<String> categorias;

    @ApiModelProperty(value = "Lista de aparelhos utilizados na atividade")
    private List<String> listAparelhos;

    @ApiModelProperty(value = "Lista de grupos musculares trabalhados")
    private List<String> gruposMusculares;

    @ApiModelProperty(value = "Lista de animações disponíveis para a atividade")
    private List<String> animacoes;

    @ApiModelProperty(value = "Lista de códigos de atividades alternativas")
    private List<Integer> atividadesAlternativas;

    @ApiModelProperty(value = "Lista de vídeos com informações detalhadas")
    private List<AtividadeVideoTO> urlLinkVideos;

    @ApiModelProperty(value = "Lista de imagens em tamanho médio com informações detalhadas")
    private List<ImgMediumUrlsTO> listImgMediumUrls;

    public List<Integer> getAtividadesAlternativas() {
        return atividadesAlternativas;
    }

    public void setAtividadesAlternativas(List<Integer> atividadesAlternativas) {
        this.atividadesAlternativas = atividadesAlternativas;
    }

    public Integer getCod() {
        return cod;
    }

    public void setCod(Integer cod) {
        this.cod = cod;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public String getVersao() {
        return versao;
    }

    public void setVersao(String versao) {
        this.versao = versao;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public String getThumb() {
        return thumb;
    }

    public void setThumb(String thumb) {
        this.thumb = thumb;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public String getImgMedium() {
        return imgMedium;
    }

    public void setImgMedium(String imgMedium) {
        this.imgMedium = imgMedium;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(String aparelhos) {
        this.aparelhos = aparelhos;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getCamposFiltro() {
        return camposFiltro;
    }

    public void setCamposFiltro(String camposFiltro) {
        this.camposFiltro = camposFiltro;
    }

    public Integer getCodigoAtividade() {
        return codigoAtividade;
    }

    public void setCodigoAtividade(Integer codigoAtividade) {
        this.codigoAtividade = codigoAtividade;
    }

    public List<String> getImgMediumUrls() {
        return imgMediumUrls;
    }

    public void setImgMediumUrls(List<String> imgMediumUrls) {
        this.imgMediumUrls = imgMediumUrls;
    }

    public String getUrlVideo() {
        return urlVideo;
    }

    public void setUrlVideo(String urlVideo) {
        this.urlVideo = urlVideo;
    }

    public List<String> getUrlVideos() { return urlVideos; }

    public void setUrlVideos(List<String> urlVideos) { this.urlVideos = urlVideos; }

    public String getComplementoNomeAtividade() {
        return complementoNomeAtividade;
    }

    public void setComplementoNomeAtividade(String complementoNomeAtividade) {
            this.complementoNomeAtividade = complementoNomeAtividade;
    }

    public List<AjusteJSON> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AjusteJSON> ajustes) {
        this.ajustes = ajustes;
    }

    public List<String> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<String> niveis) {
        this.niveis = niveis;
    }

    public List<String> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<String> categorias) {
        this.categorias = categorias;
    }

    public List<String> getListAparelhos() {
        return listAparelhos;
    }

    public void setListAparelhos(List<String> listAparelhos) {
        this.listAparelhos = listAparelhos;
    }

    public List<String> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<String> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

    public List<String> getAnimacoes() {
        return animacoes;
    }

    public void setAnimacoes(List<String> animacoes) {
        this.animacoes = animacoes;
    }

    public String getIaID() {
        return iaID;
    }

    public void setIaID(String iaID) {
        this.iaID = iaID;
    }

    public List<AtividadeVideoTO> getUrlLinkVideos() {
        return urlLinkVideos;
    }

    public void setUrlLinkVideos(List<AtividadeVideoTO> urlLinkVideos) {
        this.urlLinkVideos = urlLinkVideos;
    }

    public List<ImgMediumUrlsTO> getListImgMediumUrls() {
        return listImgMediumUrls;
    }

    public void setListImgMediumUrls(List<ImgMediumUrlsTO> listImgMediumUrls) {
        this.listImgMediumUrls = listImgMediumUrls;
    }

    public Integer getIaID2() {
        return iaID2;
    }

    public void setIaID2(Integer iaID2) {
        this.iaID2 = iaID2;
    }
}
