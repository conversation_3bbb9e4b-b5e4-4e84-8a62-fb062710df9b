package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

/**
 * Created by joao moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações detalhadas da atividade física que pode ser realizada no aparelho")
public class AtividadeAparelhoResponseTO {

    @ApiModelProperty(value = "Lista de níveis de dificuldade associados à atividade")
    private ArrayList<AtividadeNivelResponseTO> niveis;

    @ApiModelProperty(value = "Lista de músculos trabalhados na atividade")
    private ArrayList<AtividadeMusculoResponseTO> musculos;

    @ApiModelProperty(value = "Lista de grupos musculares trabalhados na atividade")
    private ArrayList<AtividadeGrupoMuscularResponseTO> gruposMusculares;

    @ApiModelProperty(value = "Lista de aparelhos que podem ser utilizados para esta atividade")
    private ArrayList<AtividadeAparelhoResponseRecursiveTO> aparelhos;

    @ApiModelProperty(value = "Lista de categorias às quais a atividade pertence")
    private ArrayList<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade;

    @ApiModelProperty(value = "Lista de imagens demonstrativas da atividade")
    private ArrayList<AtividadeImagemResponseTO> images;

    @ApiModelProperty(value = "URL do vídeo demonstrativo da atividade", example = "https://exemplo.com/video-treino-costas.mp4")
    private String videoUri;

    @ApiModelProperty(value = "Lista de empresas que têm acesso à atividade")
    private ArrayList<AtividadeEmpresaResponseTO> empresas;

    @ApiModelProperty(value = "Descrição detalhada da atividade física", example = "Exercício para fortalecimento dos músculos das costas utilizando aparelho específico")
    private String descricao;

    @ApiModelProperty(value = "Tipo da atividade física. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ANAEROBICO (Neuromuscular)\n" +
            "- AEROBICO (Cardiovascular)\n", example = "ANAEROBICO")
    private TipoAtividadeEnum tipo;

    @ApiModelProperty(value = "Indica se a série da atividade é baseada apenas em duração", example = "false")
    private Boolean serieApenasDuracao;

    @ApiModelProperty(value = "Código único identificador da atividade", example = "15")
    private Integer id;

    @ApiModelProperty(value = "Nome da atividade física", example = "Remada Cavalinho")
    private String nome;

    @ApiModelProperty(value = "Indica se a atividade está ativa no sistema", example = "true")
    private boolean ativo;

    public AtividadeAparelhoResponseTO(){

    }

    public AtividadeAparelhoResponseTO(Atividade atividade){
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.ativo = atividade.isAtivo();
        this.serieApenasDuracao = atividade.getSelecionado();
        this.tipo = atividade.getTipo();
        this.descricao = atividade.getDescricao();
        this.empresas = new ArrayList<AtividadeEmpresaResponseTO>();
        for(AtividadeEmpresa atividadeEmpresa : atividade.getEmpresasHabilitadas()) {
            empresas.add(new AtividadeEmpresaResponseTO(atividadeEmpresa));
        }
        this.videoUri = atividade.getLinkVideo();
        if(atividade.getTemImagem()) {
            this.images = new ArrayList<AtividadeImagemResponseTO>();
            for(AtividadeAnimacao atividadeAnimacao : atividade.getAnimacoes()) {
                this.images.add(new AtividadeImagemResponseTO(atividadeAnimacao));
            }
        }
        if(atividade.getCategorias().isEmpty() == false){
            this.categoriasAtividade = new ArrayList<AtividadeCategoriaAtividadeResponseTO>();
            for(AtividadeCategoriaAtividade atividadeCategoriaAtividade :
                atividade.getCategorias()) {
                categoriasAtividade.add(new AtividadeCategoriaAtividadeResponseTO(atividadeCategoriaAtividade));
            }
        }
        if(atividade.getAparelhos().isEmpty() == false) {
            this.aparelhos = new ArrayList<AtividadeAparelhoResponseRecursiveTO>();
            for(AtividadeAparelho atividadeAparelho : atividade.getAparelhos()) {
                if (atividadeAparelho.getAparelho() != null) {
                    this.aparelhos.add(new AtividadeAparelhoResponseRecursiveTO(atividadeAparelho));
                }
            }
        }
        if(atividade.getGruposMusculares().isEmpty() == false) {
            this.gruposMusculares = new ArrayList<AtividadeGrupoMuscularResponseTO>();
            for(AtividadeGrupoMuscular atividadeGrupoMuscular : atividade.getGruposMusculares()) {
                this.gruposMusculares.add(new AtividadeGrupoMuscularResponseTO(atividadeGrupoMuscular));
            }
        }
        if(atividade.getMusculos().isEmpty() == false) {
            this.musculos = new ArrayList<AtividadeMusculoResponseTO>();
            for(AtividadeMusculo atividadeMusculo : atividade.getMusculos()) {
                this.musculos.add(new AtividadeMusculoResponseTO(atividadeMusculo));
            }
        }
        if(atividade.getNiveis().isEmpty() == false) {
            this.niveis = new ArrayList<AtividadeNivelResponseTO>();
            for(AtividadeNivel atividadeNivel : atividade.getNiveis()) {
                this.niveis.add(new AtividadeNivelResponseTO(atividadeNivel));
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public ArrayList<AtividadeNivelResponseTO> getNiveis() {
        return niveis;
    }

    public void setNiveis(ArrayList<AtividadeNivelResponseTO> niveis) {
        this.niveis = niveis;
    }

    public ArrayList<AtividadeMusculoResponseTO> getMusculos() {
        return musculos;
    }

    public void setMusculos(ArrayList<AtividadeMusculoResponseTO> musculos) {
        this.musculos = musculos;
    }

    public ArrayList<AtividadeGrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(ArrayList<AtividadeGrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

    public ArrayList<AtividadeAparelhoResponseRecursiveTO> getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(ArrayList<AtividadeAparelhoResponseRecursiveTO> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public ArrayList<AtividadeCategoriaAtividadeResponseTO> getCategoriasAtividade() {
        return categoriasAtividade;
    }

    public void setCategoriasAtividade(ArrayList<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade) {
        this.categoriasAtividade = categoriasAtividade;
    }

    public ArrayList<AtividadeImagemResponseTO> getImages() {
        return images;
    }

    public void setImages(ArrayList<AtividadeImagemResponseTO> images) {
        this.images = images;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public ArrayList<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(ArrayList<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoAtividadeEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEnum tipo) {
        this.tipo = tipo;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }
}
