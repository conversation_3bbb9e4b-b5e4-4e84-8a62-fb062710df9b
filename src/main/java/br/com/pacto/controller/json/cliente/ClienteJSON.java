/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.cliente;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.serialization.JsonDateSerializerYYYYMMDD;
import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.DataUtils;
import br.com.pacto.util.json.ClienteSintenticoJson;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Informações completas de um cliente/aluno da academia")
public class ClienteJSON extends SuperJSON {

    @ApiModelProperty(value = "Código identificador único do cliente no sistema", example = "1001")
    private Integer codigo;

    @ApiModelProperty(value = "Código da pessoa no sistema ZW (integração)", example = "5001")
    private Integer codigoPessoaZW;

    @ApiModelProperty(value = "Código do cliente no sistema ZW (integração)", example = "3001")
    private Integer codigoClienteZW;

    @ApiModelProperty(value = "Código do contrato ativo do cliente", example = "2001")
    private Integer codigoContrato;

    @ApiModelProperty(value = "Número da matrícula do cliente", example = "2024001")
    private Integer matricula;

    @ApiModelProperty(value = "Nome completo do cliente", example = "João Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Data de nascimento do cliente")
    private Date dataNascimento;

    @ApiModelProperty(value = "Idade do cliente em anos", example = "28")
    private Integer idade;

    @ApiModelProperty(value = "Profissão do cliente", example = "Engenheiro")
    private String profissao;

    @ApiModelProperty(value = "Colaboradores responsáveis pelo cliente", example = "Carlos Silva, Ana Santos")
    private String colaboradores;

    @ApiModelProperty(value = "Professor responsável pelo cliente", example = "Carlos Silva")
    private String professor;

    @ApiModelProperty(value = "Situação atual do cliente. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)", example = "AT")
    private String situacao;

    @ApiModelProperty(value = "Situação do contrato do cliente. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- MA (MATRICULA)\n" +
            "- RE (REMATRICULA)\n" +
            "- RN (RENOVACAO)", example = "MA")
    private String situacaoContrato;
    @ApiModelProperty(value = "Data do último acesso do cliente ao sistema")
    private Date dataUltimoAcesso;

    @ApiModelProperty(value = "Data de início do período de acesso do cliente")
    private Date dataInicioPeriodoAcesso;

    @ApiModelProperty(value = "Data de fim do período de acesso do cliente")
    private Date dataFimPeriodoAcesso;

    @ApiModelProperty(value = "Código da empresa/academia do cliente", example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "Endereço de email do cliente", example = "<EMAIL>")
    private String email = "";

    @ApiModelProperty(value = "Números de telefone do cliente", example = "(11) 99999-9999, (11) 88888-8888")
    private String telefones;

    @ApiModelProperty(value = "Status do andamento do treino do cliente", example = "Em progresso")
    private String andamentoTreino;

    @ApiModelProperty(value = "URL da foto do cliente", example = "https://academia.com/fotos/cliente_123.jpg")
    private String urlFoto;

    @ApiModelProperty(value = "Nome de usuário do cliente no sistema", example = "joao.silva")
    private String userName;

    @ApiModelProperty(value = "Data específica relacionada ao cliente")
    private Date dia;

    @ApiModelProperty(value = "Nível do cliente na academia", example = "Intermediário")
    private String nivel;

    @ApiModelProperty(value = "Nome da empresa/academia", example = "Academia Fitness Pro")
    private String nomeEmpresa;

    @ApiModelProperty(value = "Versão do registro do cliente", example = "2")
    private Integer versao;

    @ApiModelProperty(value = "Código de acesso do cliente", example = "ABC123")
    private String codigoAcesso;

    @ApiModelProperty(value = "Data de vencimento do plano do cliente", example = "31/12/2024")
    private String vencPlano;

    @ApiModelProperty(value = "Lista de números de telefone do cliente", example = "[\"(11) 99999-9999\", \"(11) 88888-8888\"]")
    private List<String> listaTelefones;

    @ApiModelProperty(value = "Lista de endereços de email do cliente", example = "[\"<EMAIL>\", \"<EMAIL>\"]")
    private List<String> listaEmails;

    @ApiModelProperty(value = "Sexo do cliente", example = "M")
    private String sexo;

    @ApiModelProperty(value = "Objetivos do cliente na academia", example = "Perder peso, ganhar massa muscular")
    private String objetivos;

    @ApiModelProperty(value = "Dados da avaliação física do cliente", example = "Peso: 75kg, Altura: 1.75m, IMC: 24.5")
    private String dadosAvaliacao;

    @ApiModelProperty(value = "Indica se o cliente preencheu o questionário PAR-Q", example = "true")
    private Boolean parQ;

    @ApiModelProperty(value = "Chave da foto do cliente no aplicativo", example = "foto_app_123")
    private String fotoKeyApp;

    @ApiModelProperty(value = "Tempo total de aula do cliente em minutos", example = "60")
    private Integer tempoDeAula;

    @ApiModelProperty(value = "Posição do cliente no ranking geral", example = "15")
    private Integer posicaoRankingAluno;

    @ApiModelProperty(value = "Total de calorias queimadas pelo cliente", example = "350")
    private Integer calories;

    @ApiModelProperty(value = "Potência média do cliente nos exercícios", example = "180")
    private Integer averagePower;

    public ClienteJSON() {
    }

    public ClienteJSON(final ClienteSintetico clienteSintetico) {
        this.andamentoTreino = clienteSintetico.getAndamentoTreino();
        this.codigo = clienteSintetico.getCodigo();
        this.codigoClienteZW = clienteSintetico.getCodigoCliente();
        this.codigoContrato = clienteSintetico.getCodigoContrato();
        this.codigoPessoaZW = clienteSintetico.getCodigoPessoa();
        this.colaboradores = clienteSintetico.getColaboradores();
        this.dataFimPeriodoAcesso = clienteSintetico.getDataFimPeriodoAcesso();
        this.dataInicioPeriodoAcesso = clienteSintetico.getDataFimPeriodoAcesso();
        this.dataNascimento = clienteSintetico.getDataNascimento();
        this.dataUltimoAcesso = clienteSintetico.getDataUltimoacesso();
        this.empresa = clienteSintetico.getEmpresa();
        this.idade = clienteSintetico.getIdade();
        this.matricula = clienteSintetico.getMatricula();
        this.nome = clienteSintetico.getNome();
        this.professor = clienteSintetico.getProfessorSintetico() != null ? clienteSintetico.getProfessorSintetico().getNome() : "";
        this.profissao = clienteSintetico.getProfissao();
        this.situacao = clienteSintetico.getSituacao();
        this.situacaoContrato = clienteSintetico.getSituacaoContrato();
        this.dia = clienteSintetico.getDia();
        this.nivel = clienteSintetico.getNivelAluno() != null ? clienteSintetico.getNivelAluno().getNome() : "";
        this.versao = clienteSintetico.getVersao();
        this.codigoAcesso = clienteSintetico.getCodigoAcesso();
        this.vencPlano = clienteSintetico.getDataVigenciaAteAjustadaApresentar();
        this.listaEmails = clienteSintetico.getListaEmails();
        this.listaTelefones = clienteSintetico.getListaTelefones();
        this.sexo = clienteSintetico.getSexo();
        this.objetivos = clienteSintetico.getObjetivos() != null ? clienteSintetico.getObjetivos().toString() : "";
        this.dadosAvaliacao = clienteSintetico.getDadosAvaliacao() != null ? clienteSintetico.getDadosAvaliacao() : "";
        this.parQ = clienteSintetico.getParq() == null ? false : clienteSintetico.getParq();
        this.urlFoto = clienteSintetico.getUrlFoto();
        this.fotoKeyApp = clienteSintetico.getFotoKeyApp();

    }

    public ClienteJSON(final ClienteSintenticoJson cliente) {
        this.codigo = cliente.getCodigo();
        this.codigoClienteZW = cliente.getCodigoClienteZW();
        this.codigoContrato = cliente.getCodigoContrato();
        this.codigoPessoaZW = cliente.getCodigoPessoaZW();
        this.colaboradores = cliente.getColaboradores();
        this.dataFimPeriodoAcesso = cliente.getDataFimPeriodoAcesso();
        this.dataInicioPeriodoAcesso = cliente.getDataFimPeriodoAcesso();
        this.dataNascimento = cliente.getDataNascimento();
        this.dataUltimoAcesso = cliente.getDataUltimoAcesso();
        this.empresa = cliente.getEmpresa();
        this.idade = cliente.getIdade();
        this.matricula = cliente.getMatricula();
        this.nome = cliente.getNome();
        this.professor = cliente.getProfessor();
        this.profissao = cliente.getProfissao();
        this.situacao = cliente.getSituacao();
        this.situacaoContrato = cliente.getSituacaoContrato();
        this.dia = cliente.getDia();
        this.nivel = cliente.getNivel();
        this.versao = cliente.getVersao();
        this.codigoAcesso = cliente.getCodigoAcesso();
        this.vencPlano = Uteis.getData(cliente.getDataVirgencia());
        this.listaEmails = cliente.getListaEmails();
        this.listaTelefones = cliente.getListaTelefones();
        this.sexo = cliente.getSexo();
        this.objetivos = cliente.getObjetivos();
        this.dadosAvaliacao = cliente.getDadosAvaliacao();
        this.parQ = cliente.getParQ() == null ? false : cliente.getParQ();
        this.urlFoto = cliente.getUrlFoto();
    }

    public ClienteJSON(Integer codigoPessoaZw, Integer codigoClienteZW, Integer codigoContrato, String urlFoto,
                       String nome, Integer matricula, String telefones, String situacaoContrato, String situacao,
                       String fotoKey, String dataNascimento, String email, String sexo) throws ParseException {
        this.setCodigoPessoaZW(codigoPessoaZw);
        if(codigoClienteZW != null){
            this.setCodigoClienteZW(codigoClienteZW);
        }
        if(codigoContrato != null){
            this.setCodigoContrato(codigoContrato);
        }
        if(urlFoto != null){
            this.setUrlFoto(urlFoto);
        }
        if(nome != null){
            this.setNome(nome);
        }
        if(matricula != null){
            this.setMatricula(matricula);
        }
        if(telefones != null){
            this.setTelefones(telefones);
        }
        if(situacaoContrato != null){
            this.setSituacaoContrato(situacaoContrato);
        }
        if(situacao != null){
            this.setSituacao(situacao);
        }
        if(fotoKey != null){
            this.setFotoKeyApp(fotoKey);
        }
        if(dataNascimento != null){
            Date data = DataUtils.stringToDate(dataNascimento, "yyyy-MM-dd");
            this.setDataNascimento(data);
        }
        if(email != null){
            this.setEmail(email);
        }
        if(sexo != null){
            this.setSexo(sexo);
        }
    }


    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoPessoaZW() {
        return codigoPessoaZW;
    }

    public void setCodigoPessoaZW(Integer codigoPessoaZW) {
        this.codigoPessoaZW = codigoPessoaZW;
    }

    public Integer getCodigoClienteZW() {
        return codigoClienteZW;
    }

    public void setCodigoClienteZW(Integer codigoClienteZW) {
        this.codigoClienteZW = codigoClienteZW;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(String colaboradores) {
        this.colaboradores = colaboradores;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataUltimoAcesso() {        
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    public void setDataInicioPeriodoAcesso(Date dataInicioPeriodoAcesso) {
        this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class, nullsUsing = JsonDateSerializerYYYYMMDD.class)
    public Date getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    public void setDataFimPeriodoAcesso(Date dataFimPeriodoAcesso) {
        this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefones() {
        if (telefones == null) {
            telefones = "";
        }
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getAndamentoTreino() {
        return andamentoTreino;
    }

    public void setAndamentoTreino(String andamentoTreino) {
        this.andamentoTreino = andamentoTreino;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonSerialize(using = JsonDateSerializerYYYYMMDD.class)
    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getVencPlano() {
        return vencPlano;
    }

    public void setVencPlano(String vencPlano) {
        this.vencPlano = vencPlano;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(String objetivos) {
        this.objetivos = objetivos;
    }

    public List<String> getListaTelefones() {
        return listaTelefones;
    }

    public void setListaTelefones(List<String> listaTelefones) {
        this.listaTelefones = listaTelefones;
    }

    public List<String> getListaEmails() {
        return listaEmails;
    }

    public void setListaEmails(List<String> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public String getDadosAvaliacao() {
        return dadosAvaliacao;
    }

    public void setDadosAvaliacao(String dadosAvaliacao) {
        this.dadosAvaliacao = dadosAvaliacao;
    }

    public Boolean getParQ() {
        return parQ;
    }

    public void setParQ(Boolean parQ) {
        this.parQ = parQ;
    }

    public String getFotoKeyApp() {
        return fotoKeyApp;
    }

    public void setFotoKeyApp(String fotoKeyApp) {
        this.fotoKeyApp = fotoKeyApp;
    }

    public Integer getTempoDeAula() {
        return tempoDeAula;
    }

    public void setTempoDeAula(Integer tempoDeAula) {
        this.tempoDeAula = tempoDeAula;
    }

    public Integer getPosicaoRankingAluno() {
        return posicaoRankingAluno;
    }

    public void setPosicaoRankingAluno(Integer posicaoRankingAluno) {
        this.posicaoRankingAluno = posicaoRankingAluno;
    }

    public Integer getCalories() {
        return calories;
    }

    public void setCalories(Integer calories) {
        this.calories = calories;
    }

    public Integer getAveragePower() {
        return averagePower;
    }

    public void setAveragePower(Integer averagePower) {
        this.averagePower = averagePower;
    }
}
