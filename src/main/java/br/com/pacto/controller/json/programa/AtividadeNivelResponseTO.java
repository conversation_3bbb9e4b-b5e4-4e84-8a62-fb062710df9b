package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeNivel;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do nível da atividade física")
public class AtividadeNivelResponseTO {

    @ApiModelProperty(value = "Código único identificador do nível de atividade física", example = "3")
    private Integer id;
    @ApiModelProperty(value = "Nome do nível da atividade física", example = "Intermediário")
    private String nome;

    public AtividadeNivelResponseTO() {

    }

    public AtividadeNivelResponseTO(AtividadeNivel an) {
        this.id = an.getNivel().getCodigo();
        this.nome = an.getNivel().getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
