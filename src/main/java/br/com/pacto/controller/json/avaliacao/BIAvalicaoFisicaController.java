package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.bi.IndicadorAvaliacaoFisicaEnum;
import br.com.pacto.bean.professor.Professor<PERSON>int<PERSON><PERSON>;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.avaliacao.bi_enum.TipoBIAvaliacaoEnum;
import br.com.pacto.dao.intf.professor.ProfessorSinteticoDao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.avaliacao.ItemAvaliacaoFisicaTO;
import br.com.pacto.service.intf.avaliacao.BIAvaliacaoFisicaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaBIAvaliacaoFisicaDTO;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaListItemAvaliacaoFisicaTO;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaExportacaoAvaliacoesBI;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import io.swagger.annotations.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Siqueira 16/01/2019
 */

@Api(tags = "BI Avaliação Física", description = "Endpoints para Business Intelligence de avaliações físicas")
@Controller
@RequestMapping("/psec/avaliacao-fisica-bi")
public class BIAvalicaoFisicaController {

    @Autowired
    private BIAvaliacaoFisicaService biAvaliacaoFisicaService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private UsuarioService usuarioService;

    @ApiOperation(
            value = "Consultar indicadores consolidados de Business Intelligence para avaliações físicas",
            notes = "Retorna um dashboard completo com indicadores estatísticos de avaliações físicas para o período especificado, incluindo número de avaliações realizadas, novas, reavaliações, atrasadas, alunos que perderam peso/gordura, ganharam massa magra, e dados para gráficos de acompanhamento. Os dados podem ser filtrados por professor, avaliador e tipo de avaliação.",
            tags = "BI Avaliação Física"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Indicadores de BI de avaliações físicas)", response = ExemploRespostaBIAvaliacaoFisicaDTO.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> motarBI(
            @ApiParam(value = "Identificador da empresa", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Data de início do período em timestamp", defaultValue = "1717200000000", required = true)
            @RequestParam(value = "dataInicio") Long dataInicio,
            @ApiParam(value = "Data de fim do período em timestamp", defaultValue = "1719792000000", required = true)
            @RequestParam(value = "dataFim") Long dataFim,
            @ApiParam(value = "Código do professor para filtro específico", defaultValue = "123", required = false)
            @RequestParam(value = "codigoProfessor", required = false) Integer codigoProfessor,
            @ApiParam(value = "Tipo de avaliação para filtro.<br/><br/>" +
                    "<strong>Valores disponíveis:</strong><br/>" +
                    "- <strong>TODOS_PROFESSORES:</strong> Inclui avaliações de todos os professores ativos<br/>" +
                    "- <strong>ALUNOS_SEM_VINCULO:</strong> Inclui apenas alunos sem vínculo com professor<br/>" +
                    "- <strong>TODOS_PROFESSORES_INATIVOS:</strong> Inclui avaliações de professores inativos",
                    defaultValue = "TODOS_PROFESSORES", required = false)
            @RequestParam(value = "tipoAvaliacao", required = false) TipoBIAvaliacaoEnum tipoAvaliacao,
            @ApiParam(value = "Código do avaliador físico para filtro específico", defaultValue = "456", required = false)
            @RequestParam(value = "codigoAvaliador", required = false) Integer codigoAvaliador){
        try{
            final String chave = sessaoService.getUsuarioAtual().getChave();

            if (UteisValidacao.notEmptyNumber(codigoAvaliador) && codigoAvaliador > 0) {
                try {
                    Usuario us = usuarioService.consultarProColaborador(chave, codigoAvaliador);
                    if (us != null) {
                        codigoAvaliador = us.getCodigo();
                    }
                } catch (Exception ignore) {}
            }
            return ResponseEntityFactory.ok(biAvaliacaoFisicaService.getBI(chave, empresaId, new Date(dataInicio), new Date(dataFim), codigoProfessor, tipoAvaliacao, codigoAvaliador));
        }catch (ServiceException e){
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar BI Avaliacao Fisica", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar lista detalhada de itens de avaliação física para Business Intelligence",
            notes = "Retorna uma lista paginada e filtrada de itens de avaliação física com informações detalhadas para análise de Business Intelligence. Cada item contém dados do aluno (código, nome, matrícula), datas de avaliação, avaliador responsável, professor e informações específicas conforme o indicador selecionado (observações, diferenças de evolução, modalidades). Permite filtrar por período, indicador específico, professor, avaliador, objetivo e busca por nome/matrícula do aluno.",
            tags = "BI Avaliação Física"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", dataType = "int", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", dataType = "int", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAvaliacao,DESC')", dataType = "string", paramType = "query", defaultValue = "dataAvaliacao,DESC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Lista paginada de itens de avaliação física para BI)", response = ExemploRespostaListItemAvaliacaoFisicaTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/avaliacoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> motarListaAvaliacoesBI(
            @ApiParam(value = "Identificador da empresa", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno ou matrícula.<br/>" +
                    "- <strong>dataInicio:</strong> Data de início do período em timestamp (ex: 1717200000000).<br/>" +
                    "- <strong>dataFim:</strong> Data de fim do período em timestamp (ex: 1719792000000).<br/>" +
                    "- <strong>professorId:</strong> Código do professor responsável pelas avaliações.<br/>" +
                    "- <strong>indicador:</strong> Indicador específico de avaliação física (ex: 'IND_AF_REALIZADAS', 'IND_AF_NOVAS', 'IND_AF_REAVALIACOES', 'IND_AF_ATRASADAS', 'IND_AF_PERDERAM_PESO', 'IND_AF_GANHARAM_MASSA', 'IND_AF_PARQ_POSITIVO', 'IND_AF_OBJETIVOS').<br/>" +
                    "- <strong>objetivo:</strong> Objetivo específico da avaliação física para filtro (ex: 'Emagrecimento', 'Ganho de massa').<br/>" +
                    "- <strong>tipoAvaliacao:</strong> Tipo de avaliação ('TODOS_PROFESSORES', 'ALUNOS_SEM_VINCULO', 'TODOS_PROFESSORES_INATIVOS').<br/>" +
                    "- <strong>avaliadorId:</strong> Código do avaliador físico responsável pelas avaliações.",
                    defaultValue = "{\"quicksearchValue\":\"João\", \"dataInicio\":1717200000000, \"dataFim\":1719792000000, \"professorId\":123, \"indicador\":\"IND_AF_REALIZADAS\", \"objetivo\":\"Emagrecimento\", \"tipoAvaliacao\":\"TODOS_PROFESSORES\", \"avaliadorId\":456}",
                    required = false)
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO){
        try{
            FiltrosAvaliacaoJSON filtro = new FiltrosAvaliacaoJSON(filtros);
            if ( null != filtro.getTipoAvaliacao() && filtro.getTipoAvaliacao().equals("ALUNOS_SEM_VINCULO")){
                filtro.setCodProfessor(TipoBIAvaliacaoEnum.ALUNOS_SEM_VINCULO.getId());
            }
            if (null != filtro.getTipoAvaliacao() && filtro.getTipoAvaliacao().equals("TODOS_PROFESSORES_INATIVOS")){
                filtro.setCodProfessor(TipoBIAvaliacaoEnum.TODOS_PROFESSORES_INATIVOS.getId());
            }
            final String chave = sessaoService.getUsuarioAtual().getChave();
            if ( filtro.getCodProfessor() != 0 && filtro.getCodProfessor() != Integer.MAX_VALUE && filtro.getCodProfessor() != Integer.MAX_VALUE-1){
                ProfessorSinteticoDao professorSinteticoDao = UtilContext.getBean(ProfessorSinteticoDao.class);
                ProfessorSintetico professorSintetico = professorSinteticoDao.obterPorIdColaborador(chave,filtro.getCodProfessor());
                filtro.setCodProfessor(professorSintetico == null ? 0 : professorSintetico.getCodigo());
            }
            Integer codigoAvaliador = filtro.getCodAvaliador();

            if (filtro.getCodAvaliador() == null) {
                try {
                    Integer usuarioLogado = sessaoService.getUsuarioAtual().getId();
                    codigoAvaliador = usuarioLogado;
                } catch (Exception ignore) {}
            }
            if (UteisValidacao.notEmptyNumber(filtro.getCodAvaliador()) && filtro.getCodAvaliador() > 0) {
                try {
                    Usuario us = usuarioService.consultarProColaborador(chave, filtro.getCodAvaliador());
                    if (us != null) {
                        codigoAvaliador = us.getCodigo();
                    }
                } catch (Exception ignore) {}
            }
            return ResponseEntityFactory.ok(biAvaliacaoFisicaService.listaBI(
                    chave,
                    empresaId,
                    IndicadorAvaliacaoFisicaEnum.valueOf(filtro.getIndicador()),
                    filtro.getDataInicio(),
                    filtro.getDataFim(),
                    filtro.getObjetivo(),
                    filtro.getCodProfessor(),
                    filtro.getParametro(),
                    paginadorDTO,
                    codigoAvaliador), paginadorDTO);

        }catch (ServiceException e){
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar BI Avaliacao Fisica", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Exportar lista de avaliações físicas para arquivo",
            notes = "Exporta a lista filtrada de avaliações físicas para um arquivo no formato especificado (Excel, PDF, etc.). O arquivo gerado contém informações detalhadas como código do cliente, nome do aluno, data da avaliação, avaliador, professor e observações.",
            tags = "BI Avaliação Física"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada", dataType = "int", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página", dataType = "int", paramType = "query", defaultValue = "1000"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC' (ex: 'dataAvaliacao,DESC')", dataType = "string", paramType = "query", defaultValue = "dataAvaliacao,DESC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (URL do arquivo exportado)", response = ExemploRespostaExportacaoAvaliacoesBI.class)
    })
    @ResponseBody
    @RequestMapping(value = "/avaliacoes/exportar", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> exportarListaAvaliacoesBI(
            @ApiParam(value = "Identificador da empresa", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Filtros de busca para exportação, deve ser informado como um JSON.<br/><br/>" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong><br/><br/>" +
                    "<strong>Filtros disponíveis:</strong><br/>" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno ou matrícula.<br/>" +
                    "- <strong>dataInicio:</strong> Data de início do período em timestamp.<br/>" +
                    "- <strong>dataFim:</strong> Data de fim do período em timestamp.<br/>" +
                    "- <strong>professorId:</strong> Código do professor responsável.<br/>" +
                    "- <strong>indicador:</strong> Indicador específico de avaliação física.<br/>" +
                    "- <strong>objetivo:</strong> Objetivo específico da avaliação física.<br/>" +
                    "- <strong>tipoAvaliacao:</strong> Tipo de avaliação para filtro.<br/>" +
                    "- <strong>avaliadorId:</strong> Código do avaliador físico.",
                    defaultValue = "{\"quicksearchValue\":\"João\", \"dataInicio\":1717200000000, \"dataFim\":1719792000000, \"professorId\":123, \"indicador\":\"IND_AF_REALIZADAS\", \"objetivo\":\"Emagrecimento\", \"avaliadorId\":456}",
                    required = false)
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "URL base do sistema de treino", defaultValue = "https://treino.exemplo.com", required = false)
            @RequestParam(value = "urlTreino", required = false) String urlTreino,
            @ApiParam(value = "Formato do arquivo de exportação (excel, pdf, csv)", defaultValue = "excel", required = false)
            @RequestParam(value = "format", required = false) String format,
            @ApiIgnore PaginadorDTO paginadorDTO) throws Exception {
        try{
            FiltrosAvaliacaoJSON filtro = new FiltrosAvaliacaoJSON(filtros);
            final String chave = sessaoService.getUsuarioAtual().getChave();
            List<ItemAvaliacaoFisicaTO> listaObj = biAvaliacaoFisicaService.listaBI(
                    chave,
                    empresaId,
                    IndicadorAvaliacaoFisicaEnum.valueOf(filtro.getIndicador()),
                    filtro.getDataInicio(),
                    filtro.getDataFim(),
                    filtro.getObjetivo(),
                    filtro.getCodProfessor(),
                    filtro.getParametro(),
                    paginadorDTO,
                    filtro.getCodAvaliador());

            List<Map<String, Object>> lista = new ArrayList<>();
            for (ItemAvaliacaoFisicaTO item : listaObj) {
                Map<String, Object> map = new HashMap<>();
                if (null != item.getCodCliente()){
                    map.put("Codigo do cliente", item.getCodCliente());
                }
                if (null != item.getNome()){
                    map.put("Aluno", item.getNome());
                }
                if (null != item.getDataAvaliacao()){
                    map.put("Dia", item.getDataAvaliacao());
                }
                if (null != item.getAvaliador()){
                    map.put("Avaliador", item.getAvaliador());
                }
                if (null != item.getProfessor()){
                    map.put("Professor", item.getProfessor());
                }
                if (null != item.getObs()){
                    map.put("Observação", item.getObs());
                }
                if (null != item.getDiff()){
                    map.put("Diff", item.getDiff());
                }
                if (null != item.getMatricula()){
                    map.put("Matricula do cliente", item.getMatricula());
                }
                lista.add(map);
            }

            String url = "";
//            url = processarUrl( url, format, urlTreino, lista);
            return ResponseEntityFactory.ok(url);

        }catch (ServiceException e){
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar BI Avaliacao Fisica", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


}
