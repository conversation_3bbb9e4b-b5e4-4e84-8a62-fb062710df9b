package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações completas da aula coletiva")
public class AulaColetivaResponseDTO {
    @ApiModelProperty(value = "Código identificador da aula coletiva", example = "1")
    private Integer codigo;
    @ApiModelProperty(value = "Descrição da aula coletiva", example = "Aula Spinning")
    private String descricao;
    @ApiModelProperty(value = "Identificador interno da aula coletiva", example = "SPINNINGS01")
    private String identificador;
    @ApiModelProperty(value = "Cor representativa da aula", example = "#FF5733")
    private String cor;
    @ApiModelProperty(value = "Código da empresa no sistema Zillyon", example = "1")
    private Integer empresa; // código zw da empresa
    @ApiModelProperty(value = "Meta estabelecida para a aula", example = "30.0")
    private Double meta;
    @ApiModelProperty(value = "Pontuação bônus por participação na aula", example = "100")
    private Integer pontuacaoBonus;
    @ApiModelProperty(value = "Bonificação concedida pela aula", example = "50.0")
    private Double bonificacao;
    @ApiModelProperty(value = "Mensagem complementar da aula", example = "Aula de Spinning a partir da reserva de aula")
    private String mensagem;
    @ApiModelProperty(value = "Ocupação máxima da aula", example = "20")
    private Integer ocupacao;
    @ApiModelProperty(value = "Código da modalidade vinculada à aula", example = "2")
    private Integer modalidadeId;
    @ApiModelProperty(value = "Data de início da aula (timestamp)", example = "1750032000000")
    private Long dataInicio; // dataInicio: 1538362800000
    @ApiModelProperty(value = "Data final da aula (timestamp)", example = "1752706800000")
    private Long dataFinal; // dataFinal: 1540954800000
    @ApiModelProperty(value = "Tolerância para entrada em minutos", example = "10")
    private Integer toleranciaMin;
    @ApiModelProperty(value = "Tipo de tolerância: 1 (Após início), 2 (Antes do início)", example = "1", allowableValues = "1,2")
    private Integer tipoTolerancia;
    @ApiModelProperty(value = "Indica se deve validar restrições de marcação", example = "true")
    private Boolean validarRestricoesMarcacao;
    @ApiModelProperty(value = "Indica se NÃO deve validar modalidade no contrato", example = "false")
    private Boolean naoValidarModalidadeContrato;
    @ApiModelProperty(value = "Código do produto GymPass vinculado à aula", example = "3")
    private Integer produtoGymPass;
    @ApiModelProperty(value = "ID da classe no GymPass", example = "456")
    private Integer idClasseGymPass;
    @ApiModelProperty(value = "URL da turma virtual", example = "https://pactosolucoes.com.br/turma-virtual/123")
    private String urlTurmaVirtual;
    @ApiModelProperty(value = "URL da imagem da aula", example = "https://pactosolucoes.com.br/imagens/aula123.png")
    private String imageUrl;
    @ApiModelProperty(value = "Data do upload da imagem", example = "2025-05-10T15:00:00Z")
    private String imageDataUpload;
    @ApiModelProperty(value = "Indica se permite fixar a aula", example = "true")
    private Boolean permiteFixar;
    @ApiModelProperty(value = "Indica se possui integração com Selfloops", example = "false")
    private Boolean aulaIntegracaoSelfloops;
    @ApiModelProperty(value = "Indica se permite visualizar produtos do Gympass", example = "true")
    private Boolean visualizarProdutosGympass;
    @ApiModelProperty(value = "Indica se permite visualizar produtos do Totalpass", example = "false")
    private Boolean visualizarProdutosTotalpass;
    @ApiModelProperty(value = "Lista de níveis associados à aula")
    private List<NivelTO> niveis;
    @ApiModelProperty(value = "Idade máxima em anos permitida para a aula", example = "65")
    private Integer idadeMaximaAnos;
    @ApiModelProperty(value = "Idade máxima em meses permitida para a aula", example = "780")
    private Integer idadeMaximaMeses;
    @ApiModelProperty(value = "Idade mínima em anos permitida para a aula", example = "18")
    private Integer idadeMinimaAnos;
    @ApiModelProperty(value = "Idade mínima em meses permitida para a aula", example = "216")
    private Integer idadeMinimaMeses;
    @ApiModelProperty(value = "Links dos vídeos da turma")
    private List<TurmaVideoDTO> linkVideos;
    @ApiModelProperty(value = "Tipo da reserva de equipamento", example = "Reservado por Aluno")
    private String tipoReservaEquipamento;
    @ApiModelProperty(value = "Descrição do mapa de equipamentos", example = "Mapa das bicicletas")
    private String mapaEquipamentos;
    @ApiModelProperty(value = "Lista de equipamentos por turma")
    private List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho;
    @ApiModelProperty(value = "Lista geral de equipamentos utilizados")
    private List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho;
    @ApiModelProperty(value = "Horários em que a aula será oferecida")
    private List<HorarioTurmaResponseDTO> horarios;

    public AulaColetivaResponseDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Double getMeta() {
        return meta;
    }

    public void setMeta(Double meta) {
        this.meta = meta;
    }

    public Integer getPontuacaoBonus() {
        return pontuacaoBonus;
    }

    public void setPontuacaoBonus(Integer pontuacaoBonus) {
        this.pontuacaoBonus = pontuacaoBonus;
    }

    public Double getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Double bonificacao) {
        this.bonificacao = bonificacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getModalidadeId() {
        return modalidadeId;
    }

    public void setModalidadeId(Integer modalidadeId) {
        this.modalidadeId = modalidadeId;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Long dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getToleranciaMin() {
        return toleranciaMin;
    }

    public void setToleranciaMin(Integer toleranciaMin) {
        this.toleranciaMin = toleranciaMin;
    }

    public Integer getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public Boolean getValidarRestricoesMarcacao() {
        if (validarRestricoesMarcacao == null) {
            return false;
        }
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(Boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public Boolean getNaoValidarModalidadeContrato() {
        if (naoValidarModalidadeContrato == null) {
            return false;
        }
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(Boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageDataUpload() {
        return imageDataUpload;
    }

    public void setImageDataUpload(String imageDataUpload) {
        this.imageDataUpload = imageDataUpload;
    }

    public Boolean getPermiteFixar() {
        if (permiteFixar == null) {
            return false;
        }
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public Boolean getAulaIntegracaoSelfloops() {
        if (aulaIntegracaoSelfloops == null) {
            return false;
        }
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(Boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }

    public Boolean getVisualizarProdutosGympass() {
        if (visualizarProdutosGympass == null) {
            return false;
        }
        return visualizarProdutosGympass;
    }

    public void setVisualizarProdutosGympass(Boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public Boolean getVisualizarProdutosTotalpass() {
        if (visualizarProdutosTotalpass == null) {
            return false;
        }
        return visualizarProdutosTotalpass;
    }

    public void setVisualizarProdutosTotalpass(Boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public List<NivelTO> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<NivelTO> niveis) {
        this.niveis = niveis;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public List<TurmaMapaEquipamentoAparelhoDTO> getTurmaMapaEquipamentoAparelho() {
        return turmaMapaEquipamentoAparelho;
    }

    public void setTurmaMapaEquipamentoAparelho(List<TurmaMapaEquipamentoAparelhoDTO> turmaMapaEquipamentoAparelho) {
        this.turmaMapaEquipamentoAparelho = turmaMapaEquipamentoAparelho;
    }

    public List<MapaEquipamentoAparelhoDTO> getListaMapaEquipamentoAparelho() {
        return listaMapaEquipamentoAparelho;
    }

    public void setListaMapaEquipamentoAparelho(List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho) {
        this.listaMapaEquipamentoAparelho = listaMapaEquipamentoAparelho;
    }

    public List<HorarioTurmaResponseDTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<HorarioTurmaResponseDTO> horarios) {
        this.horarios = horarios;
    }
}
