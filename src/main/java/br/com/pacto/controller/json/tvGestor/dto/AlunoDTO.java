package br.com.pacto.controller.json.tvGestor.dto;

import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados do aluno para exibição na TV Gestor")
public class AlunoDTO {

    @ApiModelProperty(value = "Código identificador único do aluno", example = "12345")
    private Long codigoAluno;

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "2024001")
    private String matriculaAluno;

    @ApiModelProperty(value = "Nome completo do aluno (abreviado para exibição)", example = "João Silva")
    private String nomeAluno;

    @ApiModelProperty(value = "Nome da turma em que o aluno está matriculado", example = "Musculação Manhã")
    private String nomeTurma;

    @ApiModelProperty(value = "Data e hora de início da turma formatada", example = "2024-01-15 08:00")
    private String dataHoraInicioTurma;

    @ApiModelProperty(value = "Data e hora de fim da turma formatada", example = "2024-01-15 09:00")
    private String dataHoraFimTurma;

    @ApiModelProperty(value = "Data e hora do último acesso do aluno formatada", example = "2024-01-15 08:15")
    private String dataHoraAcesso;

    @ApiModelProperty(value = "URL da imagem/foto do aluno", example = "https://exemplo.com/fotos/aluno12345.jpg")
    private String imageUri;

    public Long getCodigoAluno() {
        return codigoAluno;
    }

    public void setCodigoAluno(Long codigoAluno) {
        this.codigoAluno = codigoAluno;
    }

    public String getMatriculaAluno() {
        return matriculaAluno;
    }

    public void setMatriculaAluno(String matriculaAluno) {
        this.matriculaAluno = matriculaAluno;
    }

    public String getNomeAluno() {
        if (StringUtils.isNotBlank(nomeAluno)) {
            return Uteis.getNomeAbreviandoSomentePrimeiroSobrenome(nomeAluno);
        } else {
            return "";
        }
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getNomeTurma() {
        return nomeTurma;
    }

    public void setNomeTurma(String nomeTurma) {
        this.nomeTurma = nomeTurma;
    }

    public String getDataHoraInicioTurma() {
        return dataHoraInicioTurma;
    }

    public void setDataHoraInicioTurma(String dataHoraInicioTurma) {
        this.dataHoraInicioTurma = dataHoraInicioTurma;
    }

    public String getDataHoraFimTurma() {
        return dataHoraFimTurma;
    }

    public void setDataHoraFimTurma(String dataHoraFimTurma) {
        this.dataHoraFimTurma = dataHoraFimTurma;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getDataHoraAcesso() {
        return dataHoraAcesso;
    }

    public void setDataHoraAcesso(String dataHoraAcesso) {
        this.dataHoraAcesso = dataHoraAcesso;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AlunoDTO alunoDTO = (AlunoDTO) o;
        return Objects.equals(codigoAluno, alunoDTO.codigoAluno) &&
                Objects.equals(dataHoraInicioTurma, alunoDTO.dataHoraInicioTurma);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codigoAluno, dataHoraInicioTurma);
    }
}
