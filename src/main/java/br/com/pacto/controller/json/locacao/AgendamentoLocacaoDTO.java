package br.com.pacto.controller.json.locacao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Dados para agendamento de locação, incluindo informações do cliente, ambiente, valores e serviços.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgendamentoLocacaoDTO {

    @ApiModelProperty(value = "Código da pessoa/cliente que está agendando.", example = "123")
    private Integer codigoPessoa;

    @ApiModelProperty(value = "Matrícula do cliente.", example = "12345")
    private String matricula;

    @ApiModelProperty(value = "Código do ambiente onde será realizada a locação.", example = "1")
    private Integer ambiente;

    @ApiModelProperty(value = "Valor total do agendamento incluindo locação e serviços.", example = "190.0")
    private Double valorTotal;

    @ApiModelProperty(value = "Valor da locação do espaço.", example = "150.0")
    private Double valorLocacao;

    @ApiModelProperty(value = "Valor por hora da locação.", example = "50.0")
    private Double valorHora;

    @ApiModelProperty(value = "Lista de horários selecionados para o agendamento.")
    private List<LocacaoHorarioTO> horarios;

    @ApiModelProperty(value = "Lista de serviços/produtos adicionais contratados.")
    private List<LocacaoProdutoSugeridoTO> servicos;

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public List<LocacaoHorarioTO> getHorarios() {
        return horarios;
    }

    public void setHorarios(List<LocacaoHorarioTO> horarios) {
        this.horarios = horarios;
    }

    public List<LocacaoProdutoSugeridoTO> getServicos() {
        return servicos;
    }

    public void setServicos(List<LocacaoProdutoSugeridoTO> servicos) {
        this.servicos = servicos;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Double getValorLocacao() {
        return valorLocacao;
    }

    public void setValorLocacao(Double valorLocacao) {
        this.valorLocacao = valorLocacao;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Double getValorHora() {
        return valorHora;
    }

    public void setValorHora(Double valorHora) {
        this.valorHora = valorHora;
    }
}
