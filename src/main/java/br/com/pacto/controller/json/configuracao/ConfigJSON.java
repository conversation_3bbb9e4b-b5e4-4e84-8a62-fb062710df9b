/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.configuracao;

import br.com.pacto.controller.json.base.SuperJSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Objeto que representa uma configuração específica do sistema")
public class ConfigJSON extends SuperJSON {

    @ApiModelProperty(value = "Identificador da configuração", example = "MODULO_AULAS")
    private String id;

    @ApiModelProperty(value = "Valor da configuração", example = "true")
    private String valor;

    @ApiModelProperty(value = "Tipo da configuração", example = "BOOLEAN")
    private String tipo;

    public ConfigJSON() {
    }

    public ConfigJSON(String id, String valor, String tipo) {
        this.id = id;
        this.valor = valor;
        this.tipo = tipo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
