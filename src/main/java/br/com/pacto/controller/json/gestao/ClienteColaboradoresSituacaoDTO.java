package br.com.pacto.controller.json.gestao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude
@ApiModel(description = "Situação detalhada do colaborador em relação ao cliente.")
public class ClienteColaboradoresSituacaoDTO {

    @ApiModelProperty(value = "Tipo da situação", example = "Plano")
    private String tipo;

    @ApiModelProperty(value = "Situação do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)\n", example = "AT")
    private String situacao;

    @ApiModelProperty(value = "Situação do contrato.\n\n" +
            "<strong>Valores disponíveis:</strong>\n" +
            "- NO - Normal\n" +
            "- AV - A vencer\n" +
            "- ATM - Atestado Médico\n" +
            "- CR - Carência\n" +
            "- CA - Cancelado\n" +
            "- DE - Desistente\n" +
            "- VE - Inativo Vencido\n" +
            "- TV - Trancado Vencido\n" +
            "- DI - Diária\n" +
            "- PE - Free Pass\n" +
            "- AA - Aula Avulsa", example = "NO")
    private String situacaoContrato;

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}
