package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * Created by paulo on 09/07/2019.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do contrato do aluno")
public class ContratoZWDTO {
    @ApiModelProperty(value = "Data de vencimento do contrato", example = "2026-01-01T12:00:00Z")
    private Date vencimento;
    @ApiModelProperty(value = "Tipo do contrato.\n\n" +
            "<strong>Valores disponíveis</strong>" +
            "MA - Matrícula" +
            "RE - Rematrícula" +
            "RN - Renovação",
            example = "MA")
    private TipoContratoZWEnum tipo;

    public ContratoZWDTO(Date vencimento, TipoContratoZWEnum tipo) {
        this.vencimento = vencimento;
        this.tipo = tipo;
    }

    public Date getVencimento() {
        return vencimento;
    }

    public void setVencimento(Date vencimento) {
        this.vencimento = vencimento;
    }

    public TipoContratoZWEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoContratoZWEnum tipo) {
        this.tipo = tipo;
    }
}
