package br.com.pacto.controller.json.locacao;

import br.com.pacto.bean.locacao.LocacaoHorario;
import br.com.pacto.controller.json.ambiente.AmbienteHorarioLocacaoTO;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.DiasSemana;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "Horário de funcionamento de uma locação, incluindo dia da semana, horários, responsável e valores.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocacaoHorarioTO {

    @ApiModelProperty(value = "Código único do horário.", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Dia da semana do horário.", example = "Segunda-feira")
    private String diaSemana;

    @ApiModelProperty(value = "Usuário responsável pelo horário.")
    private UsuarioTO responsavel;

    @ApiModelProperty(value = "Horário de início no formato HH:mm.", example = "08:00")
    private String horaInicio;

    @ApiModelProperty(value = "Horário de fim no formato HH:mm.", example = "09:00")
    private String horaFim;

    @ApiModelProperty(value = "Indica se permite agendamento pelo app de treino.", example = "true")
    private Boolean permiteAgendarPeloAppTreino;

    @ApiModelProperty(value = "Lista de ambientes disponíveis neste horário.")
    private List<AmbienteHorarioLocacaoTO> ambientes;

    @ApiModelProperty(value = "Indica se possui agendamentos neste horário.", example = "false")
    private Boolean possuiAgendamentos;

    @ApiModelProperty(value = "Indica se o horário está ativo.", example = "true")
    private Boolean ativo;

    @ApiModelProperty(value = "Indica se o horário foi finalizado.", example = "false")
    private Boolean finalizado;

    @ApiModelProperty(value = "Valor da locação para este horário.", example = "150.0")
    private Double valorLocacao;

    @ApiModelProperty(value = "Valor dos extras obrigatórios.", example = "25.0")
    private Double valorExtrasObrigatorios;

    @ApiModelProperty(value = "Valor dos extras adicionais.", example = "15.0")
    private Double valorExtrasAdicionais;

    @ApiModelProperty(value = "Valor total incluindo todos os custos.", example = "190.0")
    private Double valorTotal;

    @ApiModelProperty(value = "Código da venda avulsa associada.", example = "123")
    private Integer vendaAvulsaCodigo;

    @ApiModelProperty(value = "Tempo mínimo em minutos para este horário.", example = "15")
    private Integer tempoMinimoMinutos;

    public LocacaoHorarioTO() {}
    public LocacaoHorarioTO(LocacaoHorario locacaoHorario) {
        if (locacaoHorario != null) {
            this.codigo = locacaoHorario.getCodigo();
            if (!UteisValidacao.emptyString(locacaoHorario.getDiaSemana())) {
                this.diaSemana = DiasSemana.getDiaSemana(locacaoHorario.getDiaSemana()).getCodigo();
            }
            this.responsavel = new UsuarioTO();
            this.responsavel.setCodigo(locacaoHorario.getResponsavel());
            this.horaInicio = locacaoHorario.getHoraInicio();
            this.horaFim = locacaoHorario.getHoraFim();
            this.permiteAgendarPeloAppTreino = locacaoHorario.getPermiteAgendarPeloAppTreino();
            if (locacaoHorario.getAmbientes() != null) {
                setAmbientes(new ArrayList<>());
                locacaoHorario.getAmbientes().forEach(a -> {
                    AmbienteHorarioLocacaoTO ambiente = new AmbienteHorarioLocacaoTO(a);
                    getAmbientes().add(ambiente);
                });
            }
            this.possuiAgendamentos = false;
            this.ativo = locacaoHorario.getAtivo();
            this.tempoMinimoMinutos = locacaoHorario.getTempoMinimoMinutos();
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDiaSemana() {
        return diaSemana;
    }

    public void setDiaSemana(String diaSemana) {
        this.diaSemana = diaSemana;
    }

    public UsuarioTO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioTO responsavel) {
        this.responsavel = responsavel;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraFim() {
        return horaFim;
    }

    public void setHoraFim(String horaFim) {
        this.horaFim = horaFim;
    }

    public Boolean getPermiteAgendarPeloAppTreino() {
        return permiteAgendarPeloAppTreino;
    }

    public void setPermiteAgendarPeloAppTreino(Boolean permiteAgendarPeloAppTreino) {
        this.permiteAgendarPeloAppTreino = permiteAgendarPeloAppTreino;
    }

    public List<AmbienteHorarioLocacaoTO> getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(List<AmbienteHorarioLocacaoTO> ambientes) {
        this.ambientes = ambientes;
    }

    public Boolean getPossuiAgendamentos() {
        return possuiAgendamentos;
    }

    public void setPossuiAgendamentos(Boolean possuiAgendamentos) {
        this.possuiAgendamentos = possuiAgendamentos;
    }

    public String getInicioFim(){
        return horaInicio + " - " + horaFim;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getFinalizado() {
        return finalizado;
    }

    public void setFinalizado(Boolean finalizado) {
        this.finalizado = finalizado;
    }

    public Double getValorLocacao() {
        return valorLocacao;
    }

    public void setValorLocacao(Double valorLocacao) {
        this.valorLocacao = valorLocacao;
    }

    public Double getValorExtrasObrigatorios() {
        return valorExtrasObrigatorios;
    }

    public void setValorExtrasObrigatorios(Double valorExtrasObrigatorios) {
        this.valorExtrasObrigatorios = valorExtrasObrigatorios;
    }

    public Double getValorExtrasAdicionais() {
        return valorExtrasAdicionais;
    }

    public void setValorExtrasAdicionais(Double valorExtrasAdicionais) {
        this.valorExtrasAdicionais = valorExtrasAdicionais;
    }

    public Double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public Integer getVendaAvulsaCodigo() {
        return vendaAvulsaCodigo;
    }

    public void setVendaAvulsaCodigo(Integer vendaAvulsaCodigo) {
        this.vendaAvulsaCodigo = vendaAvulsaCodigo;
    }

    public Integer getTempoMinimoMinutos() {
        return tempoMinimoMinutos;
    }

    public void setTempoMinimoMinutos(Integer tempoMinimoMinutos) {
        this.tempoMinimoMinutos = tempoMinimoMinutos;
    }
}
