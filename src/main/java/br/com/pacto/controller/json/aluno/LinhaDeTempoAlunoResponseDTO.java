package br.com.pacto.controller.json.aluno;

import br.com.pacto.util.json.LinhaDoTempoTO;
import br.com.pacto.util.json.TipoLinhaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * Created paulo 07/11/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Representa um evento na linha do tempo do aluno.")
public class LinhaDeTempoAlunoResponseDTO {

    @ApiModelProperty(value = "Data do evento na linha do tempo", example = "2025-06-10T14:30:00Z")
    private Date data;

    @ApiModelProperty(value = "Tipo de evento registrado na linha do tempo.\n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- 0 - TREINOU\n" +
            "- 1 - AGENDAMENTO\n" +
            "- 2 - REVISOU_TREINO\n" +
            "- 3 - RENOVOU_TREINO\n" +
            "- 4 - ACABOU_TREINO\n" +
            "- 5 - NOTIFICACAO\n" +
            "- 6 - MONTOU_TREINO\n" +
            "- 7 - MUDOU_DE_NIVEL\n" +
            "- 8 - GANHOU_BADGE\n" +
            "- 9 - FEZ_AULA\n" +
            "- 10 - REALIZOU_AVALIACAO\n" +
            "- 11 - REGISTROU_WOD\n" +
            "- 12 - AGENDOU_BOOKING_GYMPASS\n" +
            "- 13 - ALTERACAO_AGENDAMENTO_SERVICOS\n" +
            "- 14 - AULA_DESMARCADA", example = "TREINOU")
    private TipoLinhaEnum evento;

    @ApiModelProperty(value = "Cor associada ao evento na linha do tempo", example = "#FF5733")
    private String cor;

    @ApiModelProperty(value = "Descrição do evento", example = "Treinou na Academia Pacto GO.")
    private String descricao;

    @ApiModelProperty(value = "Nome do usuário que realizou a ação", example = "marcos.silva")
    private String usuario;

    @ApiModelProperty(value = "Origem da ação registrada", example = "1")
    private String origem;


    public LinhaDeTempoAlunoResponseDTO(LinhaDoTempoTO linhaDoTempoTO) {
        this.data = linhaDoTempoTO.getData();
        this.evento = TipoLinhaEnum.valueOf(linhaDoTempoTO.getTipo());
        this.cor = linhaDoTempoTO.getClasseCss().getCor();

        switch (TipoLinhaEnum.valueOf(linhaDoTempoTO.getTipo())) {
            case AGENDAMENTO:
                this.descricao = "Agendamento: " + linhaDoTempoTO.getTitulo();
                break;
            case TREINOU:
                this.descricao = "Executou a ficha de treino: " + linhaDoTempoTO.getNomeFichaExecucao();
                this.origem = "Origem: " + linhaDoTempoTO.getOrigem();
                break;
            case NOTIFICACAO:
                this.descricao = "Notificação: " + linhaDoTempoTO.getSubtitulo();
                break;
            case MUDOU_DE_NIVEL:
                this.descricao = "Mudou de nível: " + linhaDoTempoTO.getNivelNovo();
                break;
            case REVISOU_TREINO:
                this.descricao = "Revisou treino: " + linhaDoTempoTO.getSubtitulo().replace("Programa de treino: ", "");
                break;
            case MONTOU_TREINO:
                this.descricao = "Montou treino: " + linhaDoTempoTO.getSubtitulo().replace("Programa de treino: ", "");
                break;
            case RENOVOU_TREINO:
                this.descricao = "Renovou treino: " + linhaDoTempoTO.getSubtitulo().replace("Programa de treino: ", "");
                break;
            case ACABOU_TREINO:
                this.descricao = "Acabou treino: " + linhaDoTempoTO.getSubtitulo().replace("Programa de treino: ", "");
                break;
            case GANHOU_BADGE:
                this.descricao = linhaDoTempoTO.getNomeBadge();
                break;
            case FEZ_AULA:
                this.descricao = "Agendou aula coletiva: " + linhaDoTempoTO.getSubtitulo();
                this.descricao = "Aula marcada: " + linhaDoTempoTO.getSubtitulo();
                break;
            case AULA_DESMARCADA:
                this.descricao = "Aula desmarcada: " + linhaDoTempoTO.getSubtitulo();
                this.usuario = linhaDoTempoTO.getNomeProfessor();
                break;
            case REALIZOU_AVALIACAO:
                if (linhaDoTempoTO.getNomeProfessorMinusculo() != null && !linhaDoTempoTO.getNomeProfessorMinusculo().equals("")) {
                    this.descricao = linhaDoTempoTO.getSubtitulo() + " " + linhaDoTempoTO.getNomeProfessorMinusculo();
                } else {
                    this.descricao = "Realizou Avaliação Física";
                }
                break;
            case REGISTROU_WOD:
                this.descricao = "Registrou resultado de wod: " + linhaDoTempoTO.getSubtitulo().replace("Registrou resultado de wod: ", "");
                break;
            case AGENDOU_BOOKING_GYMPASS:
                if (linhaDoTempoTO.getSubtitulo().equals("Link de visitantes")) {
                    this.descricao = "Agendou aula via: " + linhaDoTempoTO.getSubtitulo().replace("Agendou aula via Booking Gympass: ", "");
                } else {
                    this.descricao = "Agendou aula via Booking Gympass: " + linhaDoTempoTO.getSubtitulo().replace("Agendou aula via Booking Gympass: ", "");
                }
                break;
            case ALTERACAO_AGENDAMENTO_SERVICOS:
                this.descricao = "Status do agendamento " + linhaDoTempoTO.getSubtitulo().replace("Status do agendamento ", "");
                break;
        }
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public TipoLinhaEnum getEvento() {
        return evento;
    }

    public void setEvento(TipoLinhaEnum evento) {
        this.evento = evento;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

}
