package br.com.pacto.controller.json.atividade;

import br.com.pacto.bean.atividade.*;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 01/02/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da atividade CrossFit")
public class AtividadeCrossfitResponseDTO {

    @ApiModelProperty(value = "Código único identificador da atividade CrossFit", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade física CrossFit", example = "Power Clean")
    private String nome;
    @ApiModelProperty(value = "Categoria da atividade CrossFit.\n\n" +
            "**Valores disponíveis**\n" +
            "- Nenhum (NENHUM)\n" +
            "- 1 - <PERSON>bell (BARBELL)\n" +
            "- 2 - Gymnastic (GYMNASTIC)\n" +
            "- 3 - Endurance (ENDURANCE)\n" +
            "- 4 - Notables (NOTABLES)\n" +
            "- 5 - Girls (Girls)\n" +
            "- 6 - Open (OPEN)\n" +
            "- 7 - The Heroes (THEHEROES)\n" +
            "- 8 - Campeonatos (CAMPEONATOS)\n" +
            "- 9 - Crossfit Games (CROSSFIT_GAMES)",
            example = "1", allowableValues = "0,1,2,3,4,5,6,7,8,9")
    private CategoriaAtividadeWodEnum categoria;
    @ApiModelProperty(value = "Unidade medida durante a execução dessa atividade.\n\n" +
            "**Valores disponíveis**\n" +
            "- 0 - Nenhum (NENHUM)\n" +
            "- 1 - Reps (REPS)\n" +
            "- 2 - Time (Time)\n" +
            "- 3 - Distance (DISTANCE)\n" +
            "- 4 - Weight (WEIGHT)\n" +
            "- 5 - Cals (CALS)\n" +
            "- 6 - Reps For Time (REPSFORTIME)\n" +
            "- 7 - Rounds (ROUNDS)\n" +
            "- 8 - Rounds and Reps (ROUNDS_REPS)"
            , example = "1", allowableValues = "0,1,2,3,4,5,6,7,8")
    private UnidadeMedidaEnum unidadeMedida;
    @ApiModelProperty(value = "Indica se a atividade está ativa (true) ou não (false)", example = "true")
    private boolean ativo;
    @ApiModelProperty(value = "Lista de empresas que a atividade está vinculada")
    private List<AtividadeEmpresaResponseTO> empresas = new ArrayList<>();
    @ApiModelProperty(value = "Descrição da atividade crossfit", example = "Atividade PowerClean de Repetição")
    private String descricao;
    @ApiModelProperty(value = "URL do vídeo da atividade", example = "www.youtube.com/video/exemplo")
    private String videoUri;

    public AtividadeCrossfitResponseDTO(Atividade atividade) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.categoria = atividade.getCategoriaAtividadeWod() == CategoriaAtividadeWodEnum.NENHUM ? null : atividade.getCategoriaAtividadeWod();
        this.unidadeMedida = atividade.getUnidadeMedida() == UnidadeMedidaEnum.NENHUM ? null : atividade.getUnidadeMedida();
        this.ativo = atividade.isAtivo();
        this.descricao = atividade.getDescricao();
        this.videoUri = atividade.getLinkVideo();

        if (atividade.getEmpresasHabilitadas() != null && !UteisValidacao.emptyList(atividade.getEmpresasHabilitadas())) {
            for (AtividadeEmpresa empresa : atividade.getEmpresasHabilitadas()) {
                this.empresas.add(new AtividadeEmpresaResponseTO(empresa));
            }
        }
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public CategoriaAtividadeWodEnum getCategoria() {
        return categoria;
    }

    public void setCategoria(CategoriaAtividadeWodEnum categoria) {
        this.categoria = categoria;
    }

    public UnidadeMedidaEnum getUnidadeMedida() {
        return unidadeMedida;
    }

    public void setUnidadeMedida(UnidadeMedidaEnum unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public List<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }
}
