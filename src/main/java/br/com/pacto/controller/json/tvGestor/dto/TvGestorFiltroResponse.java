package br.com.pacto.controller.json.tvGestor.dto;

import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Resposta contendo os filtros disponíveis e salvos para a TV Gestor")
public class TvGestorFiltroResponse {

    @ApiModelProperty(value = "Lista de modalidades disponíveis para filtro")
    private List<ModalidadeResponseTO> modalidades;

    @ApiModelProperty(value = "Lista de ambientes disponíveis para filtro")
    private List<AmbienteResponseTO> ambientes;

    @ApiModelProperty(value = "Lista de colaboradores/professores disponíveis para filtro")
    private List<ColaboradorSimplesTO> colaboradores;

    public TvGestorFiltroResponse(List<ModalidadeResponseTO> modalidades, List<AmbienteResponseTO> ambientes, List<ColaboradorSimplesTO> colaboradores) {
        this.modalidades = modalidades;
        this.ambientes = ambientes;
        this.colaboradores = colaboradores;
    }

    public List<ModalidadeResponseTO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeResponseTO> modalidades) {
        this.modalidades = modalidades;
    }

    public List<AmbienteResponseTO> getAmbientes() {
        return ambientes;
    }

    public void setAmbientes(List<AmbienteResponseTO> ambientes) {
        this.ambientes = ambientes;
    }

    public List<ColaboradorSimplesTO> getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(List<ColaboradorSimplesTO> colaboradores) {
        this.colaboradores = colaboradores;
    }
}
