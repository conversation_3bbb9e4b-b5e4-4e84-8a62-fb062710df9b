package br.com.pacto.controller.json.ficha;

import br.com.pacto.controller.json.atividade.AtividadeTreinoGeradoPorIADTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Ficha de treino gerada por Inteligência Artificial")
public class FichaDeTreinoGeradaPorIADTO {

    @ApiModelProperty(value = "Versão da ficha para controle de alterações", example = "1")
    private int versao;

    @ApiModelProperty(value = "Nome da ficha de treino gerada", example = "Ficha A - Membros Superiores")
    private String nome;

    @ApiModelProperty(value = "Tipo de execução da ficha. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- SEQUENCIAL (Execução sequencial das atividades)\n" +
            "- CIRCUITO (Execução em circuito)\n" +
            "- LIVRE (Execução livre)\n", example = "SEQUENCIAL")
    private String tipo_execucao;

    @ApiModelProperty(value = "Lista de atividades que compõem a ficha. Cada item é uma referência à classe AtividadeTreinoGeradoPorIADTO")
    private List<AtividadeTreinoGeradoPorIADTO> atividades;

    @ApiModelProperty(value = "Indica se a ficha está ativa", example = "true")
    private boolean ativo;

    @ApiModelProperty(value = "Indica se a ficha é pré-definida", example = "false")
    private boolean predefinida;

    public int getVersao() {
        return versao;
    }

    public void setVersao(int versao) {
        this.versao = versao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTipo_execucao() {
        return tipo_execucao;
    }

    public void setTipo_execucao(String tipo_execucao) {
        this.tipo_execucao = tipo_execucao;
    }

    public List<AtividadeTreinoGeradoPorIADTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeTreinoGeradoPorIADTO> atividades) {
        this.atividades = atividades;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isPredefinida() {
        return predefinida;
    }

    public void setPredefinida(boolean predefinida) {
        this.predefinida = predefinida;
    }
}