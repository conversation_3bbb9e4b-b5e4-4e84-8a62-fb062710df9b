package br.com.pacto.controller.json.biapp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados consolidados do dashboard de Business Intelligence do aplicativo")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BiAppDTO {

    @ApiModelProperty(value = "Quantidade total de alunos ativos na empresa", example = "150")
    private Integer ativos;

    @ApiModelProperty(value = "Quantidade de alunos ativos que utilizam o aplicativo", example = "95")
    private Integer ativosComApp;

    @ApiModelProperty(value = "Quantidade de alunos ativos que não utilizam o aplicativo", example = "55")
    private Integer ativosSemApp;

    @ApiModelProperty(value = "Quantidade de alunos inativos que utilizam o aplicativo", example = "12")
    private Integer inativosComApp;

    @ApiModelProperty(value = "Percentual de alunos que utilizam o aplicativo", example = "63")
    private Integer percentualAlunosUsamApp;

    @ApiModelProperty(value = "Data e hora da última atualização dos dados", example = "2024-06-18 14:30:00")
    private String ultimaAtualizacao;

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public Integer getAtivosComApp() {
        return ativosComApp;
    }

    public void setAtivosComApp(Integer ativosComApp) {
        this.ativosComApp = ativosComApp;
    }

    public Integer getAtivosSemApp() {
        return ativosSemApp;
    }

    public void setAtivosSemApp(Integer ativosSemApp) {
        this.ativosSemApp = ativosSemApp;
    }

    public Integer getInativosComApp() {
        return inativosComApp;
    }

    public void setInativosComApp(Integer inativosComApp) {
        this.inativosComApp = inativosComApp;
    }

    public Integer getPercentualAlunosUsamApp() {
        return percentualAlunosUsamApp;
    }

    public void setPercentualAlunosUsamApp(Integer percentualAlunosUsamApp) {
        this.percentualAlunosUsamApp = percentualAlunosUsamApp;
    }

    public String getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }

    public void setUltimaAtualizacao(String ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
    }
}
