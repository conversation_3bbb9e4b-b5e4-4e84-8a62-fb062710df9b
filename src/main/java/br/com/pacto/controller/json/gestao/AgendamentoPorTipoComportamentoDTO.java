package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.TipoEventoDisponibilidadeBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO contendo dados de agendamentos por tipo de comportamento, incluindo estatísticas de execução, cancelamentos e confirmações.")
public class AgendamentoPorTipoComportamentoDTO {

    @ApiModelProperty(value = "Tipo de agendamento ou evento.", example = "Treino Individual")
    private String tipo;

    @ApiModelProperty(value = "Total de disponibilidades oferecidas.", example = "50")
    private Integer disponibilidade;

    @ApiModelProperty(value = "Total de agendamentos executados.", example = "38")
    private Integer executaram;

    @ApiModelProperty(value = "Total de agendamentos cancelados.", example = "7")
    private Integer cancelaram;

    @ApiModelProperty(value = "Total de faltas em agendamentos.", example = "5")
    private Integer faltaram;

    @ApiModelProperty(value = "Total de agendamentos aguardando confirmação.", example = "3")
    private Integer aguardandoConfirmacao;

    @ApiModelProperty(value = "Total de agendamentos confirmados.", example = "45")
    private Integer confirmado;

    public AgendamentoPorTipoComportamentoDTO() {

    }

    public AgendamentoPorTipoComportamentoDTO(TipoEventoDisponibilidadeBI tipo) {
        this.tipo = tipo.getTipo().getDescricao();
        this.disponibilidade = tipo.getHorasDisponibilidade();
        this.executaram = tipo.getHorasExecutaram();
        this.cancelaram = tipo.getHorasCancelaram();
        this.faltaram = tipo.getHorasFaltaram();
        this.aguardandoConfirmacao = tipo.getHorasAguardando();
        this.confirmado = tipo.getHorasConfirmado();
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getDisponibilidade() {
        return disponibilidade;
    }

    public void setDisponibilidade(Integer disponibilidade) {
        this.disponibilidade = disponibilidade;
    }

    public Integer getExecutaram() {
        return executaram;
    }

    public void setExecutaram(Integer executaram) {
        this.executaram = executaram;
    }

    public Integer getCancelaram() {
        return cancelaram;
    }

    public void setCancelaram(Integer cancelaram) {
        this.cancelaram = cancelaram;
    }

    public Integer getFaltaram() {
        return faltaram;
    }

    public void setFaltaram(Integer faltaram) {
        this.faltaram = faltaram;
    }

    public Integer getAguardandoConfirmacao() {
        return aguardandoConfirmacao;
    }

    public void setAguardandoConfirmacao(Integer aguardandoConfirmacao) {
        this.aguardandoConfirmacao = aguardandoConfirmacao;
    }

    public Integer getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Integer confirmado) {
        this.confirmado = confirmado;
    }
}
