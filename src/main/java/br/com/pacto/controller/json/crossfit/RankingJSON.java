package br.com.pacto.controller.json.crossfit;

import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.bean.wod.ScoreTreino;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import static java.util.Objects.isNull;

/**
 * Created by <PERSON><PERSON> Alcides on 16/02/2017.
 */
@ApiModel(description = "Dados do ranking de um aluno em um WOD específico")
public class RankingJSON {

    @ApiModelProperty(value = "Posição do aluno no ranking", example = "1")
    private Integer posicao;

    @ApiModelProperty(value = "Nome completo do aluno", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "Matrícula do aluno", example = "12345")
    private String matricula;

    @ApiModelProperty(value = "URL da foto do aluno", example = "https://exemplo.com/foto.jpg")
    private String foto;

    @ApiModelProperty(value = "Indica se o aluno realizou o WOD como prescrito (RX)", example = "true")
    private boolean rx;

    @ApiModelProperty(value = "Código identificador do WOD", example = "456")
    private Integer codigoWOD;

    @ApiModelProperty(value = "Tipo do WOD realizado", example = "AMRAP")
    private String tipoWOD;

    @ApiModelProperty(value = "Campos de resultado utilizados no WOD", example = "tempo,rounds")
    private String campos;

    @ApiModelProperty(value = "Tempo gasto para completar o WOD em segundos", example = "1200")
    private Integer tempo;

    @ApiModelProperty(value = "Peso utilizado no WOD em quilogramas", example = "80.5")
    private Double peso;

    @ApiModelProperty(value = "Número de repetições realizadas", example = "150")
    private Integer repeticoes;

    @ApiModelProperty(value = "Número de rounds completados", example = "8")
    private Integer rounds;

    @ApiModelProperty(value = "Código do nível de crossfit do aluno", example = "2")
    private Integer nivel;

    @ApiModelProperty(value = "Descrição do nível de crossfit", example = "Intermediário")
    private String nivelDescricao;

    @ApiModelProperty(value = "Sigla do nível de crossfit", example = "IN")
    private String nivelSigla;

    @ApiModelProperty(value = "Sexo do aluno", example = "M")
    private String sexo;

    @ApiModelProperty(value = "Código identificador do usuário", example = "789")
    private Integer codUsuario;

    @ApiModelProperty(value = "Indica se o aluno é visitante", example = "false")
    private Boolean visitante;

    public RankingJSON(ScoreTreino scoreTreino) {
        this.posicao = scoreTreino.getPosicao();
        if(scoreTreino.getUsuario() == null){
            scoreTreino.setUsuario(new Usuario());
        }
        this.nome = !isNull(scoreTreino.getUsuario().getSuperNome()) ? scoreTreino.getUsuario().getSuperNome() : "";
        if (scoreTreino.getUsuario().getCliente() != null) {
            this.matricula = scoreTreino.getUsuario().getCliente().getMatriculaString();
            this.visitante = scoreTreino.getUsuario().getCliente().getSituacao().equals("VI");
            this.sexo = scoreTreino.getUsuario().getCliente() == null ?
                    "" : scoreTreino.getUsuario().getCliente().getSexo();
        } else {
            this.matricula = "";
        }
        this.codUsuario = scoreTreino.getUsuario().getCodigo();
        this.foto = "";
        this.rx = scoreTreino.getRx();
        this.codigoWOD = scoreTreino.getWod().getCodigo();
        this.tipoWOD = scoreTreino.getWod().getTipoWodTabela().getNome();
        this.campos = scoreTreino.getWod().getTipoWodTabela().getCamposResultado();
        this.tempo = scoreTreino.getTempo();
        this.peso = scoreTreino.getPeso();
        this.repeticoes = scoreTreino.getRepeticoes();
        this.rounds = scoreTreino.getRounds();
        if (scoreTreino.getNivelcrossfit() != null) {
            this.nivel = scoreTreino.getNivelcrossfit().getCodigo();
            this.nivelDescricao = scoreTreino.getNivelcrossfit().getNome();
            this.nivelSigla = scoreTreino.getNivelcrossfit().getNome().substring(0,2).toUpperCase();
        }

    }

    @ApiModelProperty(value = "Descrição completa da performance do aluno no WOD", example = "Tempo: 20:00 - Rounds: 8 - Intermediário")
    public String getDescricao(){
        StringBuilder d = new StringBuilder();
        try {
            if(campos.contains("tempo")){
                d.append("Tempo: ").append(Uteis.converterSegundosEmMinutos(tempo)).append(" - ");
            }
            if(campos.contains("peso")){
                d.append("Peso: ").append(peso).append(" kg - ");
            }
            if(campos.contains("rounds")){
                d.append("Rounds: ").append(rounds).append(" - ");
            }
            if(campos.contains("repeticoes")){
                d.append("Repetições: ").append(repeticoes).append(" - ");
            }

        }catch (Exception e){
        }
        d.append(nivelDescricao);
        return d.toString();
    }

    @ApiModelProperty(value = "Descrição resumida da performance do aluno no WOD", example = "Tempo: 20:00 - Rounds: 8")
    public String getDescricaoSigla(){
        StringBuilder d = new StringBuilder();
        try {
            if(campos.contains("tempo")){
                d.append(" - Tempo: ").append(Uteis.converterSegundosEmMinutos(tempo));
            }
            if(campos.contains("peso")){
                d.append(" - Peso: ").append(peso).append(" kg");
            }
            if(campos.contains("rounds")){
                d.append(" - Rounds: ").append(rounds);
            }
            if(campos.contains("repeticoes")){
                d.append(" - Repetições: ").append(repeticoes);
            }

        }catch (Exception e){
        }
        return d.toString().replaceFirst(" - ", "");
    }

    public RankingJSON() {
    }

    public Integer getPosicao() {
        return posicao;
    }

    public void setPosicao(Integer posicao) {
        this.posicao = posicao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getFoto() {
        return foto;
    }

    public void setFoto(String foto) {
        this.foto = foto;
    }

    public boolean isRx() {
        return rx;
    }

    public void setRx(boolean rx) {
        this.rx = rx;
    }

    public Integer getCodigoWOD() {
        return codigoWOD;
    }

    public void setCodigoWOD(Integer codigoWOD) {
        this.codigoWOD = codigoWOD;
    }

    public Integer getTempo() {
        return tempo;
    }

    public void setTempo(Integer tempo) {
        this.tempo = tempo;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Integer getRepeticoes() {
        return repeticoes;
    }

    public void setRepeticoes(Integer repeticoes) {
        this.repeticoes = repeticoes;
    }

    public Integer getRounds() {
        return rounds;
    }

    public void setRounds(Integer rounds) {
        this.rounds = rounds;
    }

    public Integer getNivel() {
        if (nivel == null) {
            nivel = 0;
        }
        return nivel;
    }

    public void setNivel(Integer nivel) {
        this.nivel = nivel;
    }

    public String getNivelDescricao() {
        if (nivelDescricao == null) {
            nivelDescricao = "";
        }
        return nivelDescricao;
    }

    public void setNivelDescricao(String nivelDescricao) {
        this.nivelDescricao = nivelDescricao;
    }

    public String getTipoWOD() {
        return tipoWOD;
    }

    public void setTipoWOD(String tipoWOD) {
        this.tipoWOD = tipoWOD;
    }

    public String getCampos() {
        return campos;
    }

    public void setCampos(String campos) {
        this.campos = campos;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    @ApiModelProperty(value = "Primeiro nome do aluno", example = "João")
    public String getPrimeiroNome() {
        if (nome != null) {
            return Uteis.getPrimeiroNome(nome);
        } else {
            return "";
        }
    }

    public String getNivelSigla() {
        return nivelSigla;
    }

    public void setNivelSigla(String nivelSigla) {
        this.nivelSigla = nivelSigla;
    }

    public Integer getCodUsuario() {
        if (codUsuario == null) {
            codUsuario = 0;
        }
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public Boolean getvisitante() {
        return visitante;
    }

    public void setSituacaoAluno(Boolean visitante) {
        this.visitante = visitante;
    }
}
