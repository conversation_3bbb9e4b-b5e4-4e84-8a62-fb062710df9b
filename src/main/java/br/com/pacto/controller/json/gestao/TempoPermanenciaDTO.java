package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.ItemMediaBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO contendo dados de tempo de permanência, incluindo valores médio, mínimo e máximo.")
public class TempoPermanenciaDTO {

    @ApiModelProperty(value = "Tempo médio de permanência em dias.", example = "45")
    private Integer medio;

    @ApiModelProperty(value = "Dados do aluno com menor tempo de permanência.")
    private ItemTempoPermanenciaDTO minimo;

    @ApiModelProperty(value = "Dados do aluno com maior tempo de permanência.")
    private ItemTempoPermanenciaDTO maximo;

    public TempoPermanenciaDTO() {
        this.minimo = new ItemTempoPermanenciaDTO() ;
        this.maximo = new ItemTempoPermanenciaDTO() ;
    }

    public TempoPermanenciaDTO(DashboardBI dash, ItemMediaBI maior, ItemMediaBI menor) {
        this.medio = dash.getTempoMedioPermanenciaTreino();
        this.minimo = new ItemTempoPermanenciaDTO(menor.getDuracao(), menor.getMatricula(), menor.getNome()) ;
        this.maximo = new ItemTempoPermanenciaDTO(maior.getDuracao(), maior.getMatricula(), maior.getNome()) ;
    }

    public TempoPermanenciaDTO(DashboardBI dash) {
        this.medio = dash.getTempoMedioPermanenciaCarteira();
        this.minimo = new ItemTempoPermanenciaDTO(dash.getMenorPermanencia(), dash.getMatriculaClienteMenorPermanencia(), dash.getNomeClienteMenorPermanencia()) ;
        this.maximo = new ItemTempoPermanenciaDTO(dash.getMaiorPermanencia(), dash.getMatriculaClienteMaiorPermanencia(), dash.getNomeClienteMaiorPermanencia()) ;
    }

    public Integer getMedio() {
        return medio;
    }

    public void setMedio(Integer medio) {
        this.medio = medio;
    }

    public ItemTempoPermanenciaDTO getMinimo() {
        return minimo;
    }

    public void setMinimo(ItemTempoPermanenciaDTO minimo) {
        this.minimo = minimo;
    }

    public ItemTempoPermanenciaDTO getMaximo() {
        return maximo;
    }

    public void setMaximo(ItemTempoPermanenciaDTO maximo) {
        this.maximo = maximo;
    }
}
