package br.com.pacto.controller.json.locacao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de quantidade de um produto, incluindo identificador e rótulo descritivo.")
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QuantidadeDTO {

    @ApiModelProperty(value = "Identificador único da quantidade.", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Rótulo descritivo da quantidade.", example = "1 unidade")
    private String label;

    public QuantidadeDTO() {

    }

    public QuantidadeDTO(Integer id, String label) {
        this.id = id;
        this.label = label;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
