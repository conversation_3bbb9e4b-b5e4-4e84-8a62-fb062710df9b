package br.com.pacto.controller.json.serie;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.serie.SerieEndpointTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.serie.ExemploRespostaSerieResponseTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by paulo on 22/11/2018
 */
@Controller
@RequestMapping("/psec/series")
public class SerieController {

    private final SerieService serieService;

    @Autowired
    public SerieController(SerieService serieService) {
        Assert.notNull(serieService, "O serviço de serie não foi injetado corretamente");
        this.serieService = serieService;
    }

    @ApiOperation(value = "Cadastrar nova série", notes = "Cadastra uma nova série para uma atividade da ficha de treino", tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Série cadastrada com sucesso", response = ExemploRespostaSerieResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarSerie(
            @ApiParam(value = "Dados da série a ser cadastrada", required = true)
            @RequestBody SerieEndpointTO serieEndpointTO
    ) {
        try {
            return ResponseEntityFactory.ok(serieService.cadastrarSerie(serieEndpointTO));
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar uma nova serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Remover série", notes = "Remove uma série existente pelo seu ID", tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Série removida com sucesso", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerSerie(
            @ApiParam(value = "ID único da série a ser removida", required = true, example = "1")
            @PathVariable(value = "id") Integer id
    ) {
        try {
            serieService.removerSerie(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover uma serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Atualizar série", notes = "Atualiza os dados de uma série existente", tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Série atualizada com sucesso", response = ExemploRespostaSerieResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarSerie(
            @ApiParam(value = "ID único da série a ser atualizada", required = true, example = "1")
            @PathVariable(value = "id") Integer id,
            @ApiParam(value = "Dados atualizados da série", required = true)
            @RequestBody SerieEndpointTO serieEndpointTO
    ) {
        try {
            serieEndpointTO.setId(id);
            return ResponseEntityFactory.ok(serieService.editarSerie(serieEndpointTO));
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar uma serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Marcar série como realizada", notes = "Marca uma série como realizada pelo aluno", tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Série marcada como realizada com sucesso", response = ExemploRespostaSerieResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}/serieRealizada", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarSerieRealizada(
            @ApiParam(value = "ID único da série a ser marcada como realizada", required = true, example = "1")
            @PathVariable(value = "id") Integer id
    ) {
        try {
            SerieEndpointTO serie = new SerieEndpointTO();
            serie.setId(id);
            serie.setSerieRealizada(true);
            return ResponseEntityFactory.ok(serieService.editarSerie(serie));
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar uma serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Espelhar série", notes = "Copia os valores de uma série para todas as outras séries da mesma atividade", tags = "Atividades")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Série espelhada com sucesso", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.SERIE)
    @RequestMapping(value = "/{id}/espelhar", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> espelharSerie(
            @ApiParam(value = "ID único da série cujos valores serão espelhados para as demais séries da atividade", required = true, example = "1")
            @PathVariable(value = "id") Integer id
    ) {
        try {
            serieService.espelharSerie(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(SerieController.class.getName()).log(Level.SEVERE, "Erro ao tentar espelhar serie", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
