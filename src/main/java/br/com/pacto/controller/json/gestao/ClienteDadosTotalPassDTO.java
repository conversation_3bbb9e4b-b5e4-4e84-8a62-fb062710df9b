package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados do cliente para autorização de acesso TotalPass")
public class ClienteDadosTotalPassDTO {

    @ApiModelProperty(value = "Código da empresa do cliente", example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "CPF do cliente para validação", example = "12345678901")
    private String cpf;

    @ApiModelProperty(value = "Código da pessoa no sistema", example = "123")
    private Integer pessoa;

    @ApiModelProperty(value = "Matrícula do cliente", example = "12345")
    private String matricula;

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }
}
