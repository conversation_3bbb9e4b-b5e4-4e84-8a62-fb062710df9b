package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.bean.aula.Aula;
import br.com.pacto.bean.aula.AulaHorario;
import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.ambiente.AmbienteResponseTO;
import br.com.pacto.controller.json.modalidade.ModalidadeResponseTO;
import br.com.pacto.controller.json.turma.TurmaVideoDTO;
import br.com.pacto.util.enumeradores.DiasSemana;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da aula")
public class AulaResponseDTO {

    @ApiModelProperty(value = "Código identificador da aula", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da aula", example = "Aula Spinning")
    private String nome;
    @ApiModelProperty(value = "Descrição da aula", example = "Aula Spinning com Turma 01")
    private String descricao;
    @ApiModelProperty(value = "Cor da aula", example = "Azul")
    private String cor;
    @ApiModelProperty(value = "Meta", example = "80%")
    private Integer meta;
    @ApiModelProperty(value = "Pontuação bônus por realização da aula", example = "100")
    private Integer pontuacaoBonus;
    @ApiModelProperty(value = "Bonificação por realização da aula", example = "100")
    private Integer bonificacao;
    @ApiModelProperty(value = "Mensagem da aula", example = "Aula de Spinning.")
    private String mensagem;
    @ApiModelProperty(value = "URL do vídeo da aula no Youtube", example = "www.youtube.com/video/aula")
    private String urlVideoYoutube;
    @ApiModelProperty(value = "Ocupação da aula", example = "ALTAFREQUENCIA")
    private String ocupacao;
    @ApiModelProperty(value = "Ambiente que será utilizado para realização da aula")
    private AmbienteResponseTO ambiente;
    @ApiModelProperty(value = "Modalidade da aula")
    private ModalidadeResponseTO modalidade;
    @ApiModelProperty(value = "Professor responsável pela aula")
    private ProfessorResponseTO professor;
    @ApiModelProperty(value = "Data de início da aula (Em timestamp)", example = "1744567200")
    private Long dataInicio;
    @ApiModelProperty(value = "Data final da aula (Em timestamp)", example = "1744570800")
    private Long dataFinal;
    @ApiModelProperty(value = "Tempo de tolerância para a aula em minutos", example = "10")
    private Integer toleranciaMin;
    @ApiModelProperty(value = "Tipo de tolerância da aula.\n\n <strong>Valores disponíveis</strong>" +
            "- 1 (Após início)" +
            "- 2 (Antes do início)",
            example = "1",
            allowableValues = "1,2")
    private Integer tipoTolerancia;
    @ApiModelProperty(value = "Capacidade de alunos para a aula", example = "30")
    private Integer capacidade;
    @ApiModelProperty(value = "Limite de vagas agregados", example = "30")
    private Integer limiteVagasAgregados;
    @ApiModelProperty(value = "Lista de horários da aula")
    private HorarioDTO[] horarios;
    @ApiModelProperty(value = "Dias de aula", example = "1")
    private String dias;
    @ApiModelProperty(value = "Lista de dias da semana que a aula acontece", example = "[\"Segunda\", \"Quarta\"]")
    private String[] diasSemana;
    @ApiModelProperty(value = "Indica se deve validar as restrições de marcação da aula", example = "false")
    private Boolean validarRestricoesMarcacao;
    @ApiModelProperty(value = "Indica se NÃO deve validar a modalidade do contrato para participação da aula", example = "false")
    private Boolean naoValidarModalidadeContrato;
    @ApiModelProperty(value = "Código do produto GymPass vinculado a aula", example = "1")
    private Integer produtoGymPass;
    @ApiModelProperty(value = "ID da classe no GymPass", example = "1")
    private Integer idClasseGymPass;
    @ApiModelProperty(value = "URL da turma virtual para a aula", example = "www.pactosolucoes.com.br/turma-virtual/aula")
    private String urlTurmaVirtual;
    @ApiModelProperty(value = "URL da imagem da aula", example = "www.pactosolucoes.com.br/imagens/aula-spinning.png")
    private String imageUrl;
    @ApiModelProperty(value = "Indica se permite fixar a aula", example = "true")
    private Boolean permiteFixar;
    @ApiModelProperty(value = "Indica se a aula tem integração com o Selfloops", example = "false")
    private Boolean aulaIntegracaoSelfloops;
    @ApiModelProperty(value = "Indica se é possível visualizar o produto GymPass", example = "false")
    private Boolean visualizarProdutosGympass;
    @ApiModelProperty(value = "Indica se é possível visualizar o produto Totalpass", example = "false")
    private Boolean visualizarProdutosTotalpass;
    @ApiModelProperty(value = "Níveis vinculado a aula")
    private List<NivelTO> niveis;

    @ApiModelProperty(value = "Idade máxima em anos para participação da aula", example = "70")
    private Integer idadeMaximaAnos;
    @ApiModelProperty(value = "Idade máxima em meses para participação da aula", example = "840")
    private Integer idadeMaximaMeses;
    @ApiModelProperty(value = "Idade mínima necessária para participação da aula", example = "18")
    private Integer idadeMinimaAnos;
    @ApiModelProperty(value = "Idade mínima em meses necessária para participação da aula", example = "216")
    private Integer idadeMinimaMeses;
    @ApiModelProperty(value = "Lista de links dos vídeos da turma")
    private List<TurmaVideoDTO> linkVideos;
    @ApiModelProperty(value = "Tipo da reserva de equipamento para a aula", example = "Reservado por Aluno")
    private String tipoReservaEquipamento;
    @ApiModelProperty(value = "Mapa dos equipamentos que serão utilizados na aula", example = "Mapa de bicicletas Spinning")
    private String mapaEquipamentos;
    @ApiModelProperty(value = "Lista de mapas de equipamentos que serão utilizados na aula")
    private List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho;

    public AulaResponseDTO() {
    }

    public AulaResponseDTO(Aula a, Boolean treinoIndependente) {
        this(a, a.getProfessor(), treinoIndependente);
    }

    public AulaResponseDTO(Aula a, ProfessorSintetico prof, Boolean treinoIndependente) {
//        private Integer id;
        this.id = a.getCodigo();
        //        private String nome;
        this.nome = a.getNome();
//        private AmbienteResponseTO ambiente;
        this.ambiente = new AmbienteResponseTO(a.getAmbiente(), treinoIndependente, false);
        //        private ModalidadeResponseTO modalidade;
        this.modalidade = new ModalidadeResponseTO(a.getModalidade(), treinoIndependente);
        //        private ProfessorResponseTO professor;
        Usuario u = new Usuario();
        u.setProfessor(prof);
        this.professor = new ProfessorResponseTO(u, treinoIndependente);
        //        private Long dataInicio;
        this.dataInicio = a.getDataInicio().getTime();
        //        private Long dataFinal;
        this.dataFinal = a.getDataFim().getTime();
//        private Integer meta;
        this.meta = a.getMeta();
//        private Integer pontuacaoBonus;
        this.pontuacaoBonus = a.getPontosBonus();
//        private Integer bonificacao;
        this.bonificacao = a.getBonificacao();
//        private String mensagem;
        this.mensagem = a.getMensagem();
//        private String urlVideoYoutube;
        this.urlVideoYoutube = a.getUrlVideoYoutube();
//        private String ocupacao;
        this.ocupacao = a.getFrequencia().name();
//        private Integer toleranciaMin;
        this.toleranciaMin = a.getMinutosTolerancia();
//        private Integer capacidade;
        this.capacidade = a.getCapacidade();
//        private HorarioDTO horarios;
        List<HorarioDTO> hrs = new ArrayList<HorarioDTO>();
        for (AulaHorario hr : a.getHorarios()) {
            HorarioDTO h = new HorarioDTO();
            if (hr.getAtivo()) {
                h.setInicio(hr.getInicio());
                h.setFim(hr.getFim());
                h.setId(hr.getCodigo());
                hrs.add(h);
            }
        }
        this.horarios = hrs.toArray(new HorarioDTO[hrs.size()]);
        this.dias = a.getDiasSemana();
//        private String[] diasSemana;
        List<String> ds = new ArrayList<String>();
        try {
            for (String d : a.getDiasSemana().split("\\,")) {
                DiasSemana denum = DiasSemana.getDiaSemana(d);
                ds.add(denum.getChave());
            }
            diasSemana = ds.toArray(new String[ds.size()]);
        } catch (Exception e) {
            e.printStackTrace();
        }

        this.produtoGymPass = a.getProdutoGymPass();
        this.idClasseGymPass = a.getIdClasseGymPass();
        this.urlTurmaVirtual = a.getUrlTurmaVirtual();

    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public AmbienteResponseTO getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(AmbienteResponseTO ambiente) {
        this.ambiente = ambiente;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Long dataFinal) {
        this.dataFinal = dataFinal;
    }

    public ModalidadeResponseTO getModalidade() {
        return modalidade;
    }

    public void setModalidade(ModalidadeResponseTO modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getMeta() {
        return meta;
    }

    public void setMeta(Integer meta) {
        this.meta = meta;
    }

    public Integer getPontuacaoBonus() {
        return pontuacaoBonus;
    }

    public void setPontuacaoBonus(Integer pontuacaoBonus) {
        this.pontuacaoBonus = pontuacaoBonus;
    }

    public Integer getBonificacao() {
        return bonificacao;
    }

    public void setBonificacao(Integer bonificacao) {
        this.bonificacao = bonificacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getUrlVideoYoutube() {
        return urlVideoYoutube;
    }

    public void setUrlVideoYoutube(String urlVideoYoutube) {
        this.urlVideoYoutube = urlVideoYoutube;
    }

    public String getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(String ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getToleranciaMin() {
        return toleranciaMin;
    }

    public void setToleranciaMin(Integer toleranciaMin) {
        this.toleranciaMin = toleranciaMin;
    }

    public Integer getCapacidade() {
        return capacidade;
    }

    public void setCapacidade(Integer capacidade) {
        this.capacidade = capacidade;
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }

    public HorarioDTO[] getHorarios() {
        return horarios;
    }

    public void setHorarios(HorarioDTO[] horarios) {
        this.horarios = horarios;
    }

    public String getDias() {
        return dias;
    }

    public void setDias(String dias) {
        this.dias = dias;
    }

    public String[] getDiasSemana() {
        return diasSemana;
    }

    public void setDiasSemana(String[] diasSemana) {
        this.diasSemana = diasSemana;
    }

    public Boolean getValidarRestricoesMarcacao() {
        return validarRestricoesMarcacao;
    }

    public void setValidarRestricoesMarcacao(Boolean validarRestricoesMarcacao) {
        this.validarRestricoesMarcacao = validarRestricoesMarcacao;
    }

    public Integer getProdutoGymPass() {
        return produtoGymPass;
    }

    public void setProdutoGymPass(Integer produtoGymPass) {
        this.produtoGymPass = produtoGymPass;
    }

    public Integer getIdClasseGymPass() {
        return idClasseGymPass;
    }

    public void setIdClasseGymPass(Integer idClasseGymPass) {
        this.idClasseGymPass = idClasseGymPass;
    }

    public String getUrlTurmaVirtual() {
        return urlTurmaVirtual;
    }

    public void setUrlTurmaVirtual(String urlTurmaVirtual) {
        this.urlTurmaVirtual = urlTurmaVirtual;
    }

    public Boolean getNaoValidarModalidadeContrato() {
        return naoValidarModalidadeContrato;
    }

    public void setNaoValidarModalidadeContrato(Boolean naoValidarModalidadeContrato) {
        this.naoValidarModalidadeContrato = naoValidarModalidadeContrato;
    }

    public Integer getTipoTolerancia() {
        return tipoTolerancia;
    }

    public void setTipoTolerancia(Integer tipoTolerancia) {
        this.tipoTolerancia = tipoTolerancia;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Boolean getVisualizarProdutosGympass() {
        return visualizarProdutosGympass;
    }

    public void setVisualizarProdutosGympass(Boolean visualizarProdutosGympass) {
        this.visualizarProdutosGympass = visualizarProdutosGympass;
    }

    public Boolean getVisualizarProdutosTotalpass() {
        return visualizarProdutosTotalpass;
    }

    public void setVisualizarProdutosTotalpass(Boolean visualizarProdutosTotalpass) {
        this.visualizarProdutosTotalpass = visualizarProdutosTotalpass;
    }

    public Boolean getPermiteFixar() {
        return permiteFixar;
    }

    public void setPermiteFixar(Boolean permiteFixar) {
        this.permiteFixar = permiteFixar;
    }

    public List<NivelTO> getNiveis() {
        return niveis;
    }

    public void setNiveis(List<NivelTO> niveis) {
        this.niveis = niveis;
    }

    public Integer getIdadeMaximaAnos() {
        return idadeMaximaAnos;
    }

    public void setIdadeMaximaAnos(Integer idadeMaximaAnos) {
        this.idadeMaximaAnos = idadeMaximaAnos;
    }

    public Integer getIdadeMaximaMeses() {
        return idadeMaximaMeses;
    }

    public void setIdadeMaximaMeses(Integer idadeMaximaMeses) {
        this.idadeMaximaMeses = idadeMaximaMeses;
    }

    public Integer getIdadeMinimaAnos() {
        return idadeMinimaAnos;
    }

    public void setIdadeMinimaAnos(Integer idadeMinimaAnos) {
        this.idadeMinimaAnos = idadeMinimaAnos;
    }

    public Integer getIdadeMinimaMeses() {
        return idadeMinimaMeses;
    }

    public void setIdadeMinimaMeses(Integer idadeMinimaMeses) {
        this.idadeMinimaMeses = idadeMinimaMeses;
    }

    public List<TurmaVideoDTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<TurmaVideoDTO> linkVideos) {
        this.linkVideos = linkVideos;
    }

    public String getTipoReservaEquipamento() {
        return tipoReservaEquipamento;
    }

    public void setTipoReservaEquipamento(String tipoReservaEquipamento) {
        this.tipoReservaEquipamento = tipoReservaEquipamento;
    }

    public String getMapaEquipamentos() {
        return mapaEquipamentos;
    }

    public void setMapaEquipamentos(String mapaEquipamentos) {
        this.mapaEquipamentos = mapaEquipamentos;
    }

    public List<MapaEquipamentoAparelhoDTO> getListaMapaEquipamentoAparelho() {
        return listaMapaEquipamentoAparelho;
    }

    public void setListaMapaEquipamentoAparelho(List<MapaEquipamentoAparelhoDTO> listaMapaEquipamentoAparelho) {
        this.listaMapaEquipamentoAparelho = listaMapaEquipamentoAparelho;
    }

    public Boolean getAulaIntegracaoSelfloops() {
        return aulaIntegracaoSelfloops;
    }

    public void setAulaIntegracaoSelfloops(Boolean aulaIntegracaoSelfloops) {
        this.aulaIntegracaoSelfloops = aulaIntegracaoSelfloops;
    }
}
