package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.anamnese.AnamneseController;
import br.com.pacto.objeto.to.AtividadeFichaEndpointTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.swagger.respostas.atividade.ExemploRespostaAtividadeFichaResponseTO;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaBoolean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 13/08/2018.
 */
@Controller
@RequestMapping("/psec/atividades-ficha")
public class AtividadeFichaController {

    private final ProgramaTreinoService programaTreinoService;

    @Autowired
    public AtividadeFichaController(ProgramaTreinoService programaTreinoService){
        Assert.notNull(programaTreinoService, "O serviço de programa treino não foi injetado corretamente");
        this.programaTreinoService = programaTreinoService;
    }

    @ApiOperation(
            value = "Criar atividade da ficha",
            notes = "Cria uma nova atividade dentro de uma ficha de treino. " +
                    "Recebe os dados da atividade incluindo exercício, séries, repetições, carga e outros parâmetros de treino.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAtividadeFicha(
            @ApiParam(value = "Dados da atividade da ficha a ser criada", required = true)
            @RequestBody AtividadeFichaEndpointTO atividadeFichaTO) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.criarAtividadeFicha(atividadeFichaTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Atualizar atividade da ficha",
            notes = "Atualiza uma atividade existente dentro de uma ficha de treino. " +
                    "Permite modificar parâmetros como séries, repetições, carga, método de execução e outros dados da atividade.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividadeFicha(
            @ApiParam(value = "ID único da atividade da ficha a ser atualizada", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados atualizados da atividade da ficha", required = true)
            @RequestBody AtividadeFichaEndpointTO atividadeFichaTO) {
        try {
            atividadeFichaTO.setId(id);
            return ResponseEntityFactory.ok(programaTreinoService.atualizarAtividadeFicha(atividadeFichaTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Remover atividade da ficha",
            notes = "Remove uma atividade específica de uma ficha de treino. " +
                    "A exclusão é permanente e remove todas as séries associadas à atividade.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividadeFicha(
            @ApiParam(value = "ID único da atividade da ficha a ser removida", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id){
        try {
            programaTreinoService.removerAtividadeFicha(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Adicionar atividade à ficha",
            notes = "Adiciona uma atividade existente a uma ficha de treino específica. " +
                    "Cria uma nova atividade da ficha com configurações padrão baseadas na atividade selecionada.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/add-atividade/{ficha}/{atividade}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> addAtividadeFicha(
            @ApiParam(value = "ID da ficha onde a atividade será adicionada", defaultValue = "1", required = true)
            @PathVariable Integer ficha,
            @ApiParam(value = "ID da atividade a ser adicionada à ficha", defaultValue = "1", required = true)
            @PathVariable Integer atividade) {
        try {
            return ResponseEntityFactory.ok(programaTreinoService.addAtividadeFicha(ficha, atividade));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeFichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar atividade ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
