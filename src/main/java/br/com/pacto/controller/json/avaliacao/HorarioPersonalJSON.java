package br.com.pacto.controller.json.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Horário específico para agendamento personalizado")
public class HorarioPersonalJSON {

    @ApiModelProperty(value = "Timestamp de início do horário em milissegundos (formato Unix timestamp)",
                      example = "1705320000000")
    public long inicio;

    @ApiModelProperty(value = "Timestamp de fim do horário em milissegundos (formato Unix timestamp)",
                      example = "1705323600000")
    public long fim;

    public long getInicio() {
        return inicio;
    }

    public void setInicio(long inicio) {
        this.inicio = inicio;
    }

    public long getFim() {
        return fim;
    }

    public void setFim(long fim) {
        this.fim = fim;
    }
}
