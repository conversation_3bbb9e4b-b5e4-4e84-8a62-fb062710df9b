package br.com.pacto.controller.json.gogood;

import br.com.pacto.bean.gogood.ConfigGoGood;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações de integração GoGood")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfigGoGoodDTO {

    @ApiModelProperty(value = "Código único da configuração GoGood", example = "1")
    private Integer codigo;

    @ApiModelProperty(value = "Código da empresa no sistema Treino", example = "456")
    private Integer empresa;

    @ApiModelProperty(value = "Nome da empresa", example = "CrossFit Elite Box")
    private String nome;

    @ApiModelProperty(value = "Token de integração da academia no GoGood", example = "gogood_academy_token_xyz456")
    private String tokenAcademyGoGood;

    public ConfigGoGoodDTO() {
    }

    public ConfigGoGoodDTO(ConfigGoGood config) {
        this.codigo = config.getCodigo();
        this.empresa = config.getEmpresa().getCodigo();
        this.nome = config.getEmpresa().getNome();
        this.tokenAcademyGoGood = config.getTokenAcademyGoGood();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTokenAcademyGoGood() {
        return tokenAcademyGoGood;
    }

    public void setTokenAcademyGoGood(String tokenAcademyGoGood) {
        this.tokenAcademyGoGood = tokenAcademyGoGood;
    }
}
