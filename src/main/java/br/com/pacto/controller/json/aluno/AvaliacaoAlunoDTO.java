package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da avaliação física")
public class AvaliacaoAlunoDTO {
    @ApiModelProperty(value = "Data da avaliação física", example = "2023-10-12T14:30:00Z")
    private Date data;

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }
}
