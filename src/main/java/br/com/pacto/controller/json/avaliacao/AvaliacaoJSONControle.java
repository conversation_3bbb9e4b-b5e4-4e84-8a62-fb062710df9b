/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.bean.avaliacao.*;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.AgendamentoJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.dao.intf.avaliacao.ParQDao;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.impl.notificacao.excecao.AlunoExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.AvaliacaoFisicaExcecoes;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.avaliacao.AvaliacaoFisicaService;
import br.com.pacto.service.intf.avaliacao.EvolucaoFisicaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaObterPerguntasParQ;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaImprimirParQAssinaturaDigital;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaSalvarRespostasParQ;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaListarRespostasParQPorCliente;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaListarRespostasParQPorClienteV2;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaAvaliadoresFisicos;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaHorariosSugeridosAvaliacao;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaInserirAgendamentoAvaliacao;
import br.com.pacto.swagger.respostas.avaliacao.ExemploRespostaHistoricoAvaliacaoFisica;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import servicos.integracao.midias.MidiaService;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/avaliacao")
public class AvaliacaoJSONControle extends SuperControle{

    @Autowired
    private AvaliacaoFisicaService avaliacaoService;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private EvolucaoFisicaService evolucaoService;
    @Autowired
    private AnamneseService anamneseService;
    @Autowired
    private ClienteSinteticoService clienteService;
    private ServletContext sc;
    @Autowired
    private AulaService aulaService;
    @Autowired
    private ParQDao parQDao;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private EmpresaService empresaService;

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.sc = servletContext;
    }

    @RequestMapping(value = "{ctx}/avaliacoes", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap avaliacoes(@PathVariable String ctx, @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            List<AvaliacaoFisica> avaliacoes = avaliacaoService.obterAvaliacaoCliente(ctx, cliente.getCodigo());
            mm.addAttribute(RETURN, avaliacoes);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/todasAvaliacoesPorPeriodo", method = RequestMethod.GET)
    @ResponseBody
    ModelMap todasAvaliacoesPorPeriodo(@PathVariable String ctx, @RequestParam final String dataInicio, @RequestParam final String dataFim) {
        ModelMap mm = new ModelMap();
        try {
            final List<AvaliacaoFisicaDTO> avaliacoes = avaliacaoService.todasAvaliacoesPorPeriodo(ctx, dataInicio, dataFim);
            mm.addAttribute("avaliacoes", avaliacoes);
        } catch(Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }

        return mm;
    }

    @RequestMapping(value = "{ctx}/avaliacaoDentroPeriodo", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap avaliacoesDentroPeriodo(@PathVariable String ctx, @RequestParam final String matricula, @RequestParam final String dataInicio, @RequestParam final String dataFim) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            List<AvaliacaoFisica> avaliacoes = avaliacaoService.obterAvaliacaoCliente(ctx, cliente.getCodigo());
            for(AvaliacaoFisica ava: avaliacoes){
                if(Calendario.entre(ava.getDataAvaliacao(), Uteis.getDate(dataInicio), Uteis.getDate(dataFim))){
                    return mm.addAttribute(RETURN, true);
                }
            }
            mm.addAttribute(RETURN, false);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/avaliacaoAtual", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap avaliacaoAtual(@PathVariable String ctx, @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
            AvaliacaoFisicaJSON avaliacaoFisicaJSON = avaliacaoService.obterAvaliacaoAtual(ctx, matricula);
            mm.addAttribute(RETURN, avaliacaoFisicaJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Histórico de avaliações físicas do aluno",
                  notes = "Retorna o histórico completo de avaliações físicas de um aluno específico, incluindo dados antropométricos, " +
                         "composição corporal, dobras cutâneas, perimetria, fotos e avaliação postural. Também fornece uma URL " +
                         "criptografada para acesso à evolução automática dos dados.",
                  tags = "Avaliação Física")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Histórico de avaliações físicas obtido com sucesso",
                     response = ExemploRespostaHistoricoAvaliacaoFisica.class)
    })
    @RequestMapping(value = "{ctx}/historico", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap historico(@PathVariable String ctx,
                      @ApiParam(value = "Matrícula do aluno para consulta do histórico de avaliações físicas",
                               defaultValue = "2024001")
                      @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.consultarPorMatricula(ctx, matricula);
            mm.addAttribute("url", "/evolucaoauto/avtw"
                    + Uteis.encriptar(ctx
                    + "-" + clienteSintetico.getEmpresa()
                    + "-" + clienteSintetico.getCodigo()
                    + "-true", "c0deVFisic4"));
            List<AvaliacaoFisicaJSON> avaliacoes = avaliacaoService.historico(ctx, matricula);
            mm.addAttribute(RETURN, avaliacoes);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/evolucao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap evolucao(@PathVariable String ctx, @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            List<AvaliacaoFisica> avaliacoes = avaliacaoService.historico(ctx, cliente.getCodigo());
            EvolucaoFisica evolucaoFisica = evolucaoService.dadosEvolucaoFisica(ctx, avaliacoes, cliente);
            mm.addAttribute(RETURN, evolucaoFisica);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/proximaAvaliacaoAgendada", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap proximaAvaliacaoAgendada(@PathVariable String ctx, @RequestParam final String matricula, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.consultarPorMatricula(ctx, matricula);
            List<Agendamento> agendamentos = agendamentoService.agendamentosFuturosCliente(ctx, clienteSintetico, TipoAgendamentoEnum.AVALIACAO_FISICA);
            if(UteisValidacao.emptyList(agendamentos)){
                throw new Exception("proxima.avaliacao.nao.agendada");
            }
            Agendamento agendamento = agendamentos.get(0);
            AgendamentoJSON ag = new AgendamentoJSON(agendamento.getCodigo(),
                    Calendario.getData(agendamento.getInicio(), Calendario.MASC_DATAHORA),
                    Calendario.getHora(agendamento.getInicio(), Calendario.MASC_HORA),
                    Calendario.getHora(agendamento.getFim(), Calendario.MASC_HORA),
                    agendamento.getTipoEvento().getNome(),
                    agendamento.getStatus().getDescricao(), agendamento.getNomeProfessor(),
                    agendamento.getStatus().getCor(), agendamento.getInicio().getTime());
            if(agendamento.getProfessor() != null && agendamento.getProfessor().getUriImagem() != null){
                ag.setFotoProfessor(agendamento.getProfessor().getUriImagem());
            }
            ag.setCodProfessor(agendamento.getProfessor().getCodigo());
            ag.setCor(agendamento.getTipoEvento().getCor().getCor());
            ag.setStatusCod(agendamento.getStatusCod());
            mm.addAttribute(RETURN, ag);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Consultar avaliadores físicos disponíveis",
                  notes = "Retorna a lista de avaliadores físicos (professores) disponíveis para agendamento de avaliações físicas em uma data específica. " +
                         "O sistema consulta a agenda dos professores e retorna apenas aqueles que possuem disponibilidade para realizar avaliações físicas. " +
                         "Cada avaliador retornado inclui informações básicas como código, nome, status ativo, empresa, avatar e a data do primeiro dia disponível.",
                  tags = "Avaliação Física")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Lista de avaliadores físicos disponíveis retornada com sucesso",
                     response = ExemploRespostaAvaliadoresFisicos.class)
    })
    @RequestMapping(value = "{ctx}/avaliadoresFisicos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap avaliadoresFisicos(@ApiParam(value = "Contexto da aplicação (chave da empresa)", example = "treino")
                               @PathVariable String ctx,
                               @ApiParam(value = "Código da empresa para filtrar avaliadores específicos. Se não informado, considera todas as empresas do contexto",
                                        required = false, example = "1")
                               @RequestParam(required = false) Integer empresa,
                               @ApiParam(value = "Data para consulta da disponibilidade dos avaliadores físicos",
                                        required = true, defaultValue = "24/07/2025")
                               @RequestParam final String data,
                               HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            List<AvaliadorFisicoJSON> professorJSONS = avaliacaoService.avaliadoresFisicosDisponiveis(ctx, empresa, Uteis.getDate(data), request);
            mm.addAttribute(RETURN, professorJSONS);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter horários sugeridos para avaliação física",
                  notes = "Retorna uma lista de horários disponíveis para agendamento de avaliação física com um professor específico em uma determinada data. " +
                         "Os horários são calculados com base na disponibilidade do professor e nos tipos de eventos de avaliação física ativos.",
                  tags = "Avaliação Física")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Horários sugeridos obtidos com sucesso",
                     response = ExemploRespostaHorariosSugeridosAvaliacao.class)
    })
    @RequestMapping(value = "{ctx}/horariosSugeridos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap horariosSugeridos(@ApiParam(value = "Contexto da aplicação (chave da empresa)", defaultValue = "treino")
                               @PathVariable String ctx,
                               @ApiParam(value = "Data para consulta dos horários disponíveis no formato dd/MM/yyyy",
                                        required = true, defaultValue = "24/07/2025")
                               @RequestParam final String data,
                               @ApiParam(value = "Código do professor/avaliador físico para consultar disponibilidade",
                                        required = true, defaultValue = "1001")
                               @RequestParam final Integer professor,
                               @ApiParam(value = "Código da empresa para filtrar horários específicos",
                                        required = true, defaultValue = "1")
                               @RequestParam final Integer empresa) {
        ModelMap mm = new ModelMap();
        try {
            List<SugestaoHorarioJSON> horarioJSONS = avaliacaoService.sugerirHorarios(ctx, Calendario.getDataComHoraZerada(Uteis.getDate(data)), professor, empresa);
            mm.addAttribute(RETURN, horarioJSONS);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/gerarPDF", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarPdf(@PathVariable String ctx, @RequestParam final Integer avaliacao,
            @RequestParam(required = false) final Boolean enviarEmail, HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            String refPdf = avaliacaoService.comporUrlPdf(ctx, avaliacao, request, false, "PT");
            mm.addAttribute("urlpdf", refPdf);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Inserir agendamento de avaliação física",
                  notes = "Cria um novo agendamento de avaliação física para um aluno específico. " +
                         "O sistema valida a disponibilidade do horário e professor antes de confirmar o agendamento. " +
                         "Retorna os dados de confirmação incluindo código da venda gerada.",
                  tags = "Avaliação")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Agendamento criado com sucesso",
                        response = ExemploRespostaInserirAgendamentoAvaliacao.class)
    })
    @RequestMapping(value = "{ctx}/inserirAgendamento", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap horariosSugeridos(@PathVariable String ctx,
                               @ApiParam(value = "Data para o agendamento da avaliação física no formato dd/MM/yyyy",
                                        required = true, defaultValue = "25/07/2025")
                               @RequestParam final String data,
                               @ApiParam(value = "Horário para o agendamento da avaliação física no formato HH:mm",
                                        required = true, defaultValue = "09:00")
                               @RequestParam final String horario,
                               @ApiParam(value = "Matrícula do aluno que será avaliado",
                                        required = true, defaultValue = "2025001")
                               @RequestParam final String matricula,
                               @ApiParam(value = "Código do professor/avaliador físico responsável",
                                        required = true, defaultValue = "1001")
                               @RequestParam final Integer professor,
                               @ApiParam(value = "Código da empresa onde será realizada a avaliação",
                                        required = true, defaultValue = "1")
                               @RequestParam final Integer empresa,
                               @ApiParam(value = "Código do tipo de evento para classificação do agendamento",
                                        required = true, defaultValue = "5")
                               @RequestParam final Integer tipoEvento) {
        ModelMap mm = new ModelMap();
        try {
            ConfirmacaoAgendamentoJSON c = agendamentoService.lancarAgendamentoPeloAluno(ctx,
                    Uteis.getDate(data),
                    horario,
                    professor,
                    matricula,
                    empresa,
                    tipoEvento);
            mm.addAttribute(RETURN, c);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/linkEvolucao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap linkEvolucao(@PathVariable String ctx,
                               @RequestParam final Integer empresa,
                               @RequestParam final String matricula) {

        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.consultarPorMatricula(ctx, matricula);
            mm.addAttribute("url", "/evolucaoaluno/avtw"
                    + Uteis.encriptar(ctx
                    + "-" + empresa
                    + "-" + clienteSintetico.getCodigo()
                    + "-true", "c0deVFisic4"));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/parq", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap parq(@PathVariable String ctx,
                               @RequestParam final Integer empresa) {

        ModelMap mm = new ModelMap();
        try {
            QuestionarioJSON questionarioJSON = anamneseService.consultarParQ(ctx, empresa, getViewUtils());
            mm.addAttribute("parq", questionarioJSON);
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            boolean apresentarLeiParq = css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_VISUALIZAR_LEI_PARQ).getValorAsBoolean();
            mm.addAttribute("apresentarLeiParq", apresentarLeiParq);
            if (apresentarLeiParq) {
                mm.addAttribute("siglaEstadoLeiParq", empresaService.obterSiglaEstado(ctx, empresa));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(AvaliacaoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/responderParq", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap responderBV(@PathVariable final String ctx,
                         @RequestParam final String matricula,
                         @RequestParam(required = false) final String json,
                         @RequestBody(required = false) final String bodyJson,
                         @RequestParam final String assinatura){
        ModelMap mm = new ModelMap();
        try {
            anamneseService.responderParQServico(ctx, matricula, new JSONObject(isNotBlank(json) ? json : bodyJson), assinatura);
            mm.addAttribute(RETURN, "sucesso");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/integrada", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap responderBV(@PathVariable final String ctx,
                         @RequestParam final String matricula){
        ModelMap mm = new ModelMap();
        try {
            List<AvaliacaoIntegradaJSON> avaliacoes = avaliacaoService.obterJSONAvaliacoesIntegradas(ctx, matricula);
            mm.addAttribute("avaliacoes", avaliacoes);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/bioimpedancia", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap bioimpedancia(@PathVariable final String ctx,
                         @RequestParam final Integer codigo,
                         @RequestParam(required = false) final String cpf,
                         @RequestBody final String dados){
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico;
            if(UteisValidacao.emptyString(cpf)){
                 clienteSintetico = clienteService.obterPorCodigo(ctx, codigo);
            } else {
                clienteSintetico = clienteService.consultarPorCpf(ctx, cpf);
            }

            if(clienteSintetico == null){
                throw new Exception("cliente nao encontrado");
            }

            avaliacaoService.gravarBioimpedancia(ctx, clienteSintetico, dados);
            mm.addAttribute(STATUS_SUCESSO, "Avaliação gravada com sucesso.");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/nova", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap nova(@PathVariable final String ctx,
                         @RequestParam final Integer cliente){
        ModelMap mm = new ModelMap();
        try {
            boolean temNovaAvaliacao = avaliacaoService.temNovaAvaliacao(ctx, cliente);
            mm.addAttribute("nova", temNovaAvaliacao);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/altura", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap altura(@PathVariable final String ctx,
                         @RequestParam final String matricula){
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico clienteSintetico = clienteService.consultarPorMatricula(ctx, matricula);

            if (clienteSintetico == null) {
                clienteSintetico = aulaService.addAlunoAutomaticamente(ctx, matricula);
            }

            AvaliacaoFisica avaliacaoFisica = avaliacaoService.obterAvaliacaoVigente(ctx, clienteSintetico.getCodigo());
            mm.addAttribute("altura", avaliacaoFisica.getAltura());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/wdMtRWRG4bqwquc5xgjk4aKhWur2bCtj6iKB0r4", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap alturaCrypt(@PathVariable final String ctx, @RequestBody String content){
        ModelMap mm = new ModelMap();
        try {
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            String matricula = o.optString("matricula");

            ClienteSintetico clienteSintetico = clienteService.consultarPorMatricula(ctx, matricula);

            if (clienteSintetico == null) {
                clienteSintetico = aulaService.addAlunoAutomaticamente(ctx, matricula);
            }

            AvaliacaoFisica avaliacaoFisica = avaliacaoService.obterAvaliacaoVigente(ctx, clienteSintetico.getCodigo());
            mm.addAttribute("altura", avaliacaoFisica.getAltura());

            // Criptografar toda a resposta de sucesso
            JSONObject responseJson = new JSONObject();
            for (String key : mm.keySet()) {
                responseJson.put(key, mm.get(key));
            }
            mm.clear();
            mm.addAttribute(RETURN, Uteis.encryptUserData(responseJson.toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/restaura-resposta", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap restaurarRespostas(@PathVariable final String ctx,
                         @RequestBody final String codigosPerguntas){
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute("altura", avaliacaoService.restaurarRespostas(ctx, codigosPerguntas));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/imprimirParq", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap imprimirParq(@PathVariable final String ctx,
                          @RequestParam final Integer avaliacao,
                          @RequestParam final String assinaturaDigital,
                          HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            AvaliacaoFisica avaliacaoFisica = avaliacaoService.obterPorId(ctx, avaliacao);
            Usuario usu = usuarioService.obterPorId(ctx, avaliacaoFisica.getResponsavelLancamento_codigo());
            Anamnese questionarioParq = anamneseService.consultarParq(ctx, usu.getCodigo(), getViewUtils());
            questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
            List<ItemAvaliacaoFisica> parqs = avaliacaoService.obterItensAvaliacaoFisica(ctx, avaliacaoFisica.getCliente().getCodigo(), ItemAvaliacaoFisicaEnum.ANAMNESE, 1, true, avaliacaoFisica);
            if (!UteisValidacao.emptyList(parqs)) {
                avaliacaoService.obterRespostas(ctx, questionarioParq, parqs.get(0));
            }
            String pdf = avaliacaoService.gerarPDFParQ(ctx, questionarioParq, avaliacaoFisica, usu, getViewUtils(), request, sc, true, assinaturaDigital);
            mm.addAttribute(STATUS_SUCESSO, pdf);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/{ctx}/consultarClientesParQAssinaturaDigital", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap consultarClientesParQAssinaturaDigital(@PathVariable final String ctx,
                                                    @RequestParam final String empresaZw,
                                                    @RequestParam final String filtro,
                                                    @RequestParam(value = "diasParaVencimentoParq", required = false) final Integer diasParaVencimentoParq,
                                                    @RequestParam(value = "todos", required = false) final String todos,
                                                    HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            String filtroDecoded = URLDecoder.decode(filtro, "UTF-8");
            mm.addAttribute("return", avaliacaoService.consultarClientesParQAssinaturaDigital(ctx, Integer.parseInt(empresaZw), filtroDecoded, request, diasParaVencimentoParq, todos));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Obter perguntas do questionário PAR-Q",
                  notes = "Retorna as perguntas do questionário PAR-Q (Physical Activity Readiness Questionnaire) para avaliação de prontidão para atividade física, " +
                         "incluindo configurações de exibição e respostas já preenchidas pelo aluno quando informado o código da resposta.",
                  tags = "Avaliação Física")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Perguntas do questionário PAR-Q obtidas com sucesso",
                     response = ExemploRespostaObterPerguntasParQ.class)
    })
    @RequestMapping(value = "/{ctx}/obterPerguntasParQ", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap obterPerguntasParQ(@ApiParam(value = "Contexto da empresa", defaultValue = "empresa123")
                                @PathVariable final String ctx,
                                @ApiParam(value = "Código da resposta PAR-Q do aluno para recuperar respostas já preenchidas",
                                         defaultValue = "1001")
                                @RequestParam(required = false) final String codigoRespostaParq) {
        ModelMap mm = new ModelMap();
        try {
            Integer codigoAnamnese = null;
            try {
                Anamnese questionarioParq = anamneseService.consultarParq(ctx, 0, getViewUtils());
                codigoAnamnese = questionarioParq.getCodigo();
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logar(e, AvaliacaoJSONControle.class);
            }
            Integer codigoResposta = UteisValidacao.emptyString(codigoRespostaParq) ? 0 : Integer.parseInt(codigoRespostaParq);
            List<RespostaCliente> respostasCliente = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(codigoResposta)) {
                RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCodigo(ctx, codigoResposta);
                respostasCliente = avaliacaoService.obterRespostasCliente(ctx, rcp.getCliente().getCodigo(), rcp.getCodigo());
            }
            mm.addAttribute("perguntasParQ", avaliacaoService.obterPerguntasParQ(ctx, codigoAnamnese));
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            mm.addAttribute("apresentarLeiParq", css.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_VISUALIZAR_LEI_PARQ).getValorAsBoolean());
            mm.addAttribute("respostasClienteParQ", avaliacaoService.respostaClienteToTO(respostasCliente));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Salvar respostas do questionário PAR-Q",
                  notes = "Salva as respostas do questionário de prontidão para atividade física (PAR-Q) de um aluno. " +
                         "O PAR-Q é um questionário obrigatório que avalia se o aluno está apto para iniciar atividades físicas. " +
                         "As respostas são processadas e determina se o PAR-Q é positivo ou negativo, influenciando na liberação do aluno para treinos.",
                  tags = "Avaliação")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Respostas do PAR-Q salvas com sucesso", response = ExemploRespostaSalvarRespostasParQ.class)
    })
    @RequestMapping(value = "/{ctx}/salvarRespostasParQ", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap salvarRespostasParQ(@ApiParam(value = "Contexto da empresa", defaultValue = "empresa123")
                                 @PathVariable final String ctx,
                                 @ApiParam(value = "JSON contendo as respostas do questionário PAR-Q. " +
                                          "Deve conter a matrícula do aluno, assinatura digital em base64 (opcional) e as respostas das perguntas. " +
                                          "<br/><strong>Estrutura esperada:</strong><br/>" +
                                          "- <strong>matriculaAluno:</strong> Matrícula do aluno que está respondendo o questionário<br/>" +
                                          "- <strong>assinatura64:</strong> Assinatura digital do aluno em formato base64 (opcional)<br/>" +
                                          "- <strong>respostas:</strong> Array com as respostas das perguntas do PAR-Q, onde cada resposta contém o código da pergunta e a resposta (1 para SIM, 0 para NÃO)",
                                          defaultValue = "{\"matriculaAluno\":\"12345\",\"assinatura64\":\"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==\",\"respostas\":[{\"codigoPergunta\":1,\"resposta\":\"0\",\"observacao\":\"\"},{\"codigoPergunta\":2,\"resposta\":\"1\",\"observacao\":\"Problema cardíaco controlado\"}]}")
                                 @RequestBody final String jsonRespostas,
                                 @ApiParam(value = "Código do usuário responsável pelo preenchimento do questionário", defaultValue = "1001")
                                 @RequestParam final String usuarioZw) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, anamneseService.salvarRespostasParQ(ctx, new JSONObject(jsonRespostas), Integer.parseInt(usuarioZw)));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/{ctx}/salvarEdicaoRespostasParQ", method = RequestMethod.POST)
    public
    @ResponseBody
    ModelMap salvarEdicaoRespostasParQ(@PathVariable final String ctx,
                                       @RequestBody final String jsonRespostas,
                                       @RequestParam final String usuarioZw,
                                       @RequestParam final String codigoRespostaParq) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, anamneseService.salvarEdicaoRespostasParQ(ctx, new JSONObject(jsonRespostas), Integer.parseInt(usuarioZw), Integer.parseInt(codigoRespostaParq)));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Gerar PDF do questionário PAR-Q com assinatura digital",
                  notes = "Gera o PDF do questionário PAR-Q (Physical Activity Readiness Questionnaire) preenchido pelo aluno " +
                         "incluindo a assinatura digital capturada. Retorna a URL do PDF gerado e a URL da assinatura digital " +
                         "quando disponível. O questionário PAR-Q é utilizado para avaliar a prontidão do aluno para atividade física.",
                  tags = "Avaliação Física")
    @ApiResponses({
        @ApiResponse(code = 200, message = "PDF do questionário PAR-Q gerado com sucesso",
                     response = ExemploRespostaImprimirParQAssinaturaDigital.class)
    })
    @RequestMapping(value = "/{ctx}/imprimirParQAssinaturaDigital", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap imprimirParQAssinaturaDigital(@PathVariable final String ctx,
                          @ApiParam(value = "Matrícula do aluno para geração do PDF do questionário PAR-Q",
                                    required = true,
                                    defaultValue = "ALU001")
                          @RequestParam final String matricula,
                          @ApiParam(value = "Código da resposta específica do questionário PAR-Q. " +
                                           "Se não informado, será utilizada a resposta mais recente do aluno",
                                    required = false,
                                    defaultValue = "123")
                          @RequestParam(required = false) final String codigoRespostaParq,
                          HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            Integer codigoResposta = UteisValidacao.emptyString(codigoRespostaParq) ? 0 : Integer.parseInt(codigoRespostaParq);
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
            RespostaClienteParQ rcp = new RespostaClienteParQ();
            if (!UteisValidacao.emptyNumber(codigoResposta)) {
                rcp = parQDao.consultarRespostaParQPorCodigo(ctx, codigoResposta);
            } else {
                rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
            }
            Anamnese questionarioParq = anamneseService.consultarParq(ctx, rcp.getUsuario_codigo(), getViewUtils());
            questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
            List<RespostaCliente> respostasCliente = avaliacaoService.obterRespostasCliente(ctx, cliente.getCodigo(), rcp.getCodigo());
            mm.addAttribute("return", avaliacaoService.gerarPDFParQAssinaturaDigital(ctx, questionarioParq, respostasCliente, rcp, getViewUtils(), request, sc, true, cliente.getCodigo()));
            if (!UteisValidacao.emptyString(rcp.getUrlAssinatura())) {
                mm.addAttribute("assinatura", rcp.getFullUrlAssinatura());
            }else{
                mm.addAttribute("assinatura", "");
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/{ctx}/removerAssinaturaParQ", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap removerAssinaturaParQ(@PathVariable final String ctx,
                                           @RequestParam final String matricula) {
        ModelMap mm = new ModelMap();
//        A ASSINATURA DO ALUNO É O QUE ASSEGURA A VALIDADE DO PARQ E NÃO DEVE SER REMOVIDA, DESTA DATA EM DIANTE A ESTRUTURA DO PARQ FOI REMODELADA PARA ATENDER AO NOVO CENÁRIO
//        try {
//            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula);
//            RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
//            if (!UteisValidacao.emptyString(rcp.getUrlAssinatura())) {
//                MidiaService.getInstanceWood().deleteObject(rcp.getUrlAssinatura());
//                rcp.setUrlAssinatura(null);
//                parQDao.update(ctx, rcp);
//            }
//        } catch (Exception ex) {
//            mm.addAttribute(STATUS_ERRO, ex.getMessage());
//            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
//        }
        return mm;
    }

    @ApiOperation(value = "Listar respostas do questionário PAR-Q por cliente",
                  notes = "Retorna as respostas do questionário PAR-Q (Physical Activity Readiness Questionnaire) de um aluno específico, " +
                         "incluindo informações sobre cada resposta fornecida, observações e dados de assinatura digital.",
                  tags = "Avaliação Física")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Respostas do questionário PAR-Q obtidas com sucesso",
                     response = ExemploRespostaListarRespostasParQPorCliente.class)
    })
    @RequestMapping(value = "/{ctx}/listarRespostasParQPorCliente", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap listarRespostasParQPorCliente(@ApiParam(value = "Contexto da empresa", defaultValue = "empresa123")
                                           @PathVariable final String ctx,
                                           @ApiParam(value = "Código identificador do cliente no sistema", defaultValue = "1001")
                                           @RequestParam final Integer clienteCodigo,
                                           HttpServletRequest request) {
        // @RequestParam final Integer clienteCodigo deve ser o codigo do clientesintetico
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteService.obterPorCodigo(ctx, clienteCodigo);
            if (cliente == null || UteisValidacao.emptyNumber(cliente.getCodigo())) {
                mm.addAttribute(STATUS_ERRO, AlunoExcecoes.ERRO_ALUNO_NAO_ENCONTRADO);
            } else {
                RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCliente(ctx, clienteCodigo);
                if (rcp == null || UteisValidacao.emptyNumber(rcp.getCodigo())) {
                    mm.addAttribute(STATUS_ERRO, AvaliacaoFisicaExcecoes.PARQ_NAO_RESPONDIDO);
                } else {
                    Anamnese questionarioParq = anamneseService.consultarParq(ctx, rcp.getUsuario_codigo(), getViewUtils());
                    questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
                    List<RespostaCliente> respostasCliente = avaliacaoService.obterRespostasCliente(ctx, clienteCodigo, rcp.getCodigo());
                    List<RespostaClienteJSON> respostasClienteJson = new ArrayList<>();
                    respostasCliente.stream().forEach((RespostaCliente respostaCliente) -> {
                        RespostaClienteJSON respostaJson = new RespostaClienteJSON(respostaCliente);
                        respostasClienteJson.add(respostaJson);
                    });

                    mm.addAttribute(RETURN, respostasClienteJson);
                }
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(value = "Listar respostas do questionário PAR-Q por matrícula (versão 2)",
                  notes = "Retorna as respostas do questionário PAR-Q (Physical Activity Readiness Questionnaire) de um aluno específico " +
                         "utilizando a matrícula como identificador, incluindo informações sobre cada resposta fornecida, observações e dados de assinatura digital.",
                  tags = "Avaliação Física")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Respostas do questionário PAR-Q obtidas com sucesso",
                     response = ExemploRespostaListarRespostasParQPorClienteV2.class)
    })
    @RequestMapping(value = "/{ctx}/v2/listarRespostasParQPorCliente", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap listarRespostasParQPorClienteV2(@ApiParam(value = "Contexto da empresa", defaultValue = "empresa123")
                                             @PathVariable final String ctx,
                                             @ApiParam(value = "Número da matrícula do aluno", defaultValue = "2024001")
                                             @RequestParam final Integer matricula,
                                             HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            ClienteSintetico cliente = clienteService.consultarPorMatricula(ctx, matricula.toString());
            if (cliente == null || UteisValidacao.emptyNumber(cliente.getCodigo())) {
                mm.addAttribute(STATUS_ERRO, AlunoExcecoes.ERRO_ALUNO_NAO_ENCONTRADO);
            } else {
                RespostaClienteParQ rcp = parQDao.consultarRespostaParQPorCliente(ctx, cliente.getCodigo());
                if (rcp == null || UteisValidacao.emptyNumber(rcp.getCodigo())) {
                    mm.addAttribute(STATUS_ERRO, AvaliacaoFisicaExcecoes.PARQ_NAO_RESPONDIDO);
                } else {
                    Anamnese questionarioParq = anamneseService.consultarParq(ctx, rcp.getUsuario_codigo(), getViewUtils());
                    questionarioParq.setPerguntas(anamneseService.obterPerguntasAnamnese(ctx, questionarioParq.getCodigo()));
                    List<RespostaCliente> respostasCliente = avaliacaoService.obterRespostasCliente(ctx, cliente.getCodigo(), rcp.getCodigo());
                    List<RespostaClienteJSON> respostasClienteJson = new ArrayList<>();
                    respostasCliente.stream().forEach((RespostaCliente respostaCliente) -> {
                        RespostaClienteJSON respostaJson = new RespostaClienteJSON(respostaCliente);
                        respostasClienteJson.add(respostaJson);
                    });
                    mm.addAttribute(RETURN, respostasClienteJson);
                }
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "/{ctx}/is-parq-aluno-assinado/{empresaZw}/{matricula}", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap isParqAlunoAssinado(@PathVariable final String ctx,
                                 @PathVariable final String empresaZw,
                                 @PathVariable final String matricula,
                                 HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute("assinado", avaliacaoService.isParqAlunoAssinado(ctx, Integer.parseInt(empresaZw), matricula, request));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }

        return mm;
    }

    @RequestMapping(value = "/{ctx}/obter-mat-alunos-parq/{empresaZw}/{tipoConsulta}", method = RequestMethod.GET)
    public
    @ResponseBody
    ModelMap obterMatAlunosParq(@PathVariable final String ctx,
                                 @PathVariable final String empresaZw,
                                 @PathVariable final String tipoConsulta,
                                @RequestParam(value = "diasParaVencimentoParq", required = false) final Integer diasParaVencimentoParq,
                                 HttpServletRequest request) {
        // tipoConsulta = assinado; vencido; nao_assinado; todos;
        ModelMap mm = new ModelMap();
        try {
            mm.addAttribute(RETURN, avaliacaoService.obterMatAlunosParq(ctx, Integer.parseInt(empresaZw), tipoConsulta, diasParaVencimentoParq));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

}
