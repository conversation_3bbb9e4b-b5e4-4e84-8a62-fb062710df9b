package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.AcessosExecucoesBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO contendo dados de execuções de treino, incluindo estatísticas de acessos e uso de dispositivos.")
public class ExecucoesTreinoDTO {

    @ApiModelProperty(value = "Total de execuções de treino realizadas.", example = "45")
    private Integer execucoesTreino;

    @ApiModelProperty(value = "Total de alunos que executaram treinos.", example = "32")
    private Integer alunosDoTreino;

    @ApiModelProperty(value = "Total de execuções realizadas via smartphone.", example = "28")
    private Integer smartphone;

    @ApiModelProperty(value = "Total de acessos ao sistema de treino.", example = "67")
    private Integer acessos;

    public ExecucoesTreinoDTO() {

    }
    public ExecucoesTreinoDTO(AcessosExecucoesBI acessos) {
        this.execucoesTreino = acessos.getExecucoes();
        this.alunosDoTreino = acessos.getAcessotreino();
        this.smartphone = acessos.getSmartphone();
        this.acessos = acessos.getAcessos();
    }

    public Integer getExecucoesTreino() {
        return execucoesTreino;
    }

    public void setExecucoesTreino(Integer execucoesTreino) {
        this.execucoesTreino = execucoesTreino;
    }

    public Integer getAlunosDoTreino() {
        return alunosDoTreino;
    }

    public void setAlunosDoTreino(Integer alunosDoTreino) {
        this.alunosDoTreino = alunosDoTreino;
    }

    public Integer getSmartphone() {
        return smartphone;
    }

    public void setSmartphone(Integer smartphone) {
        this.smartphone = smartphone;
    }

    public Integer getAcessos() {
        return acessos;
    }

    public void setAcessos(Integer acessos) {
        this.acessos = acessos;
    }
}
