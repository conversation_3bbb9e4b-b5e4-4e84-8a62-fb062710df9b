package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.tipoEvento.TipoEvento;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar um tipo de evento, contendo identificador e descrição do evento.")
public class TipoDeEventosDTO{

    @ApiModelProperty(value = "Identificador único do tipo de evento", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Descrição do tipo de evento", example = "Avaliação Física")
    private String descricao;


    public TipoDeEventosDTO(TipoEvento tipoEvento){
        this.id = tipoEvento.getCodigo();
        this.descricao = tipoEvento.getNome();
    }


    public Integer getId() {return id;}
    public void setId(Integer id) {this.id = id;}
    public String getDescricao() {return descricao;}
    public void setDescricao(String descricao) {this.descricao = descricao;}
}