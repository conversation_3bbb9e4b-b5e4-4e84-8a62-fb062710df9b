package br.com.pacto.controller.json.anamnese;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.avaliacao.FiltrosJSON;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.anamnese.AnamneseService;
import br.com.pacto.swagger.respostas.anamnese.ExemploRespostaAnamneseResponseTO;
import br.com.pacto.swagger.respostas.anamnese.ExemploRespostaListAnamneseResponseTO;
import br.com.pacto.swagger.respostas.anamnese.ExemploRespostaListAnamneseResponseTOPaginacao;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 03/08/2018.
 */
@Controller
@RequestMapping("/psec/anamneses")
public class AnamneseController {

    private final AnamneseService anamneseService;

    @Autowired
    public AnamneseController(AnamneseService anamneseService) {
        Assert.notNull(anamneseService, "O serviço de anamnese não foi injetado corretamente");
        this.anamneseService = anamneseService;
    }

    @ApiOperation(
            value = "Consultar fichas de anamnese",
            notes = "Consulta os tipos de fichas de anamnese cadastrados no sistema. Para cada ficha, serão exibidos o nome e as perguntas que o compõem.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAnamneseResponseTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamneses(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON." +
                    "<br/><strong>É necessário fazer o ENCONDE na URL para realizar a requisição com os filtros.</strong></br>" +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong>Filtra pelo nome do tipo de formulário de anamnese</li>" +
                    "<li><strong>status:</strong>Filtra pelos status da anamnese (Deve ser informado como uma lista ex: [\"ATIVO\"]) </li>" +
                    "</ul>", defaultValue = "{\"quicksearchValue\":\"Sênior\", status: [\"ATIVO\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltrosJSON filtroAnamneseJSON = new FiltrosJSON(filtros);
            return ResponseEntityFactory.ok(anamneseService.consultarAnamneses(filtroAnamneseJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os anamneses", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar ficha de anamnese",
            notes = "Consulta as informações de uma ficha de anamnese cadastrada no sistema. Para cada ficha, serão exibidos o nome e as perguntas que o compõem.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAnamneseResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAnamnese(@ApiParam(value = "Código único identificador da ficha de anamnese", required = true, defaultValue = "3")
                                                                 @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(anamneseService.consultarAnamnese(id));
        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar a anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar ficha de anamnese",
            notes = "Cadastra as informações de uma ficha de anamnese no sistema. Para cada ficha, serão cadastrada o nome e as perguntas que o compõem.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAnamneseResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirAnamneses(@ApiParam(value = "Informações para cadastrar uma ficha de anamnese")
                                                                @RequestBody AnamneseTO anamneseTO) {

        try {
            return ResponseEntityFactory.ok(anamneseService.inserir(anamneseTO));
        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar incluir anamnese", e);
            return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Alterar informações de uma ficha de anamnese",
            notes = "Altera as informações de uma ficha de anamnese que já foi cadastrada no sistema.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAnamneseResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAnamneses(@ApiParam(value = "Código único identificador da ficha de anamnese que será atualizada", defaultValue = "3", required = true)
                                                                @PathVariable("id") final Integer id,
                                                                @ApiParam(value = "Informações da ficha de anamnese que será atualizada. O Código Identificador deve ser informado pela URL")
                                                                @RequestBody AnamneseTO anamneseTO) {
        try {
            anamneseTO.setId(id);
            return ResponseEntityFactory.ok(anamneseService.alterar(anamneseTO));
        } catch (ServiceException e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar anamnese", e);
            if (StringUtils.isBlank(e.getChaveExcecao()) && e.getChaveExcecao().equalsIgnoreCase("validacao_anamnese_ja_existe")) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Excluir informações de uma ficha de anamnese",
            notes = "Deleta as informações de uma ficha de anamnese.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)")
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirAnamnese(@ApiParam(value = "Código identificador da ficha de anamnese que será excluída", required = true, defaultValue = "3")
                                                               @PathVariable("id") final Integer id) {
        try {
            anamneseService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ValidacaoException e) {
            return ResponseEntityFactory.erroConhecido(e.getMessage(), e.getMessage());
        } catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar todas as fichas de anamnese",
            notes = "Consulta todos os tipos de fichas de anamnese cadastrados no sistema. Para cada ficha, serão exibidos o nome e as perguntas que o compõem.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAnamneseResponseTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/todas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodas() {
        try {
            return ResponseEntityFactory.ok(anamneseService.obterTodas(false, false));
        } catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamnese", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todas as fichas de anamnese integradas",
            notes = "Consulta todos os tipos de fichas de anamnese integradas. Para cada ficha, serão exibidos o nome e as perguntas que o compõem.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAnamneseResponseTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/todas-integradas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasIntegradas() {
        try {
            return ResponseEntityFactory.ok(anamneseService.obterTodas(true, false));
        } catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamneses integradas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar todas as fichas de anamnese integradas e ativas",
            notes = "Consulta todos os tipos de fichas de anamnese integradas e ativas. Para cada ficha, serão exibidos o nome e as perguntas que o compõem.<br/>" +
                    "Um formulário de anamnese é uma entrevista estruturada utilizada por profissionais de educação física. Seu objetivo é coletar informações detalhadas " +
                    "sobre a condição física, histórico esportivo e metas de treino do cliente.",
            tags = "Anamneses"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAnamneseResponseTO.class),
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ANAMNESE)
    @RequestMapping(value = "/todas-integradas-ativas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterTodasIntegradasAtivas() {
        try {
            return ResponseEntityFactory.ok(anamneseService.obterTodas(true, true));
        } catch (Exception e) {
            Logger.getLogger(AnamneseController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter todas anamneses integradas", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
}
