package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.bi.TipoEventoDisponibilidadeBI;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "DTO contendo dados de Business Intelligence relacionados à agenda e agendamentos, incluindo estatísticas de execução, faltas e avaliações.")
public class BITreinoAgendaDTO {
    @ApiModelProperty(value = "Total de agendamentos realizados.", example = "245")
    private Integer agendamentos;

    @ApiModelProperty(value = "Total de agendamentos executados (alunos que compareceram).", example = "198")
    private Integer executados;

    @ApiModelProperty(value = "Total de faltas em agendamentos.", example = "32")
    private Integer faltas;

    @ApiModelProperty(value = "Total de agendamentos cancelados.", example = "15")
    private Integer cancelados;

    @ApiModelProperty(value = "Total de professores ativos.", example = "12")
    private Integer professores;

    @ApiModelProperty(value = "Total de horas de disponibilidade dos professores.", example = "480")
    private Integer horasDisponibilidades;

    @ApiModelProperty(value = "Total de horas efetivamente executadas.", example = "385")
    private Integer horasExecutadas;

    @ApiModelProperty(value = "Percentual de ocupação das disponibilidades.", example = "80")
    private Integer ocupacao;

    @ApiModelProperty(value = "Total de novos treinos criados.", example = "28")
    private Integer novosTreinos;

    @ApiModelProperty(value = "Total de treinos renovados.", example = "42")
    private Integer treinosRenovados;

    @ApiModelProperty(value = "Total de treinos revisados.", example = "15")
    private Integer treinosRevisados;

    @ApiModelProperty(value = "Total de avaliações físicas realizadas.", example = "67")
    private Integer avaliacoesFisicas;

    @ApiModelProperty(value = "Nome do professor quando filtro específico é aplicado.", example = "João Silva")
    private String nomeProfessor;

    @ApiModelProperty(value = "Total de alunos sem avaliação física.", example = "23")
    private Integer totalAlunosSemAvaliacaoFisica;

    @ApiModelProperty(value = "Percentual de avaliações realizadas.", example = "85")
    private Integer percentualAvaliacoes;

    @ApiModelProperty(value = "Lista de agendamentos por tipo de comportamento.")
    private List<AgendamentoPorTipoComportamentoDTO> agendamentosPorTipoComportamento = new ArrayList<>();

    public BITreinoAgendaDTO(DashboardBI dash, List<TipoEventoDisponibilidadeBI> listaTiposBi) {
        this.agendamentos = dash.getAgendamentos();
        this.executados = dash.getCompareceram();
        this.faltas = dash.getFaltaram();
        this.cancelados = dash.getCancelaram();
        this.professores = dash.getProfessores();
        this.horasDisponibilidades = dash.getHorasDisponibilidade();
        this.horasExecutadas = dash.getHorasAtendimento();
        this.ocupacao = dash.getOcupacao();
        this.novosTreinos = dash.getNovosTreinos();
        this.treinosRenovados = dash.getTreinosRenovados();
        this.treinosRevisados = dash.getTreinosRevisados();
        this.avaliacoesFisicas = dash.getAvaliacoesFisicas();
        this.totalAlunosSemAvaliacaoFisica = dash.getTotalAlunosSemAvaliacoes();
        this.percentualAvaliacoes = dash.getPercentualAvaliacoes();
        List<TipoAgendamentoEnum> tipos = new ArrayList<>();
        for (TipoEventoDisponibilidadeBI t : listaTiposBi) {
            if (tipos.contains(t.getTipo())) {
                continue;
            }
            this.agendamentosPorTipoComportamento.add(new AgendamentoPorTipoComportamentoDTO(t));
            tipos.add(t.getTipo());
        }
    }

    public Integer getAgendamentos() {
        return agendamentos;
    }

    public void setAgendamentos(Integer agendamentos) {
        this.agendamentos = agendamentos;
    }

    public Integer getExecutados() {
        return executados;
    }

    public void setExecutados(Integer executados) {
        this.executados = executados;
    }

    public Integer getFaltas() {
        return faltas;
    }

    public void setFaltas(Integer faltas) {
        this.faltas = faltas;
    }

    public Integer getCancelados() {
        return cancelados;
    }

    public void setCancelados(Integer cancelados) {
        this.cancelados = cancelados;
    }

    public Integer getProfessores() {
        return professores;
    }

    public void setProfessores(Integer professores) {
        this.professores = professores;
    }

    public Integer getHorasDisponibilidades() {
        return horasDisponibilidades;
    }

    public void setHorasDisponibilidades(Integer horasDisponibilidades) {
        this.horasDisponibilidades = horasDisponibilidades;
    }

    public Integer getHorasExecutadas() {
        return horasExecutadas;
    }

    public void setHorasExecutadas(Integer horasExecutadas) {
        this.horasExecutadas = horasExecutadas;
    }

    public Integer getOcupacao() {
        return ocupacao;
    }

    public void setOcupacao(Integer ocupacao) {
        this.ocupacao = ocupacao;
    }

    public Integer getNovosTreinos() {
        return novosTreinos;
    }

    public void setNovosTreinos(Integer novosTreinos) {
        this.novosTreinos = novosTreinos;
    }

    public Integer getTreinosRenovados() {
        return treinosRenovados;
    }

    public void setTreinosRenovados(Integer treinosRenovados) {
        this.treinosRenovados = treinosRenovados;
    }

    public Integer getTreinosRevisados() {
        return treinosRevisados;
    }

    public void setTreinosRevisados(Integer treinosRevisados) {
        this.treinosRevisados = treinosRevisados;
    }

    public Integer getAvaliacoesFisicas() {
        return avaliacoesFisicas;
    }

    public void setAvaliacoesFisicas(Integer avaliacoesFisicas) {
        this.avaliacoesFisicas = avaliacoesFisicas;
    }

    public List<AgendamentoPorTipoComportamentoDTO> getAgendamentosPorTipoComportamento() {
        return agendamentosPorTipoComportamento;
    }

    public void setAgendamentosPorTipoComportamento(List<AgendamentoPorTipoComportamentoDTO> agendamentosPorTipoComportamento) {
        this.agendamentosPorTipoComportamento = agendamentosPorTipoComportamento;
    }
    public String getNomeProfessor() {return nomeProfessor;}
    public void setNomeProfessor(String nomeProfessor) {this.nomeProfessor = nomeProfessor;}

    public Integer getTotalAlunosSemAvaliacaoFisica() {
        return totalAlunosSemAvaliacaoFisica;
    }

    public void setTotalAlunosSemAvaliacaoFisica(Integer totalAlunosSemAvaliacaoFisica) {
        this.totalAlunosSemAvaliacaoFisica = totalAlunosSemAvaliacaoFisica;
    }

    public Integer getPercentualAvaliacoes() {
        return percentualAvaliacoes;
    }

    public void setPercentualAvaliacoes(Integer percentualAvaliacoes) {
        this.percentualAvaliacoes = percentualAvaliacoes;
    }
}
