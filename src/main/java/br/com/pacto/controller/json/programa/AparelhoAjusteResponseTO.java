package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.aparelho.AparelhoAjuste;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by joa<PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações dos ajustes disponíveis para o aparelho")
public class AparelhoAjusteResponseTO {

    @ApiModelProperty(value = "Código único identificador do ajuste", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do ajuste disponível para o aparelho", example = "Altura do banco")
    private String nome;

    public AparelhoAjusteResponseTO(){

    }

    public AparelhoAjusteResponseTO(AparelhoAjuste aparelhoAjuste){
        this.id = aparelhoAjuste.getCodigo();
        this.nome = aparelhoAjuste.getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
