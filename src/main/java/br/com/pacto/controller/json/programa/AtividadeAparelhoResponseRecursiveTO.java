package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by joa<PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações básicas do aparelho para listagem simplificada")
public class AtividadeAparelhoResponseRecursiveTO {

    @ApiModelProperty(value = "Nome do aparelho", example = "Esteira Ergométrica")
    private String nome;

    @ApiModelProperty(value = "Código único identificador do aparelho", example = "1")
    private Integer id;


    public AtividadeAparelhoResponseRecursiveTO(AtividadeAparelho atividadeAparelho) {
        this.id = atividadeAparelho.getAparelho().getCodigo();
        this.nome = atividadeAparelho.getAparelho().getNome();
    }
    public AtividadeAparelhoResponseRecursiveTO(Aparelho aparelho) {
        this.id = aparelho.getCodigo();
        this.nome = aparelho.getNome();
    }
    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
