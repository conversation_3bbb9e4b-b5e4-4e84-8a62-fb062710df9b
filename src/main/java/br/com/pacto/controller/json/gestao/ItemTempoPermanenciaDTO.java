package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO contendo dados de um item específico de tempo de permanência, incluindo valor e dados do aluno.")
public class ItemTempoPermanenciaDTO {

    @ApiModelProperty(value = "Valor do tempo de permanência em dias.", example = "120")
    private Integer valor;

    @ApiModelProperty(value = "Dados básicos do aluno.")
    private IndividuoBIDTO aluno;

    public ItemTempoPermanenciaDTO() {

    }

    public ItemTempoPermanenciaDTO(Integer valor, String matricula, String nome) {
        this.valor = valor;
        this.aluno = new IndividuoBIDTO(matricula, nome);
    }

    public Integer getValor() {
        return valor;
    }

    public void setValor(Integer valor) {
        this.valor = valor;
    }

    public IndividuoBIDTO getAluno() {
        return aluno;
    }

    public void setAluno(IndividuoBIDTO aluno) {
        this.aluno = aluno;
    }
}
