package br.com.pacto.controller.json.usuario;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações sobre o uso do aplicativo pelo aluno.")
public class AlunoAppInfoDTO {

    @ApiModelProperty(value = "Indica se o aluno utiliza o aplicativo", example = "true")
    private boolean usaApp = false;

    @ApiModelProperty(value = "Data de registro do uso do app pelo aluno", example = "2023-08-22T10:15:30Z")
    private Date dataRegistroUsoApp;

    @ApiModelProperty(value = "Identificador do cliente no aplicativo", example = "APP123456789")
    private String idClienteApp;

    public AlunoAppInfoDTO() {
        this.usaApp = false;
    }

    public boolean isUsaApp() {
        return usaApp;
    }

    public void setUsaApp(boolean usaApp) {
        this.usaApp = usaApp;
    }

    public Date getDataRegistroUsoApp() {
        return dataRegistroUsoApp;
    }

    public void setDataRegistroUsoApp(Date dataRegistroUsoApp) {
        this.dataRegistroUsoApp = dataRegistroUsoApp;
    }

    public String getIdClienteApp() {
        return idClienteApp;
    }

    public void setIdClienteApp(String idClienteApp) {
        this.idClienteApp = idClienteApp;
    }
}
