package br.com.pacto.controller.json.aluno;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Configurações para exibição da lista rápida de acesso no sistema.")
public class ConfigListaRapidaAcessoDTO {

    @ApiModelProperty(value = "Lista de itens configurados para acesso rápido.")
    private List<ListaRapidaAcessoDTO> lista = new ArrayList<>();

    @ApiModelProperty(
            value = "Indica se a visualização de pendências está habilitada.",
            example = "false"
    )
    private Boolean verPendencias = false;

    @ApiModelProperty(
            value = "Indica se o usuário pode visualizar todos os itens da lista.",
            example = "false"
    )
    private Boolean verTodos = false;

    @ApiModelProperty(
            value = "Mapa com configurações adicionais, identificadas por chave e valor booleano.",
            example = "{\"config\":true}"
    )
    private Map<String, Boolean> mapConfigs = new HashMap<>();
    public Boolean getVerPendencias() {
        return verPendencias;
    }

    public Map<String, Boolean> getMapConfigs() {
        return mapConfigs;
    }

    public void setMapConfigs(Map<String, Boolean> mapConfigs) {
        this.mapConfigs = mapConfigs;
    }

    public void setVerPendencias(Boolean verPendencias) {
        this.verPendencias = verPendencias;
    }

    public Boolean getVerTodos() {
        return verTodos;
    }

    public void setVerTodos(Boolean verTodos) {
        this.verTodos = verTodos;
    }

    public List<ListaRapidaAcessoDTO> getLista() {
        return lista;
    }

    public void setLista(List<ListaRapidaAcessoDTO> lista) {
        this.lista = lista;
    }
}
