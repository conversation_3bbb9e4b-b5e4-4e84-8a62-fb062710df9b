package br.com.pacto.controller.json.locacao;

import br.com.pacto.controller.json.agendamento.FiltrosNewDTO;
import br.com.pacto.objeto.JSONMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONObject;

@ApiModel(description = "Filtros para consulta de locações, permitindo refinar os resultados por nome, descrição e filtros avançados de disponibilidade.")
public class FiltrosLocacaoJSON {

    @ApiModelProperty(value = "Termo de busca rápida para filtrar por nome ou descrição da locação.", example = "Quadra")
    private String quicksearchValue;

    @ApiModelProperty(value = "Filtros avançados de disponibilidade e configurações específicas.")
    private FiltrosNewDTO filterNew;

    FiltrosLocacaoJSON(JSONObject json) {
        try {
            this.filterNew = JSONMapper.getObject(json.getJSONObject("filtrosNew"), FiltrosNewDTO.class);
        } catch (Exception ex) {
        }

        if (json != null) {
            this.quicksearchValue = json.optString("quicksearchValue");
        }
    }

    public String getQuicksearchValue() {
        return quicksearchValue;
    }

    public void setQuicksearchValue(String quicksearchValue) {
        this.quicksearchValue = quicksearchValue;
    }

    public FiltrosNewDTO getFilterNew() {
        return filterNew;
    }

    public void setFilterNew(FiltrosNewDTO filterNew) {
        this.filterNew = filterNew;
    }
}
