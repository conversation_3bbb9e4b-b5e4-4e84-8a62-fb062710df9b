package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.TimeZoneEnum;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.bean.pessoa.Email;
import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.pessoa.Telefone;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.ProfessorResponseTO;
import br.com.pacto.bean.programa.ProgramaTreino;
import br.com.pacto.bean.programa.ProgramaTreinoAndamento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 28/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do aluno")
public class AlunoResponseTO {
    @ApiModelProperty(value = "Identificador único do aluno", example = "123")
    private Integer id;
    @ApiModelProperty(value = "Matrícula do aluno no sistema ZW", example = "4567")
    private Integer matriculaZW;
    @ApiModelProperty(value = "Nome completo do aluno", example = "João Silva")
    private String nome;
    @ApiModelProperty(value = "Situação do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)\n", example = "ATIVO")
    private SituacaoAlunoEnum situacaoAluno;
    @ApiModelProperty(value = "Tipo de acesso do aluno à academia.\n\n " +
            "<strong>Valores disponíveis</strong>\n" +
            "- 0 (MOBILE)\n" +
            "- 1 (KEYBOARD)\n" +
            "- 2 (FINGER_PRINT)\n" +
            "- 3 (RECONHECIMENTO_FACIAL)", example = "LIBERADO")
    private TipoAcessoAulaEnum tipoAcesso;
    @ApiModelProperty(value = "Nível do aluno")
    private NivelResponseTO nivel;
    @ApiModelProperty(value = "Professor responsável pela aula")
    private ProfessorResponseTO professor;
    @ApiModelProperty(value = "Data de nascimento do aluno", example = "1990-05-15T00:00:00Z")
    private Date dataNascimento;
    @ApiModelProperty(value = "Data de inclusão do aluno no sistema", example = "2020-01-01T12:00:00Z")
    private Date dataInclusao;
    @ApiModelProperty(value = "Sexo do aluno.\n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "0 - N (Não informado)\n" +
            "1 - M (Masculino)\n" +
            "2 - F (Feminino)\n", example = "M")
    private SexoEnum sexo;
    @ApiModelProperty(value = "Lista de emails do aluno", example = "[\"<EMAIL>\", \"<EMAIL>\"]")
    private List<String> emails = new ArrayList<>();
    @ApiModelProperty(value = "Lista de telefones do aluno")
    private List<TelefoneDTO> fones = new ArrayList<>();
    @ApiModelProperty(value = "Plano associado ao aluno")
    private PlanoZWDTO planoZW;
    @ApiModelProperty(value = "Contrato associado ao aluno")
    private ContratoZWDTO contratoZW;
    @ApiModelProperty(value = "Situação do contrato.\n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NO - Normal\n" +
            "- AV - A vencer\n" +
            "- ATM - Atestado Médico\n" +
            "- CR - Carência\n" +
            "- CA - Cancelado\n" +
            "- DE - Desistente\n" +
            "- VE - Inativo Vencido\n" +
            "- TV - Trancado Vencido\n" +
            "- DI - Diária\n" +
            "- PE - Free Pass\n" +
            "- AA - Aula Avulsa", example = "NO")
    private SituacaoContratoZWEnum situacaoContratoZW;

    @ApiModelProperty(value = "Indica se o aluno usa o aplicativo", example = "false")
    private Boolean usarApp = false;
    @ApiModelProperty(value = "Nome de usuário no aplicativo", example = "joaosilva")
    private String appUsername;
    @ApiModelProperty(value = "Nome da empresa que o aluno está vincualdo", example = "Academia Pacto GO")
    private String nomeEmpresa;
    @ApiModelProperty(value = "Pressão arterial para apresentação", example = "120/80")
    private String pressaoApresentar;
    @ApiModelProperty(value = "Frequência cardíaca para apresentação", example = "70")
    private String fcApresentar;
    @ApiModelProperty(value = "Lista de objetivos do aluno", example = "[\"Perder peso\", \"Ganhar massa muscular\"]")
    private List<String> listaObjetivos;

    @ApiModelProperty(value = "Idade do aluno em anos", example = "32")
    private Integer idade;
    @ApiModelProperty(value = "URI da imagem do aluno", example = "http://exemplo.com/imagem.jpg")
    private String imageUri;

    @ApiModelProperty(value = "Nome do programa atual do aluno", example = "Programa Fitness")
    private String nomeProgramaAtual;
    @ApiModelProperty(value = "Total de aulas previstas no programa", example = "24")
    private Integer totalAulasPrevistas;
    @ApiModelProperty(value = "Número de treinos realizados pelo aluno", example = "18")
    private Integer nrTreinosRealizados;
    @ApiModelProperty(value = "Frequência de treino do aluno", example = "3 vezes por semana")
    private String frequencia;
    @ApiModelProperty(value = "Código externo do aluno", example = "123")
    private String codigoExterno;
    @ApiModelProperty(value = "Resultado do ParQ", example = "NEGATIVO")
    private Boolean resultadoParq;
    @ApiModelProperty(value = "Informação adicional do ParQ. Valores disponíveis: NAO_ASSINADO, NEGATIVO, POSITIVO", example = "NEGATIVO")
    private String infoParQ; //NAO_ASSINADO - NEGATIVO - POSITIVO
    @ApiModelProperty(value = "Código da empresa", example = "10")
    private Integer empresaCodigo;
    @ApiModelProperty(value = "Código ZW da empresa", example = "100")
    private Integer empresaCodZW;
    @ApiModelProperty(value = "Código do cliente", example = "200")
    private Integer codigoCliente;
    @ApiModelProperty(value = "Código passivo do aluno", example = "300")
    private Integer codigoPassivo;
    @ApiModelProperty(value = "Código da pessoa", example = "400")
    private Integer codigoPessoa;
    @ApiModelProperty(value = "Indica se o aluno está confirmado", example = "false")
    private Boolean confirmado = Boolean.FALSE;
    @ApiModelProperty(value = "Lista de programas de treino do aluno")
    private List<ProgramaTreinoAlunoResponseDTO> programas;
    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data do último treino realizado", example = "2025-06-01T15:00:00Z")
    private Date dataUltimoTreino;
    @ApiModelProperty(value = "Data de término do programa atual", example = "2025-12-01T15:00:00Z")
    @Temporal(TemporalType.TIMESTAMP)
    private Date terminoPrograma;
    @ApiModelProperty(value = "Indica se o aluno está autorizado", example = "false")
    private Boolean autorizado = false;
    private String codAcesso = "";

    @ApiModelProperty(value = "Justificativa para algum status ou alteração", example = "Mudança de plano")
    private String justificativa;
    @ApiModelProperty(value = "Status do ParQ", example = "true")
    private Boolean parq_status;

    public AlunoResponseTO() {
    }

    public AlunoResponseTO(ClienteSintetico cs, Boolean confirmado, Boolean treinoIndependente) {
        this(cs, null, null, null, null, null, null, confirmado, null, treinoIndependente, null, null);
    }

    public AlunoResponseTO(ClienteSintetico cs, Boolean confirmado, Boolean treinoIndependente, ProfessorSintetico professorSintetico, Boolean parq_status) {
        this(cs, null, null, null, null, null, null, confirmado, null, treinoIndependente, professorSintetico, parq_status);
    }

    public AlunoResponseTO(ClienteSintetico cs, Usuario usuarioAluno, ProgramaTreinoAndamento programaTreinoAndamento, Usuario usuarioProfessor, Boolean treinoIndependente) {
        this(cs, usuarioAluno, usuarioProfessor, programaTreinoAndamento, null, null, null, null, null, treinoIndependente, null, null);
    }

    public AlunoResponseTO(
            ClienteSintetico cs,
            Usuario usuarioAluno,
            Usuario usuarioProfessor,
            ProgramaTreinoAndamento programaTreinoAndamento,
            List<ProgramaTreino> listProgramas,
            Date dataUltimoTreino,
            Date terminoPrograma,
            Boolean confirmado, Boolean resultadoParq,
            Boolean treinoIndependente, ProfessorSintetico professorSintetico, Boolean parq_status) {

        this.confirmado = confirmado;
        this.id = cs.getCodigo();
        try {
            this.codigoCliente = cs.getCodigoCliente();
        } catch (Exception e) {
            Uteis.logar(e, AlunoResponseTO.class);
        }
        this.matriculaZW = cs.getMatricula();
        this.nome = cs.getNome();
        this.situacaoAluno = SituacaoAlunoEnum.getInstance(cs.getSituacao());
        this.pressaoApresentar = cs.getPressaoApresentar();
        this.fcApresentar = cs.getFcApresentar();
        this.listaObjetivos = cs.getObjetivosLista();
        this.dataUltimoTreino = dataUltimoTreino;
        this.terminoPrograma = terminoPrograma;
        this.codigoExterno = cs.getCodigoExterno();
        this.codigoPessoa = cs.getCodigoPessoa();

        if (cs.getProgramaVigente() != null) {
            this.nomeProgramaAtual = cs.getProgramaVigente().getNome();
            this.totalAulasPrevistas = cs.getProgramaVigente().getTotalAulasPrevistas() == null ? 0 : cs.getProgramaVigente().getTotalAulasPrevistas();

            if (programaTreinoAndamento != null) {
                this.frequencia = programaTreinoAndamento.getFrequenciaPercent();
                this.nrTreinosRealizados = programaTreinoAndamento.getNrTreinos() == null ? 0 : programaTreinoAndamento.getNrTreinos();
            }
        }

        List<ProgramaTreinoAlunoResponseDTO> listProg = new ArrayList<>();
        if (listProgramas != null && listProgramas.size() > 0) {
            for (ProgramaTreino programa : listProgramas) {
                ProgramaTreinoAlunoResponseDTO programaAluno = new ProgramaTreinoAlunoResponseDTO(programa,
                        cs.getEmpresaTreino() == null ? TimeZoneEnum.Brazil_GTM_3.getId() : cs.getEmpresaTreino().getTimeZoneDefault(), treinoIndependente);
                listProg.add(programaAluno);
            }
        }
        this.programas = listProg;

        if (cs.getNivelAluno() != null) {
            this.nivel = new NivelResponseTO(cs.getNivelAluno());
        }
        if (usuarioProfessor != null) {
            this.professor = new ProfessorResponseTO(usuarioProfessor, treinoIndependente);
        } else if (professorSintetico != null) {
            this.professor = new ProfessorResponseTO(professorSintetico, treinoIndependente, false);
        }
        this.dataNascimento = cs.getDataNascimento();
        this.idade = cs.getMesesIdade();
        if (cs.getSexo() != null && !cs.getSexo().trim().isEmpty()) {
            try {
                this.sexo = SexoEnum.valueOf(cs.getSexo().substring(0, 1).toUpperCase());
            } catch (IllegalArgumentException e) {
                this.sexo = SexoEnum.N;
            }
        } else {
            this.sexo = SexoEnum.N;
        }
        if (treinoIndependente) {
            for (Email email : cs.getPessoa().getEmails()) {
                this.emails.add(email.getEmail());
            }

            for (Telefone telefone : cs.getPessoa().getTelefones()) {
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setNumero(telefone.getTelefone());
                telefoneDTO.setTipo(telefone.getTipo());
                this.fones.add(telefoneDTO);
            }
            this.dataInclusao = cs.getDataMatricula();
        } else {
            this.planoZW = new PlanoZWDTO(cs.getNomeplano());
            this.contratoZW = new ContratoZWDTO(cs.getDataVigenciaAteAjustada(), TipoContratoZWEnum.getInstance(cs.getSituacaoMatriculaContrato()));
            this.situacaoContratoZW = SituacaoContratoZWEnum.getInstance(cs.getSituacaoContrato());
            this.emails = cs.getListaEmails();
            for (String telefone : cs.getListaTelefones()) {
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setNumero(telefone);
                if (telefone.length() == 13) {
                    telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                } else {
                    telefoneDTO.setTipo(TipoTelefoneEnum.FIXO);
                }
                this.fones.add(telefoneDTO);
            }
        }

        if (usuarioAluno != null) {
            try {
                Integer.parseInt(usuarioAluno.getUserName());
            } catch (Exception e) {
                this.appUsername = usuarioAluno.getUserName();
                this.usarApp = true;
            }
        }
        this.imageUri = cs.getUrlFoto();
        this.resultadoParq = resultadoParq;
        this.parq_status = parq_status;

        if (cs.getFreePassInicio() != null && cs.getFreePassFim() != null && Calendario.entreComHoraZerada(Calendario.hoje(), cs.getFreePassInicio(), cs.getFreePassFim())
                && (this.situacaoAluno == SituacaoAlunoEnum.VISITANTE || this.situacaoAluno == SituacaoAlunoEnum.INATIVO)) {
            this.situacaoContratoZW = SituacaoContratoZWEnum.FREE_PASS;
        }

    }

    public AlunoResponseTO(ClienteSintetico clienteSintetico) {
        this.id = id;
        this.nome = nome;
        this.dataNascimento = dataNascimento;
        this.sexo = sexo;
        this.emails = emails;
        this.fones = fones;
    }

    public AlunoResponseTO(Integer id, String nome, Boolean autorizado, String codAcesso) {
        this.id = id;
        this.nome = nome;
        if (autorizado) {
            this.matriculaZW = id;
        }
        this.autorizado = autorizado;
        this.codAcesso = codAcesso;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMatriculaZW() {
        return matriculaZW;
    }

    public void setMatriculaZW(Integer matriculaZW) {
        this.matriculaZW = matriculaZW;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public TipoAcessoAulaEnum getTipoAcesso() {
        return tipoAcesso;
    }

    public void setTipoAcesso(TipoAcessoAulaEnum tipoAcesso) {
        this.tipoAcesso = tipoAcesso;
    }

    public NivelResponseTO getNivel() {
        return nivel;
    }

    public void setNivel(NivelResponseTO nivel) {
        this.nivel = nivel;
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Date getDataInclusao() {
        return dataInclusao;
    }

    public void setDataInclusao(Date dataInclusao) {
        this.dataInclusao = dataInclusao;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<TelefoneDTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneDTO> fones) {
        this.fones = fones;
    }

    public String getAppUsername() {
        return appUsername;
    }

    public void setAppUsername(String appUsername) {
        this.appUsername = appUsername;
    }

    public SituacaoAlunoEnum getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(SituacaoAlunoEnum situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public String getPressaoApresentar() {
        return pressaoApresentar;
    }

    public void setPressaoApresentar(String pressaoApresentar) {
        this.pressaoApresentar = pressaoApresentar;
    }

    public String getFcApresentar() {
        return fcApresentar;
    }

    public void setFcApresentar(String fcApresentar) {
        this.fcApresentar = fcApresentar;
    }

    public List<String> getListaObjetivos() {
        return listaObjetivos;
    }

    public void setListaObjetivos(List<String> listaObjetivos) {
        this.listaObjetivos = listaObjetivos;
    }

    public String getNomeProgramaAtual() {
        return nomeProgramaAtual;
    }

    public void setNomeProgramaAtual(String nomeProgramaAtual) {
        this.nomeProgramaAtual = nomeProgramaAtual;
    }

    public Integer getNrTreinosRealizados() {
        return nrTreinosRealizados;
    }

    public void setNrTreinosRealizados(Integer nrTreinosRealizados) {
        this.nrTreinosRealizados = nrTreinosRealizados;
    }

    public Integer getTotalAulasPrevistas() {
        return totalAulasPrevistas;
    }

    public void setTotalAulasPrevistas(Integer totalAulasPrevistas) {
        this.totalAulasPrevistas = totalAulasPrevistas;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    public List<ProgramaTreinoAlunoResponseDTO> getProgramas() {
        return programas;
    }

    public void setProgramas(List<ProgramaTreinoAlunoResponseDTO> programas) {
        this.programas = programas;
    }

    public Date getDataUltimoTreino() {
        return dataUltimoTreino;
    }

    public void setDataUltimoTreino(Date dataUltimoTreino) {
        this.dataUltimoTreino = dataUltimoTreino;
    }

    public Date getTerminoPrograma() {
        return terminoPrograma;
    }

    public void setTerminoPrograma(Date terminoPrograma) {
        this.terminoPrograma = terminoPrograma;
    }

    public Boolean getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public Boolean getResultadoParq() {
        return resultadoParq;
    }

    public void setResultadoParq(Boolean resultadoParq) {
        this.resultadoParq = resultadoParq;
    }

    public PlanoZWDTO getPlanoZW() {
        return planoZW;
    }

    public void setPlanoZW(PlanoZWDTO planoZW) {
        this.planoZW = planoZW;
    }

    public ContratoZWDTO getContratoZW() {
        return contratoZW;
    }

    public void setContratoZW(ContratoZWDTO contratoZW) {
        this.contratoZW = contratoZW;
    }

    public SituacaoContratoZWEnum getSituacaoContratoZW() {
        return situacaoContratoZW;
    }

    public void setSituacaoContratoZW(SituacaoContratoZWEnum situacaoContratoZW) {
        this.situacaoContratoZW = situacaoContratoZW;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Boolean getAutorizado() {
        return autorizado;
    }

    public void setAutorizado(Boolean autorizado) {
        this.autorizado = autorizado;
    }

    public String getCodAcesso() {
        return codAcesso;
    }

    public void setCodAcesso(String codAcesso) {
        this.codAcesso = codAcesso;
    }

    public String getJustificativa() { return justificativa; }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public String getInfoParQ() {
        return infoParQ;
    }

    public void setInfoParQ(String infoParQ) {
        //NAO_ASSINADO - NEGATIVO - POSITIVO
        this.infoParQ = infoParQ;
    }

    public Integer getEmpresaCodigo() {
        return empresaCodigo;
    }

    public void setEmpresaCodigo(Integer empresaCodigo) {
        this.empresaCodigo = empresaCodigo;
    }

    public Integer getEmpresaCodZW() {
        return empresaCodZW;
    }

    public void setEmpresaCodZW(Integer empresaCodZW) {
        this.empresaCodZW = empresaCodZW;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Boolean getParq_status() {
        return parq_status;
    }

    public void setParq_status(Boolean parq_status) {
        this.parq_status = parq_status;
    }
}
