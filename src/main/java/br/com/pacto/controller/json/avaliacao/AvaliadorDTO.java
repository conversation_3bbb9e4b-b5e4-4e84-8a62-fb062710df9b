package br.com.pacto.controller.json.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by paulo on 01/07/2019.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados do profissional que realizou a avaliação.")
public class AvaliadorDTO {

    public AvaliadorDTO(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    @ApiModelProperty(value = "Identificador único do avaliador.", example = "789")
    private Integer id;

    @ApiModelProperty(value = "Nome completo do profissional avaliador.", example = "<PERSON>. <PERSON>")
    private String nome;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
