/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.scheduling.treino.TaskCompromissoTreino;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.avaliacao.AnamneseTreinoPorIADTO;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.ficha.Ficha;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.bean.perfil.permissao.TipoPermissaoEnum;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.programa.*;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.serie.SerieRealizada;
import br.com.pacto.bean.serie.TreinoRealizado;
import br.com.pacto.bean.sincronizacao.TipoClassSincronizarEnum;
import br.com.pacto.bean.usuario.TipoUsuarioEnum;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.acompanhamento.AcompanhamentoSimplesJSON;
import br.com.pacto.controller.json.atividade.AtividadeJSONControle;
import br.com.pacto.controller.json.atividade.TemaAtividade;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.base.EndpointDocController;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.ficha.read.FichaJSON;
import br.com.pacto.controller.json.ficha.read.FichaVersaoJSON;
import br.com.pacto.controller.json.programa.read.*;
import br.com.pacto.controller.json.programa.write.ProgramaWriteAppJSON;
import br.com.pacto.controller.json.programa.write.ProgramaWriteJSON;
import br.com.pacto.dao.intf.programa.ProgramaTreinoAndamentoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoDao;
import br.com.pacto.dao.intf.programa.ProgramaTreinoFichaDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.cliente.perfil.FichaDoDiaDTO;
import br.com.pacto.service.impl.notificacao.excecao.ProgramaTreinoExcecoes;
import br.com.pacto.service.intf.cliente.ClienteRedeEmpresaService;
import br.com.pacto.service.intf.cliente.ClienteSinteticoService;
import br.com.pacto.service.intf.cliente.PerfilAlunoService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.ficha.SerieService;
import br.com.pacto.service.intf.programa.ObjetivoPredefinidoService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.service.intf.sincronizacao.SincronizacaoService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.impl.Ordenacao;
import org.json.JSONException;
import org.apache.http.NameValuePair;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import br.com.pacto.service.intf.programa.PrescricaoService;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaProgramaTreinoResponseTO;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaListAulaAlunoDTO;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaAulasAgendadasPorAlunoV2;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaSubmitTreinoCommentAPP;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaSubmitTreinoComment;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaSubmitTreinoCommentAPPV2;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaProgramaAtual;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaProgramaAtualCriptografado;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaVersaoProgramaAtual;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaSubmitSerie;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaSubmitTreino;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaExecutarFicha;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaAtualizarNrTreinos;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaSelecaoFicha;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaObjetivosPredef;
import br.com.pacto.swagger.respostas.programatreino.ExemploRespostaObjetivosPredefinidosSync;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/programa")
public class ProgramaTreinoJSONControle extends SuperControle {

    @Autowired
    private ProgramaTreinoService ps;
    @Autowired
    private ProgramaTreinoAndamentoDao programaTreinoAndamentoDao;
    @Autowired
    private ProgramaTreinoFichaDao programaTreinoFichaDao;
    @Autowired
    private ProgramaTreinoDao programatreinoDao;
    @Autowired
    private ClienteSinteticoService cs;
    @Autowired
    private SerieService ss;
    @Autowired
    private UsuarioService us;
    @Autowired
    private ObjetivoPredefinidoService ops;
    @Autowired
    private SincronizacaoService syncs;
    @Autowired
    private ConfiguracaoSistemaService configService;
    @Autowired
    private ClienteRedeEmpresaService clienteRedeEmpresaService;
    @Autowired
    private PrescricaoService prescricaoService;
    @Autowired
    private FichaService fichaService;
    @Autowired
    private  PerfilAlunoService perfilAlunoService;
    private static final ConcurrentHashMap<String,String> mapaExec = new ConcurrentHashMap();
    private ProgramaTreinoAndamento programaTreinoAndamento = new ProgramaTreinoAndamento();

    public static ProgramaTreinoJSON preencherProgramaJSON(ProgramaTreino programa, final String ctx,
                                                           final Ficha fichaUnica, Boolean agruparSeries, String ordenarFicha, OrigemEnum origemEnum) throws ServiceException {
        ProgramaTreinoJSON programaJSON = new ProgramaTreinoJSON();
        boolean treinoIndependente = false;
        try {
            treinoIndependente = SuperControle.independente(ctx);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String urlBase = "";
        try {
            urlBase = TemaAtividade.getURLBase(ctx);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            programaJSON.setCod(programa.getCodigo());
            programaJSON.setNome(programa.getNome());
            programaJSON.setNomeMetodo("");
            programaJSON.setDescricaoMetodo("");
            programaJSON.setVersao(programa.getVersao().toString());
            programaJSON.setDataInicio(programa.getDataInicio());
            programaJSON.setDataTerminoPrevisto(programa.getDataTerminoPrevisto());
            String revisado = programa.isRevisado() ? "Sim" : "Não";
            programaJSON.setRevisado(revisado);
            programaJSON.setDataProximaRevisao(programa.getDataProximaRevisao());
            programaJSON.setProfessorCarteira(programa.getProfessorCarteira() == null
                    ? "<SEM PROFESSOR>" : programa.getProfessorCarteira().getNome());
            programaJSON.setCref(programa.getProfessorCarteira() == null
                    ? "<SEM CREF>" : programa.getProfessorCarteira().getCref());
            programaJSON.setCrefProfessorMontou(programa.getProfessorMontou() == null
                    ? "<SEM CREF>" : programa.getProfessorMontou().getCref());
            programaJSON.setProfessorMontou(programa.getProfessorMontou() == null ?
                    "<SEM PROFESSOR>" : programa.getProfessorMontou().getNome());

            programaJSON.setUsarNovaMontagemFicha(programa.getUsarNovaMontagemFicha());
            programaJSON.setGeradoPorIA((programa.getGeradoPorIA()));
            programaJSON.setEmRevisaoProfessor(programa.getEmRevisaoProfessor());

            StringBuilder objetivos = new StringBuilder();
            for (ObjetivoPrograma obj : programa.getObjetivos()) {
                objetivos.append(obj.getObjetivo().getNome()).append("\n");
            }
            programaJSON.setObjetivos(objetivos.toString());


            StringBuilder restricoes = new StringBuilder();
            for (RestricoesEnum r : programa.getRestricoesTreino()) {
                restricoes.append(r.getNome()).append("\n");
            }
            programaJSON.setRestricoes(restricoes.toString());


            Map<Integer, AtividadeJSON> mapaTodasAtividades = new HashMap<Integer, AtividadeJSON>();

            if (!UteisValidacao.emptyString(ordenarFicha)) {
                try {
                    Ordenacao.ordenarLista(programa.getProgramaFichas(), ordenarFicha);
                } catch (Exception ignored) {
                }
            }

            if(!UteisValidacao.emptyNumber(programa.getOrigem())) {
                programaJSON.setOrigem(OrigemProgramaTreinoEnum.fromCodigo(programa.getOrigem()));
            }

            loopFichas:
            for(ProgramaTreinoFicha programaFicha : programa.getProgramaFichas()) {
                if(programaFicha.getFicha() != null){
                    programaJSON.getFichas().add(FichaJSON.obterJSON(treinoIndependente, programa, programaFicha, fichaUnica, urlBase, mapaTodasAtividades, agruparSeries, ctx, origemEnum));
                }
                if (fichaUnica != null) {
                    break loopFichas;
                }
            }
            if (!mapaTodasAtividades.isEmpty()) {
                programaJSON.setAtividades(new ArrayList(mapaTodasAtividades.values()));
            }
            mapaTodasAtividades = null;
        } catch (Exception e) {
            if (e.getClass() == NullPointerException.class) {
                e.printStackTrace();
            }
            throw new ServiceException(e);
        }
        return programaJSON;
    }

    public static ProgramaVersaoJSON preencherVersaoProgramaJSON(ProgramaTreino programa,
            final String ctx) throws ServiceException {
        ProgramaVersaoJSON programaVersaoJSON = new ProgramaVersaoJSON();
        try {
            programaVersaoJSON.setCodPrograma(programa.getCodigo());
            programaVersaoJSON.setVersao(programa.getVersao());
            for (ProgramaTreinoFicha progFicha : programa.getProgramaFichas()) {
                programaVersaoJSON.getFichas().add(new FichaVersaoJSON(
                        progFicha.getFicha().getCodigo(), progFicha.getVersao(), progFicha.getFicha().getUltimaExecucao()));
            }
        } catch (Exception e) {
            if (e.getClass() == NullPointerException.class) {
                e.printStackTrace();
            }
            throw new ServiceException(e);
        }
        return programaVersaoJSON;
    }

    public JSONObject chamadaZW(String ctx, String endpoint, Integer empresa, Integer pessoa) throws Exception {
        final String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWebInteg);
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(url + endpoint);
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("chave", ctx));
        params.add(new BasicNameValuePair("empresa", empresa.toString()));
        params.add(new BasicNameValuePair("pessoa", pessoa.toString()));
        httpPost.setEntity(new UrlEncodedFormEntity(params));
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return new JSONObject(body);
    }

    private void setAtual(ClienteSintetico cliente, String ctx, ModelMap mm, ConfiguracaoSistema configSeriesSet, String ordenarFicha, OrigemEnum origemEnum, Boolean filtraEmRevisaoProfessor) throws Exception {
        if (cliente != null) {
                ProgramaVersaoJSON progJSON = ps.obterVersaoUltimoProgramaVigente(
                        ctx, cliente.getCodigo(), Calendario.hoje());
                Integer quantidadeExecucoes = null;

            if (progJSON != null) {
                ProgramaTreino programa = ps.obterPorId(ctx, progJSON.getCodPrograma());
                programa = verificarAulasPrevistas(ctx, programa);

                if (programa.getGeradoPorIA()) {
                    boolean obrigatoriedadeAprovacaoProfessor = Optional.ofNullable(configService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_OBRIGATORIEDADE_APROVACAO_PROFESSOR))
                            .map(ConfiguracaoSistema::getValor)
                            .map(Boolean::parseBoolean)
                            .orElse(false);

                    int tempoAprovacao = Optional.ofNullable(configService.consultarPorTipo(ctx, ConfiguracoesEnum.TEMPO_APROVACAO_AUTOMATICA))
                            .map(ConfiguracaoSistema::getValor)
                            .filter(valor -> !valor.isEmpty())
                            .map(Integer::valueOf)
                            .orElse(0);

                    // Se a aprovação do professor for obrigatória, zera o tempo de aprovação automática
                    if (obrigatoriedadeAprovacaoProfessor) {
                        tempoAprovacao = 0;
                    }

                    // Correção 1: Formatação de datas com precisão de milissegundos
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
                    LocalDateTime dataCriacao = programa.getDataLancamento().toInstant()
                            .atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime dataAprovacaoAutomatica = dataCriacao.plusMinutes(tempoAprovacao);

                    long diferencaEmMinutos = ChronoUnit.MINUTES.between(programa.getDataLancamento().toInstant(), Calendario.hoje().toInstant());
                    boolean podeAprovarAutomaticamente = tempoAprovacao > 0 && diferencaEmMinutos >= tempoAprovacao;

                    Map<String, Object> iaStatus = new HashMap<>();
                    iaStatus.put("vaiSerAprovadoAutomaticamente", !obrigatoriedadeAprovacaoProfessor && podeAprovarAutomaticamente);
                    iaStatus.put("professorPodeValidar", programa.getEmRevisaoProfessor());
                    iaStatus.put("obrigatorioProfValidar", obrigatoriedadeAprovacaoProfessor);
                    iaStatus.put("dataCriacao", dataCriacao.format(formatter)); // Usando formatter
                    iaStatus.put("dataAprovacaoAutomatica", dataAprovacaoAutomatica.format(formatter)); // Usando formatter

                    if (!obrigatoriedadeAprovacaoProfessor && podeAprovarAutomaticamente && programa.getEmRevisaoProfessor()) {
                        ProgramaTreino programaTreino = programatreinoDao.obterPorId(ctx, programa.getCodigo());
                        if (programaTreino == null) {
                            throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
                        }

                        programaTreino.setEmRevisaoProfessor(false);
                        programatreinoDao.alterar(ctx, programaTreino);

                        // Correção 2: Remove as mensagens de erro e status nesse caso específico
                        // Não adiciona nada no mm

                    } else if (obrigatoriedadeAprovacaoProfessor && programa.getEmRevisaoProfessor()) {
                        mm.put("erro", "O treino está aguardando aprovação do professor.");
                        mm.put("iaStatus", iaStatus);
                    } else {
                        if (!obrigatoriedadeAprovacaoProfessor && programa.getEmRevisaoProfessor()) {
                            mm.put("erro", "Treino está sendo processado e será aprovado automaticamente.");
                            mm.put("iaStatus", iaStatus);
                        }
                    }
                }

                programaTreinoAndamento = ((ProgramaTreinoAndamentoDao) UtilContext.getBean(ProgramaTreinoAndamentoDao.class))
                        .findObjectByAttributes(ctx, new String[]{"programa.codigo"}, new Object[]{programa.getCodigo()}, "codigo");
                atualizarSeriesPorTipoAtividade(programa);
                    obterUltimaExecucaoFicha(ctx, programa);
                    ps.ordenarFichas(ctx, programa);
                    if (programa.getNrTreinosRealizados() != null
                            && programa.getTotalAulasPrevistas() != null
                            && programa.getNrTreinosRealizados() >= programa.getTotalAulasPrevistas()) {
                        ConfiguracaoSistema cfgEmitirExecutados = configService.consultarPorTipo(ctx, ConfiguracoesEnum.BLOQUEAR_IMPRESSAO_FICHA_APOS_TODAS_EXECUCOES);
                        if (cfgEmitirExecutados.getValorAsBoolean()) {
                            throw new ServiceException(getViewUtils().getMensagem("todasexecucoes"));
                        }
                    }
                    if (Calendario.menor(programa.getDataTerminoPrevisto(), Calendario.hoje())) {
                        ConfiguracaoSistema cfgEmitirVencidos = configService.consultarPorTipo(ctx, ConfiguracoesEnum.EMITIR_FICHA_APOS_VENCIMENTO_TREINO);
                        if (!cfgEmitirVencidos.getValorAsBoolean()) {
                            throw new ServiceException(getViewUtils().getMensagem("mobile.programatreinovencido"));
                        }
                    }
                    ConfiguracaoSistema conf = configService.consultarPorTipo(ctx, ConfiguracoesEnum.USAR_NOVA_MONTAGEM);
                    programa.setUsarNovaMontagemFicha(Boolean.valueOf(conf.getValor()));
                    ProgramaTreinoJSON programaJSON = preencherProgramaJSON(programa, ctx, null, configSeriesSet.getValorAsBoolean(), ordenarFicha, origemEnum);
                    ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
                    if(UteisValidacao.emptyNumber(programa.getTotalAulasPrevistas())){
                        try {
                            ps.calcularAulasPrevistas(programa);
                        }catch (Exception e){
                            Uteis.logar(e, ProgramaTreinoJSONControle.class);
                            programa.setTotalAulasPrevistas(0);
                        }
                    }
                    obterClienteMensagemAviso(ctx, programa, programaJSON);
                    verificaFichaDoDiaAtual(ctx, programaJSON, cliente.getMatricula());
                    mm.addAttribute("programa", programaJSON);
                    if (andamento == null) {
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(String.format("0/%s dias", programa.getTotalAulasPrevistas()), 0.0);
                        acompanhamentoSimplesJSON.setFrequenciaSemanal(cliente.getFrequenciaSemanal());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    } else {
                        quantidadeExecucoes = ps.obterQuantidadeExecucoesTreinoRealizados(ctx, programa.getCodigo());
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(
                                String.format("%s/%s dias", quantidadeExecucoes,
                                programa.getTotalAulasPrevistas()),
                                programaTreinoAndamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, programa.getTotalAulasPrevistas()));

                        acompanhamentoSimplesJSON.setFrequenciaSemanal(cliente.getFrequenciaSemanal());

                        acompanhamentoSimplesJSON.setNrTreinos(quantidadeExecucoes);

                        acompanhamentoSimplesJSON.setAulasPrevistas(programa.getTotalAulasPrevistas());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    }
                } else {
                    if (ps.clienteTemProgramaVencido(ctx, cliente)) {
                        List<ProgramaTreino> obterProgramasPorCliente = ps.obterProgramasPorCliente(ctx, cliente.getCodigo(), null, null, null, null);
                        ProgramaTreino programa = obterProgramasPorCliente == null || obterProgramasPorCliente.isEmpty() ?
                                new ProgramaTreino() : obterProgramasPorCliente.get(0);
                        if(filtraEmRevisaoProfessor && programa.getEmRevisaoProfessor()) {
                            throw new ServiceException(getViewUtils().getMensagem("mobile.programaemrevisao"));
                        }

                        if (Calendario.menor(programa.getDataTerminoPrevisto(), Calendario.hoje())) {
                            ConfiguracaoSistema cfgEmitirVencidos = configService.consultarPorTipo(ctx, ConfiguracoesEnum.EMITIR_FICHA_APOS_VENCIMENTO_TREINO);
                            if (!cfgEmitirVencidos.getValorAsBoolean()) {
                                throw new ServiceException(getViewUtils().getMensagem("mobile.programatreinovencido"));
                            }
                        }

                        ProgramaTreinoJSON programaJSON = preencherProgramaJSON(programa, ctx, null, configSeriesSet.getValorAsBoolean(), ordenarFicha, origemEnum);
                        ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
                        obterClienteMensagemAviso(ctx, programa, programaJSON);
                        mm.addAttribute("programa", programaJSON);
                        if (andamento == null) {
                            mm.addAttribute("acompanhamento", new AcompanhamentoSimplesJSON(String.format("0/%s dias",
                                    programa.getTotalAulasPrevistas()), 0.0));
                        } else {
                            AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(
                                    String.format("%s/%s dias", quantidadeExecucoes,
                                    programa.getTotalAulasPrevistas()),
                                    programaTreinoAndamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, programa.getTotalAulasPrevistas()));
                            acompanhamentoSimplesJSON.setNrTreinos(quantidadeExecucoes);
                            acompanhamentoSimplesJSON.setAulasPrevistas(programa.getTotalAulasPrevistas());
                            mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                        }
                        mm.addAttribute("frequenciaSemanal",cliente.getFrequenciaSemanal());
                    } else {
                        throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
                    }

                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
    }

    private ProgramaTreino verificarAulasPrevistas(String ctx, ProgramaTreino programa) throws Exception {
        if (programa.getDiasPorSemana() == null || programa.getTotalAulasPrevistas() == null) {
            List<ProgramaTreinoFicha> fichas = programaTreinoFichaDao.obterPorProgramaTreino(ctx, programa.getCodigo());
            programa.setDiasPorSemana(fichas.size());

            if (programa.getDataInicio() != null && programa.getDataTerminoPrevisto() != null) {
                long diferenca = programa.getDataTerminoPrevisto().getTime() - programa.getDataInicio().getTime();
                int semanas = (int) (diferenca / Uteis.calcularMillisPorSemana());
                programa.setTotalAulasPrevistas(semanas * programa.getDiasPorSemana());
            }

            programatreinoDao.update(ctx, programa);
        }

        return programa;
    }

    private void verificaFichaDoDiaAtual(String ctx, ProgramaTreinoJSON programaJSON, Integer matricula) throws ServiceException {
        FichaDoDiaDTO fichaDoDiaDTO = perfilAlunoService.fichaDoDia(matricula, false, ctx);
        if (fichaDoDiaDTO != null && fichaDoDiaDTO.getId() != null) {
            for(FichaJSON fichaJSON : programaJSON.getFichas()){
                if(fichaJSON.getCod().equals(fichaDoDiaDTO.getId())){
                    fichaJSON.setFichaDoDiaAtual(true);
                } else {
                    fichaJSON.setFichaDoDiaAtual(false);
                }
            }
        }
    }

    private void obterClienteMensagemAviso(String ctx, ProgramaTreino programa, ProgramaTreinoJSON programaJSON) throws ServiceException {
        ConfiguracaoSistema confMsgAviso = configService.consultarPorTipo(ctx, ConfiguracoesEnum.VISUALIZAR_MENSAGEM_AVISO);
        if(Boolean.parseBoolean(confMsgAviso.getValor())){
            if(programa != null && programa.getCliente() != null && programa.getCliente().getCodigo() != null && programa.getCliente().getCodigo() != 0) {
                programaJSON.setMensagemAviso(cs.obterClienteMensagem(ctx, programa.getCliente().getCodigo(), "AM"));
            }
        }
    }

    public void atualizarSeriesPorTipoAtividade(ProgramaTreino programa) {
        for (ProgramaTreinoFicha ptf : programa.getProgramaFichas()) {
            for (AtividadeFicha af : ptf.getFicha().getAtividades()) {
                if (af.getAtividade().getSeriesApenasDuracao() != null && af.getAtividade().getSeriesApenasDuracao()) {
                    for (Serie serie : af.getSeries()) {
                        serie.setRepeticao(0);
                        serie.setRepeticaoApp("0");
                        serie.setCarga(0.0);
                        serie.setCargaApp("0.0");
                        serie.setVelocidade(0.0);
                        serie.setDistancia(0);
                    }
                } else if (af.getAtividade().getTipo() == TipoAtividadeEnum.ANAEROBICO) {
                    for (Serie serie : af.getSeries()) {
                        serie.setVelocidade(0.0);
                        serie.setDistancia(0);
                        serie.setDuracao(0);
                    }
                } else if (af.getAtividade().getTipo() == TipoAtividadeEnum.AEROBICO) {
                    for (Serie serie : af.getSeries()) {
                        serie.setRepeticao(0);
                        serie.setRepeticaoApp("0");
                        serie.setCarga(0.0);
                        serie.setCargaApp("0.0");
                    }
                }
            }
        }
    }

    private void obterUltimaExecucaoFicha(String ctx, ProgramaTreino programa) throws ServiceException {
        for(ProgramaTreinoFicha f : programa.getProgramaFichas()){
            if(f.getFicha() != null){
                TreinoRealizado ultimo = getProgramaTreinoService().obterUltimoTreinoRealizadoFicha(ctx, f.getFicha().getCodigo());
                if(ultimo != null){
                    f.getFicha().setUltimaExecucao(ultimo.getDataInicio());
                }
            }
        }
    }

    @ApiOperation(value = "Obter programa de treino atual do aluno",
                  notes = "Consulta o programa de treino vigente de um aluno através de diferentes formas de identificação (username, matrícula ou CPF). " +
                          "Retorna os dados completos do programa incluindo fichas, atividades, acompanhamento e informações do professor responsável. " +
                          "Suporta diferentes origens de consulta e permite ordenação personalizada das fichas.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Validações:</strong> Verifica se o aluno possui parcelas vencidas (configurável) e se o programa não está em revisão pelo professor\n\n" +
                          "<strong>Retorna:</strong> Programa atual com fichas, acompanhamento de frequência e dados do professor",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Programa de treino atual consultado com sucesso", response = ExemploRespostaProgramaAtual.class)
    })
    @RequestMapping(value = "{ctx}/atual", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterAtual(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                        @PathVariable String ctx,
                        @ApiParam(value = "Nome de usuário do aluno para identificação", defaultValue = "joao.silva")
                        @RequestParam(required = false) String username,
                        @ApiParam(value = "Campo para ordenação das fichas do programa (ex: 'nome,asc' ou 'dataUltimaExecucao,desc')", defaultValue = "nome,asc")
                        @RequestParam(required = false) String ordenarFicha,
                        @ApiParam(value = "Número da matrícula do aluno (alternativa ao username)", defaultValue = "12345")
                        @RequestParam(required = false) String matricula,
                        @ApiParam(value = "CPF do aluno para identificação (alternativa ao username/matrícula)", defaultValue = "12345678901")
                        @RequestParam(required = false) String cpf,
                        @ApiParam(value = "Origem da consulta para controle de acesso e formatação específica", defaultValue = "WEB")
                        @RequestParam(required = false) String origem,
                        @ApiParam(value = "Indica se um professor está consultando o programa (afeta filtros de revisão)", defaultValue = "false")
                        @RequestParam (required = false, defaultValue = "false") Boolean professorEstaConsultando,
                        HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("obterAtual");
            OrigemEnum origemEnum = null;
            if (origem != null){
                origemEnum = OrigemEnum.valueOf(origem);
            }
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            Usuario usuario = null;
            if(username != null && username.startsWith("VIP_")){
                JSONObject programaEmRede = clienteRedeEmpresaService.programaEmRede(ctx, username, request);
                if(programaEmRede != null && programaEmRede.has("programa")){
                    mm.addAttribute("programa", programaEmRede.getJSONObject("programa").toMap());
                    if(programaEmRede.has("acompanhamento")){
                        mm.addAttribute("acompanhamento", programaEmRede.getJSONObject("acompanhamento").toMap());
                    }
                    return mm;
                }
            }
            if(username != null && username.startsWith("C0LAB_")){
                Integer codigoColaborador = Integer.valueOf(username.split("\\_")[1].trim());
                prescricaoService.setAtualColaborador(codigoColaborador, ctx, mm, configSeriesSet, ordenarFicha, getViewUtils(), origemEnum);
                return mm;
            }
            if(!UteisValidacao.emptyString(cpf)){
                usuario = us.consultarPorCpf(ctx, cpf);
            } else if(UteisValidacao.emptyString(matricula)){
                usuario = us.obterPorAtributo(ctx, "username", username, true);
            } else {
                usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }

            ClienteSintetico cliente = null;
            if(usuario == null){
                try {
                    cliente = cs.consultarPorMatricula(ctx, matricula == null ? username : matricula);
                }catch (Exception e){
                    e.printStackTrace();
                }
            } else {
                cliente = usuario.getCliente();
            }
            ConfiguracaoSistema configParcelaVencida = configService.consultarPorTipo(ctx, ConfiguracoesEnum.PROIBIR_BUSCAR_PROGRAMA_PARCELA_VENCIDA);
            if(!independente(ctx) && configParcelaVencida.getValorAsBoolean() &&
                    chamadaZW(ctx, "/prest/treino/aluno-parcela-vencida", cliente.getEmpresa(),
                            cliente.getCodigoPessoa()).getBoolean("parcelaVencida")){
                throw new ServiceException(getViewUtils().getMensagem("mobile.programaalunoparcelavencido"));
            }
            Boolean filtraEmRevisaoProfessor = !professorEstaConsultando;
            setAtual(cliente, ctx, mm, configSeriesSet, ordenarFicha, origemEnum, filtraEmRevisaoProfessor);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            /*Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);*/
            Uteis.logarDebug(ex.getMessage());
        }  catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Obter programa de treino atual por matrícula",
                  notes = "Consulta o programa de treino vigente de um aluno através do número da matrícula. " +
                          "Versão simplificada do endpoint '/atual' focada especificamente na identificação por matrícula. " +
                          "Retorna os dados completos do programa incluindo fichas, atividades e acompanhamento.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Validações:</strong> Verifica se o programa não está em revisão pelo professor\n\n" +
                          "<strong>Retorna:</strong> Programa atual com fichas, acompanhamento de frequência e dados do professor",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Programa de treino atual consultado com sucesso", response = ExemploRespostaProgramaAtual.class)
    })
    @RequestMapping(value = "{ctx}/atualPorMatricula", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualPorMatricula(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                               @PathVariable String ctx,
                               @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
                               @RequestParam Integer matricula,
                               @ApiParam(value = "Campo para ordenação das fichas do programa (ex: 'nome,asc' ou 'dataUltimaExecucao,desc')", defaultValue = "nome,asc")
                               @RequestParam(required = false) String ordenarFicha,
                               @ApiParam(value = "Origem da consulta para controle de acesso e formatação específica", defaultValue = "WEB")
                               @RequestParam(required = false) String origemEnum,
                               @ApiParam(value = "Indica se um professor está consultando o programa (afeta filtros de revisão)", defaultValue = "false")
                               @RequestParam (required = false, defaultValue = "false") Boolean professorEstaConsultando) {
        ModelMap mm = new ModelMap();
        try {
            acao("atualPorMatricula");
            Usuario usuario = us.consultarPorMatricula(ctx, matricula);
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            OrigemEnum origem = null;
            if (origemEnum != null){
                origem = OrigemEnum.valueOf(origemEnum);
            }
            Boolean filtraEmRevisaoProfessor = !professorEstaConsultando;
            setAtual(usuario == null ? null : usuario.getCliente(), ctx, mm, configSeriesSet, ordenarFicha, origem, filtraEmRevisaoProfessor);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Obter programa de treino atual por matrícula (criptografado)",
                  notes = "Consulta o programa de treino vigente de um aluno através de dados criptografados. " +
                          "Endpoint de segurança que recebe parâmetros criptografados no corpo da requisição e retorna resposta criptografada. " +
                          "Utilizado para comunicação segura entre sistemas ou aplicações que requerem proteção adicional dos dados.\n\n" +
                          "<strong>Segurança:</strong> Dados de entrada e saída são criptografados\n\n" +
                          "<strong>Formato:</strong> Recebe JSON criptografado contendo matrícula, ordenação e configurações\n\n" +
                          "<strong>Retorna:</strong> Programa atual criptografado com fichas, acompanhamento e dados do professor",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Programa de treino atual consultado com sucesso (dados criptografados)", response = ExemploRespostaProgramaAtualCriptografado.class)
    })
    @RequestMapping(value = "{ctx}/ehIYzrr2mfO10T8rw4zqaSmm9IuurDBqwOOSQj2C8os", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualPorMatriculaCrypt(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                                    @PathVariable String ctx,
                                    @ApiParam(value = "Dados criptografados contendo matrícula, ordenação e configurações do programa", required = true)
                                    @RequestBody String content) {
        ModelMap mm = new ModelMap();
        try {
            acao("atualPorMatriculaCrypt");
            JSONObject o = new JSONObject(Uteis.decryptUserData(content));
            Integer matricula = o.optInt("matricula");
            String ordenarFicha = o.optString("ordenarFicha", null);
            String origemEnum = o.optString("origemEnum", null);
            Boolean professorEstaConsultando = o.optBoolean("professorEstaConsultando", false);

            Usuario usuario = us.consultarPorMatricula(ctx, matricula);
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            OrigemEnum origem = null;
            if (origemEnum != null && !origemEnum.isEmpty()){
                origem = OrigemEnum.valueOf(origemEnum);
            }
            Boolean filtraEmRevisaoProfessor = !professorEstaConsultando;
            setAtual(usuario == null ? null : usuario.getCliente(), ctx, mm, configSeriesSet, ordenarFicha, origem, filtraEmRevisaoProfessor);

            // Criptografar toda a resposta de sucesso
            JSONObject responseJson = new JSONObject();
            for (String key : mm.keySet()) {
                responseJson.put(key, mm.get(key));
            }
            mm.clear();
            mm.addAttribute(RETURN, Uteis.encryptUserData(responseJson.toString()));
        } catch (Exception ex) {
            String erro = ex.getMessage();
            try {
                erro = Uteis.encryptUserData(ex.getMessage());
            } catch (Exception ignored) {}
            mm.addAttribute(STATUS_ERRO, erro);
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Obter versão do programa de treino atual",
                  notes = "Consulta apenas as informações de versão do programa de treino vigente de um aluno. " +
                          "Retorna dados resumidos incluindo código do programa, versões das fichas e acompanhamento básico. " +
                          "Útil para verificação de sincronização e controle de versões sem carregar dados completos.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Performance:</strong> Endpoint otimizado que retorna apenas dados essenciais de versão\n\n" +
                          "<strong>Retorna:</strong> Informações de versão do programa e acompanhamento básico",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Versão do programa de treino consultada com sucesso", response = ExemploRespostaVersaoProgramaAtual.class)
    })
    @RequestMapping(value = "{ctx}/atual/versao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap obterVersaoProgramaAtual(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                                      @PathVariable String ctx,
                                      @ApiParam(value = "Nome de usuário do aluno para identificação", required = true, defaultValue = "maria.santos")
                                      @RequestParam String username) {
        ModelMap mm = new ModelMap();
        try {
            acao("oberVersaoProgramaAtual");
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && usuario.getCliente() != null) {
                ProgramaVersaoJSON programaJSON = ps.obterVersaoUltimoProgramaVigente(
                        ctx, usuario.getCliente().getCodigo(), Calendario.hoje());
                if (programaJSON != null) {
                    ProgramaTreino programa = ps.obterPorId(ctx, programaJSON.getCodPrograma());
                    ps.ordenarFichas(ctx, programa);
                    programaJSON = preencherVersaoProgramaJSON(programa, ctx);
                    mm.addAttribute("programa", programaJSON);

                    ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
                    if (andamento == null) {
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(String.format("0/%s dias", programa.getTotalAulasPrevistas()), 0.0);
                        acompanhamentoSimplesJSON.setFrequenciaSemanal(usuario.getCliente().getFrequenciaSemanal());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    } else {
                        AcompanhamentoSimplesJSON acompanhamentoSimplesJSON = new AcompanhamentoSimplesJSON(
                                String.format("%s/%s dias", andamento.getNrTreinos(),
                                        programa.getTotalAulasPrevistas()), andamento.getPercentualFrequenciaAteHoje());
                        acompanhamentoSimplesJSON.setFrequenciaSemanal(usuario.getCliente().getFrequenciaSemanal());
                        mm.addAttribute("acompanhamento", acompanhamentoSimplesJSON);
                    }
                } else {
                    throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Submeter execução de série de treino",
                  notes = "Registra a execução de uma série específica de um exercício durante o treino. " +
                          "Permite registrar valores como carga, repetições, tempo ou outros parâmetros específicos da atividade. " +
                          "O sistema verifica se já existe uma série realizada no dia e atualiza ou cria um novo registro conforme necessário.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Validações:</strong> Verifica se a série já foi executada no dia atual\n\n" +
                          "<strong>Funcionalidade:</strong> Atualiza automaticamente os dados da ficha baseado na execução",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Série de treino submetida com sucesso", response = ExemploRespostaSubmitSerie.class)
    })
    @RequestMapping(value = "{ctx}/submitserie", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitSerie(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                         @PathVariable String ctx,
                         @ApiParam(value = "Nome de usuário do aluno que está executando a série", required = true, defaultValue = "pedro.costa")
                         @RequestParam String username,
                         @ApiParam(value = "Identificador único do programa de treino", required = true, defaultValue = "1001")
                         @RequestParam String idPrograma,
                         @ApiParam(value = "Identificador único da ficha de treino", required = true, defaultValue = "2001")
                         @RequestParam String idFicha,
                         @ApiParam(value = "Data e hora de início da execução da série", required = true, defaultValue = "2024-07-24 14:30:00")
                         @RequestParam String inicio,
                         @ApiParam(value = "Data e hora de término da execução da série", required = true, defaultValue = "2024-07-24 14:32:00")
                         @RequestParam String fim,
                         @ApiParam(value = "Identificador único da atividade/exercício", required = true, defaultValue = "3001")
                         @RequestParam String idAtividade,
                         @ApiParam(value = "Primeiro valor da série (ex: carga, repetições, tempo)", required = true, defaultValue = "50")
                         @RequestParam String valor1,
                         @ApiParam(value = "Segundo valor da série (ex: repetições, velocidade, distância)", required = true, defaultValue = "12")
                         @RequestParam String valor2,
                         @ApiParam(value = "Identificador único da série específica", required = true, defaultValue = "4001")
                         @RequestParam Integer idSerie,
                         @ApiParam(value = "Flag para forçar atualização dos dados da ficha", defaultValue = "true")
                         @RequestParam(required = false) String atualizar,
                         @ApiParam(value = "Número da matrícula do aluno (alternativa ao username)", defaultValue = "12345")
                         @RequestParam(required = false) String matricula
                         ) {
        ModelMap mm = new ModelMap();
        try {
//            super.init(ctx, token);
//            Uteis.logar(null, "teste  - submit serie - " + username);
            Usuario usuario;
            if(UteisValidacao.emptyString(matricula)){
                usuario = us.obterPorAtributo(ctx, "username", username);
            } else {
                usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }
            if (usuario != null) {
                acao("submitSerie.inserirSerieRealizada");
                ConfiguracaoSistema cfgForcarCarga = configService.consultarPorTipo(ctx, ConfiguracoesEnum.MOBILE_SEMPRE_ATUALIZAR_CARGA_FICHA);
                SerieRealizada serieRealExistente = ps.obterSerieRealizadaHoje(ctx, idSerie);
                if (serieRealExistente == null) {
                    ProgramaTreinoFicha programaTreinoFicha = ps.obterProgramaTreinoFicha(ctx,
                            Integer.valueOf(idPrograma), Integer.valueOf(idFicha));
                    Serie s = ss.obterPorId(ctx, Integer.valueOf(idSerie));
                    SerieRealizada sr = ps.inserirTreinoRealizado(ctx, usuario.getUserName(),
                            programaTreinoFicha.getPrograma(),
                            idFicha, inicio, fim,
                            idAtividade, s, s.getAtividadeFicha(), OrigemExecucaoEnum.SMARTPHONE, programaTreinoFicha);
                    sr.setForcarAtualizacaoFicha(cfgForcarCarga.getValorAsBoolean());
                    sr.verificarSeDeveAtualizarDadosNaFicha(valor1, valor2);
                    if (sr.getSerieAtualizar() != null) {
                        ps.atualizarSerieComBaseNaSerieRealizada(ctx, sr);
                    }
                } else {
                    serieRealExistente.setForcarAtualizacaoFicha(cfgForcarCarga.getValorAsBoolean());
                    serieRealExistente.verificarSeDeveAtualizarDadosNaFicha(valor1, valor2);
                    ps.atualizarSerieRealizada(ctx, idSerie, valor1, valor2, null,
                            cfgForcarCarga.getValorAsBoolean());
                }
                mm.addAttribute(STATUS, STATUS_SUCESSO);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.programanaoencontrado"));
            }

        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Submete comentário e avaliação de treino via aplicativo",
                  notes = "Permite que o aluno registre sua avaliação (nota), tempo de execução e comentários sobre um treino específico através do aplicativo móvel. " +
                          "O sistema atualiza automaticamente o acompanhamento do programa de treino com os dados fornecidos.",
                  tags = "Gestão de Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Comentário de treino submetido com sucesso", response = ExemploRespostaSubmitTreinoCommentAPP.class)
    })
    @RequestMapping(value = "{ctx}/submittreinoCommentAPP", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submittreinoCommentAPP(@PathVariable String ctx,
                                    @ApiParam(value = "Nome de usuário do aluno que está submetendo o comentário", defaultValue = "joao.silva")
                                    @RequestParam String username,
                                    @ApiParam(value = "Matrícula do aluno (opcional). Se não informada, será utilizado o username para identificação", defaultValue = "12345")
                                    @RequestParam(required = false) String matricula,
                                    @ApiParam(value = "Identificador único do programa de treino", defaultValue = "1001")
                                    @RequestParam String idPrograma,
                                    @ApiParam(value = "Identificador único da ficha de treino executada", defaultValue = "2001")
                                    @RequestParam Integer idFicha,
                                    @ApiParam(value = "Data e hora de execução do treino no formato 'yyyy-MM-dd HH:mm:ss'", defaultValue = "2024-07-24 14:30:00")
                                    @RequestParam String dia,
                                    @ApiParam(value = "Nota de avaliação do treino atribuída pelo aluno (escala de 1 a 10)", defaultValue = "8")
                                    @RequestParam String nota,
                                    @ApiParam(value = "Tempo total de execução do treino em minutos", defaultValue = "45")
                                    @RequestParam Integer tempo,
                                    @ApiParam(value = "Comentários adicionais sobre o treino (opcional)", defaultValue = "Treino muito bom, senti bastante os músculos trabalhando")
                                    @RequestParam(required = false) String comentario,
                                    HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("submittreinoCommentAPP");
            ProgramaTreino programa = ps.obterPorId(ctx, Integer.valueOf(idPrograma));
            try {
                AcompanhamentoSimplesJSON acompFicha = preencherAndamento(ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha.toString(), Calendario.hoje(), true,"app", null, null, request), ctx, programa);
            }catch (Exception ignored) { }

            Usuario usuario = null;
            if(!UteisValidacao.emptyString(matricula)){
                usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
            }

            AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, usuario == null ? username : usuario.getUserName(), idPrograma,
                    idFicha, 0, nota, tempo, comentario, null), ctx, programa);
            try {
                Date dataExecucao = Uteis.getDate(dia, "yyyy-MM-dd HH:mm:ss");
                fichaService.atualizarUltimaExecucao(ctx, dataExecucao, idFicha);
            } catch (Exception ignored) {
            }
            mm.addAttribute(RETURN, acomp);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Submete comentário e avaliação de treino",
                  notes = "Permite que o aluno registre sua avaliação (nota), tempo de execução e comentários sobre um treino específico. " +
                          "O sistema atualiza automaticamente o acompanhamento do programa de treino com os dados fornecidos.",
                  tags = "Gestão de Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Comentário de treino submetido com sucesso", response = ExemploRespostaSubmitTreinoComment.class)
    })
    @RequestMapping(value = "{ctx}/submittreinoComment", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitTreinoComment(@PathVariable String ctx,
                          @ApiParam(value = "Nome de usuário do aluno que está submetendo o comentário", defaultValue = "maria.santos")
                          @RequestParam String username,
                          @ApiParam(value = "Identificador único do programa de treino", defaultValue = "1002")
                          @RequestParam String idPrograma,
                          @ApiParam(value = "Identificador único da ficha de treino executada", defaultValue = "2002")
                          @RequestParam Integer idFicha,
                          @ApiParam(value = "Dia de execução do treino (número inteiro representando o dia)", defaultValue = "15")
                          @RequestParam Integer dia,
                          @ApiParam(value = "Nota de avaliação do treino atribuída pelo aluno (escala de 1 a 10)", defaultValue = "9")
                          @RequestParam String nota,
                          @ApiParam(value = "Tempo total de execução do treino em minutos", defaultValue = "50")
                          @RequestParam Integer tempo,
                          @ApiParam(value = "Comentários adicionais sobre o treino (opcional)", defaultValue = "Excelente treino, consegui completar todas as séries")
                          @RequestParam(required = false) String comentario,
                          HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("submitTreinoComment");
            try {
                AcompanhamentoSimplesJSON acompFicha = preencherAndamento(ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha.toString(), Calendario.hoje(), true,
                        "app", null, null, request), ctx, null);
            }catch (Exception ignored) { }
            AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx,username,idPrograma,
                    idFicha, dia,nota,tempo,comentario, null), ctx, null);
            mm.addAttribute(RETURN, acomp);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Submete comentário e avaliação de treino via aplicativo (versão 2)",
                  notes = "Versão aprimorada do endpoint para submissão de comentários de treino via aplicativo móvel. " +
                          "Inclui validações adicionais como verificação de data de execução não superior à data atual e " +
                          "validação de que a data de execução não seja anterior ao início do programa de treino. " +
                          "O sistema atualiza automaticamente o acompanhamento do programa com os dados fornecidos.",
                  tags = "Gestão de Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Comentário de treino submetido com sucesso", response = ExemploRespostaSubmitTreinoCommentAPPV2.class)
    })
    @RequestMapping(value = "{ctx}/v2/submittreinoCommentAPP", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submittreinoCommentAPPV2(@PathVariable String ctx,
                                      @ApiParam(value = "Nome de usuário do aluno que está submetendo o comentário", defaultValue = "carlos.oliveira")
                                      @RequestParam String username,
                                      @ApiParam(value = "Matrícula do aluno (opcional). Se não informada, será utilizado o username para identificação", defaultValue = "54321")
                                      @RequestParam(required = false) String matricula,
                                      @ApiParam(value = "Identificador único do programa de treino", defaultValue = "1003")
                                      @RequestParam String idPrograma,
                                      @ApiParam(value = "Identificador único da ficha de treino executada", defaultValue = "2003")
                                      @RequestParam Integer idFicha,
                                      @ApiParam(value = "Data e hora de execução do treino no formato 'yyyy-MM-dd HH:mm:ss'. Deve ser posterior à data de início do programa e não superior à data atual", defaultValue = "2024-07-24 16:00:00")
                                      @RequestParam String dia,
                                      @ApiParam(value = "Nota de avaliação do treino atribuída pelo aluno (escala de 1 a 10)", defaultValue = "7")
                                      @RequestParam String nota,
                                      @ApiParam(value = "Tempo total de execução do treino em minutos", defaultValue = "40")
                                      @RequestParam Integer tempo,
                                      @ApiParam(value = "Comentários adicionais sobre o treino (opcional)", defaultValue = "Treino desafiador, preciso melhorar a técnica nos exercícios")
                                      @RequestParam(required = false) String comentario,
                                      HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            acao("submittreinoCommentAPP");
            Date dataExecucao = Uteis.getDate(dia, "yyyy-MM-dd HH:mm:ss");

            if (dataExecucao.after(Calendario.hoje())) {
                throw new ServiceException("Data de execução não pode ser maior que a data atual");
            }

            ProgramaTreino programa = ps.obterPorId(ctx, Integer.valueOf(idPrograma));
            if (programa == null) {
                throw new ServiceException(ProgramaTreinoExcecoes.PROGRAMA_TREINO_NAO_ENCONTRADO);
            }
            //se o dia da datainicio do programa for anterior a data de execução do treino, então não deixa executar
            if (programa.getDataInicio().after(dataExecucao)) {
                throw new ServiceException("Data de execução do treino não pode ser menor que a data de início do programa");
            }

            if (Calendario.dataNoMesmoDiaMes(dataExecucao, Calendario.hoje())) {
                try {
                    AcompanhamentoSimplesJSON acompFicha = preencherAndamento(ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha.toString(), Calendario.hoje(), true,
                            "app", null, null, request), ctx, null);
                } catch (Exception ignored) {
                }
                AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, username, idPrograma,
                        idFicha, 0, nota, tempo, comentario, null), ctx, null);
                mm.addAttribute(RETURN, acomp);
            } else {
                System.out.println("Programa: " + programa.getNome());
                Usuario usuario = null;
                if (!UteisValidacao.emptyString(matricula)) {
                    usuario = us.consultarPorMatricula(ctx, Integer.valueOf(matricula));
                }

                System.out.println("Usuario: " + usuario);
                AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, usuario == null ? username : usuario.getUserName(), idPrograma,
                        idFicha, 0, nota, tempo, comentario, dataExecucao), ctx, programa);
                try {
                    fichaService.atualizarUltimaExecucao(ctx, dataExecucao, idFicha);
                } catch (Exception ignored) {
                }
                mm.addAttribute(RETURN, acomp);
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }
    @ApiOperation(value = "Submeter treino realizado completo",
                  notes = "Registra a conclusão de um treino completo através de um objeto JSON contendo todos os dados da execução. " +
                          "Permite registrar informações como nota de avaliação, tempo total de execução e comentários sobre o treino. " +
                          "Atualiza automaticamente o acompanhamento do programa de treino com os dados fornecidos.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Formato:</strong> Recebe objeto JSON com dados completos do treino realizado\n\n" +
                          "<strong>Retorna:</strong> Acompanhamento atualizado com frequência e assiduidade",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Treino realizado submetido com sucesso", response = ExemploRespostaSubmitTreino.class)
    })
    @RequestMapping(value = "{ctx}/submittreino", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap submitTreino(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                          @PathVariable String ctx,
                          @ApiParam(value = "Dados completos do treino realizado incluindo avaliação, tempo e comentários", required = true)
                          @RequestBody final TreinoRealizadoJSON treinoRealizado) {
        ModelMap mm = new ModelMap();
        try {
            Uteis.logar(null, treinoRealizado.toJSON());
            acao("submittreino");
            AcompanhamentoSimplesJSON acomp = preencherAndamento(ps.concluirTreinoEmAndamento(ctx, treinoRealizado.getUsername(), treinoRealizado.getIdPrograma(),
                    treinoRealizado.getIdFicha(), treinoRealizado.getDia(), treinoRealizado.getNota(), treinoRealizado.getTempo(),"", null), ctx, null);
            mm.addAttribute(RETURN, acomp);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Executar ficha de treino",
                  notes = "Processa a execução de uma ficha de treino específica, registrando todas as séries e atividades realizadas. " +
                          "Controla o processo de execução para evitar processamento simultâneo da mesma ficha. " +
                          "Suporta execução em rede de empresas e diferentes unidades de execução.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Controle:</strong> Previne execução simultânea da mesma ficha\n\n" +
                          "<strong>Retorna:</strong> Acompanhamento atualizado após execução da ficha",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Ficha de treino executada com sucesso", response = ExemploRespostaExecutarFicha.class)
    })
    @RequestMapping(value = "{ctx}/executarFicha", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap executarFichaRetorno(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                                  @PathVariable String ctx,
                                  @ApiParam(value = "Nome de usuário do aluno que está executando a ficha", defaultValue = "ana.silva")
                                  @RequestParam(required = false) final String username,
                                  @ApiParam(value = "Identificador da unidade onde a execução está ocorrendo", defaultValue = "UNIDADE_01")
                                  @RequestParam(required = false) final String unidadeExecucao,
                                  @ApiParam(value = "Chave única para controle de execução simultânea", defaultValue = "EXEC_123456")
                                  @RequestParam(required = false) final String chaveExecucao,
                                  @ApiParam(value = "Data e hora atual da execução", required = true, defaultValue = "2024-07-24 15:00:00")
                                  final String dataAtual,
                                  @ApiParam(value = "Identificador único do programa de treino", required = true, defaultValue = "1001")
                                  final String idPrograma,
                                  @ApiParam(value = "Identificador único da ficha de treino", required = true, defaultValue = "2001")
                                  final String idFicha,
                                  @ApiParam(value = "Indica se a ficha foi totalmente concluída", required = true, defaultValue = "true")
                                  boolean fichaConcluida,
                                  HttpServletRequest request) {
        ModelMap mm = new ModelMap();
        try {
            if(username != null && username.startsWith("VIP_")){
                JSONObject execucaoFichaEmRede = clienteRedeEmpresaService.executarFichaEmRede(ctx,
                        dataAtual, idPrograma, idFicha,  fichaConcluida, username, request);
                if(execucaoFichaEmRede != null && execucaoFichaEmRede.has(RETURN)){
                    mm.addAttribute(RETURN, execucaoFichaEmRede.getJSONObject(RETURN).toMap());
                    return mm;
                }
            }
            if (!mapaExec.containsKey(ctx)) {
                mapaExec.put(ctx, idPrograma);
                acao("executarFichaRetorno");
                AcompanhamentoSimplesJSON acomp = preencherAndamento(
                        ps.baixarTodasSeriesDaFicha(ctx, idPrograma, idFicha,
                                Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual), fichaConcluida,
                                "retiraFicha", chaveExecucao, unidadeExecucao, request), ctx, null);
                mm.addAttribute(RETURN, acomp);
            } else {
                final String msg = String.format("Já possui um programa %s sendo processado para empresa %s. Por favor aguarde...", idPrograma, ctx);
                mm.put(STATUS_ERRO, msg);
                Uteis.logar(null, msg);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            mapaExec.remove(ctx);
        }
        return mm;
    }

    @ApiOperation(value = "Atualizar número de treinos realizados",
                  notes = "Atualiza o contador de treinos realizados pelo aluno no programa atual. " +
                          "Recalcula automaticamente o andamento e a frequência do programa de treino. " +
                          "Útil para sincronização de dados e correção de inconsistências no acompanhamento.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Funcionalidade:</strong> Recalcula automaticamente estatísticas de acompanhamento\n\n" +
                          "<strong>Retorna:</strong> Dados atualizados do andamento do programa",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Número de treinos atualizado com sucesso", response = ExemploRespostaAtualizarNrTreinos.class)
    })
    @RequestMapping(value = "{ctx}/atualizarNrTreinos", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap atualizarNrTreinos(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                                @PathVariable String ctx,
                                @ApiParam(value = "Número da matrícula do aluno para atualização", required = true, defaultValue = "12345")
                                @RequestParam String matricula) {
        ModelMap mm = new ModelMap();
        try {
            acao("atualizarNrTreinos");
            mm.addAttribute(RETURN, ps.atualizarAndamentoAluno(ctx, matricula));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }
    @ApiOperation(value = "Marcar treino realizado em ficha avulsa",
                  notes = "Registra a execução de uma ficha de treino avulsa (não vinculada a programa específico) para um aluno. " +
                          "Permite marcar treinos realizados fora do contexto de um programa estruturado. " +
                          "Útil para registrar atividades extras ou treinos livres realizados pelo aluno.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Funcionalidade:</strong> Registra execução de ficha independente de programa\n\n" +
                          "<strong>Retorna:</strong> Confirmação do registro da execução",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Treino em ficha avulsa marcado com sucesso", response = ExemploRespostaSelecaoFicha.class)
    })
    @RequestMapping(value = "{ctx}/selecaoFicha", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap marcarTreinoFichaAvulsa(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                                     @PathVariable String ctx,
                                     @ApiParam(value = "Identificador único da ficha de treino", required = true, defaultValue = "2001")
                                     @RequestParam final Integer idFicha,
                                     @ApiParam(value = "Nome de usuário do aluno que realizou o treino", required = true, defaultValue = "carlos.santos")
                                     @RequestParam final String usuario,
                                     @ApiParam(value = "Data de execução do treino no formato dd/MM/yyyy", required = true, defaultValue = "24/07/2024")
                                     @RequestParam final String data) {
        ModelMap mm = new ModelMap();
        try {
            acao("selecaoFicha");
            mm.addAttribute(RETURN, ps.marcarTreinoRealizadoFichaAvulsa(ctx, idFicha,usuario, data));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Consultar objetivos predefinidos",
                  notes = "Retorna a lista completa de objetivos predefinidos disponíveis no sistema. " +
                          "Estes objetivos podem ser utilizados na criação de programas de treino personalizados. " +
                          "Cada objetivo possui um código único e uma descrição que orienta o tipo de treino.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Uso:</strong> Utilizado para seleção de objetivos durante criação de programas\n\n" +
                          "<strong>Retorna:</strong> Lista completa de objetivos predefinidos disponíveis",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Objetivos predefinidos consultados com sucesso", response = ExemploRespostaObjetivosPredef.class)
    })
    @RequestMapping(value = "{ctx}/objPredef", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap objPredef(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                       @PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            acao("objPredef");
            mm.addAttribute(RETURN, ops.obterTodos(ctx));
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @ApiOperation(value = "Sincronizar objetivos predefinidos",
                  notes = "Retorna objetivos predefinidos que foram modificados após uma data específica. " +
                          "Utilizado para sincronização de dados entre sistemas ou aplicações móveis. " +
                          "Permite manter os dados atualizados sem necessidade de carregar toda a lista.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no contexto\n\n" +
                          "<strong>Sincronização:</strong> Retorna apenas registros modificados após a data informada\n\n" +
                          "<strong>Retorna:</strong> Lista de objetivos predefinidos modificados",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Sincronização de objetivos realizada com sucesso", response = ExemploRespostaObjetivosPredefinidosSync.class)
    })
    @RequestMapping(value = "{ctx}/objPredef/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap objPredef_sync(@ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
                            @PathVariable String ctx,
                            @ApiParam(value = "Data e hora base para sincronização no formato yyyy-MM-dd HH:mm:ss", required = true, defaultValue = "2024-01-01 00:00:00")
                            @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        try {
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            List<ObjetivoPredefinido> objetivos = syncs.obterLista(ctx,
                    TipoClassSincronizarEnum.ObjetivoPredefinido, dataBase);
            mm.addAttribute(RETURN, objetivos);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    private AcompanhamentoSimplesJSON obterAndamento(final String ctx, ProgramaTreino programa) {
        try {
            ProgramaTreinoAndamento andamento = ps.obterAndamento(ctx, programa);
            return preencherAndamento(andamento, ctx, null);
        } catch (ServiceException ex) {
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return new AcompanhamentoSimplesJSON("0/0 dias", 0.0);
    }

    private AcompanhamentoSimplesJSON preencherAndamento(ProgramaTreinoAndamento andamento, String ctx, ProgramaTreino programa) {
        if (andamento != null) {
            Integer quantidadeExecucoes = null;
            try {
                Integer quantidadeExecucoesTreinoRealizados = ps.obterQuantidadeExecucoesTreinoRealizados(ctx, andamento.getPrograma().getCodigo());
                quantidadeExecucoes = quantidadeExecucoesTreinoRealizados == null ?
                        0 : quantidadeExecucoesTreinoRealizados;
            } catch (ServiceException e) {
                e.printStackTrace();
            }

            return new AcompanhamentoSimplesJSON(quantidadeExecucoes + "/"
                    + andamento.getPrograma().getTotalAulasPrevistas() + " dias",
                    andamento.getPercentualExecucoesFrequencia(quantidadeExecucoes, andamento.getPrograma().getTotalAulasPrevistas()));
        }
        return new AcompanhamentoSimplesJSON("0/0 dias", 0.0);
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/ultimos", method = RequestMethod.POST)
    public ModelMap obterUltimosProgramas(@PathVariable final String contexto,
                                          @RequestParam final String username,
                                          @RequestParam final Integer codigoCliente) {

        ModelMap modelMap = new ModelMap();
        List<ProgramaTreino> programasTreino = null;

        try {
            acao("ultimos");

            final Usuario usuario = us.obterPorAtributo(contexto, "username", username);
            if (usuario == null || TipoUsuarioEnum.ALUNO.equals(usuario.getTipo())) {
                String mensagemErro = getViewUtils().getMensagem("mobile.usuarioinvalido");
                modelMap.addAttribute(STATUS_ERRO, mensagemErro);
                Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, mensagemErro);
                return modelMap;
            }

            programasTreino = ps.obterProgramasPorCliente(
                    contexto, codigoCliente, null, null, null, 3);

            ps.refresh(contexto, programasTreino);

        } catch (ServiceException ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return modelMap;
        }

        List<ProgramaWriteJSON> programasWriteJSON = new ArrayList<ProgramaWriteJSON>();
        for (ProgramaTreino programaTreino : programasTreino) {
            ProgramaWriteJSON programaWriteJSON = new ProgramaWriteJSON(programaTreino);
            programaWriteJSON.preencherObjetivosPrograma(programaTreino);
            programaWriteJSON.preencherProgramaTreinoFichas(programaTreino);
            programaWriteJSON.setUsername(username);
            programaWriteJSON.setFrequencia(obterAndamento(contexto, programaTreino));
            programasWriteJSON.add(programaWriteJSON);
        }

        return modelMap.addAttribute(RETURN, programasWriteJSON);
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/ultimosZW", method = RequestMethod.POST)
    public ModelMap obterUltimosProgramas(@PathVariable final String contexto,
                                          @RequestParam final Integer codigoCliente) {

        ModelMap modelMap = new ModelMap();
        List<ProgramaTreino> programasTreino = null;
        try {
            programasTreino = ps.obterProgramasPorClienteZW(
                    contexto, codigoCliente, null, null, null, 1);
        } catch (ServiceException ex) {
            modelMap.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return modelMap;
        }

        for (ProgramaTreino programaTreino : programasTreino) {
            ProgramaWriteJSON programaWriteJSON = new ProgramaWriteJSON(programaTreino);
            programaWriteJSON.preencherObjetivosPrograma(programaTreino);
            programaWriteJSON.preencherProgramaTreinoFichas(programaTreino);
            programaWriteJSON.setFrequencia(obterAndamento(contexto, programaTreino));
            return modelMap.addAttribute(RETURN, programaWriteJSON);
        }

        return modelMap.addAttribute(RETURN, "empty");
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/incluirProgramaAoAluno", method = RequestMethod.POST)
    public String incluirProgramaAoAluno(@PathVariable final String contexto,
                                          @RequestParam final Integer empresa,
                                          @RequestParam final Integer codigoPrograma,
                                          @RequestParam final String inicio,
                                          @RequestParam final String fim,
                                          @RequestParam final Integer codigoCliente) {
        try {
            ClienteSintetico cliente = cs.obterPorCodigoCliente(contexto, codigoCliente);
            if(cliente == null){
                return "Cliente não encontrado";
            }
            ProgramaTreino programa = ps.obterPorId(contexto, codigoPrograma);
            if(programa == null){
                return "Programa não encontrado";
            }

            programa.setDataInicio(Uteis.getDate(inicio, "dd/MM/yyyy"));
            programa.setDataTerminoPrevisto(Uteis.getDate(fim, "dd/MM/yyyy"));
            ps.criarProximoProgramaTreinoJSON(contexto, empresa, cliente, programa);
            return "OK";
        }catch (Exception ex){
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @ResponseBody
    @RequestMapping(value = "{contexto}/alterarPrograma", method = RequestMethod.POST)
    public String alterarPrograma(@PathVariable final String contexto,
                                         @RequestParam final Integer empresa,
                                         @RequestParam final Integer codigoPrograma,
                                         @RequestParam final String novaDataFim) {
        try {
            ProgramaTreino programa = ps.obterPorId(contexto, codigoPrograma);
            if(programa == null){
                return "Programa não encontrado";
            }

            if(programa.getDataInicio().after(Uteis.getDate(novaDataFim, "dd/MM/yyyy"))){
                return "Data de término não pode ser menor que a data de início";
            }

            if(programa.getDataInicio().before(Calendario.hoje())){
                return "O programa já iniciou";
            }

            programa.setDataTerminoPrevisto(Uteis.getDate(novaDataFim, "dd/MM/yyyy"));
            ps.alterarProgramaTreinoJSON(contexto, empresa, programa);
            return "OK";
        }catch (Exception ex){
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
            return ex.getMessage();
        }
    }

    @RequestMapping(value = "{ctx}/default", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap gerarProgramaDefault(@PathVariable String ctx,
            @RequestParam final String username, @RequestParam final Integer codigoCliente) {
        ModelMap mm = new ModelMap();
        try {
            acao("default");
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.INCLUIR);
                ClienteSintetico cli = cs.obterPorId(ctx, codigoCliente);
                if (cli != null) {
                    ProgramaTreino programa = ps.gerarProgramaDefault(ctx, cli, usuario, null, null);
                    ProgramaWriteJSON json = new ProgramaWriteJSON(programa);
                    json.setUsername(username);
                    mm.addAttribute(STATUS_SUCESSO, json);
                } else {
                    throw new ValidacaoException("cliente.naoencontrado.treino");
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/persistir", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap persistir(@PathVariable String ctx, @RequestBody final ProgramaWriteJSON programaGravar) {
        ModelMap mm = new ModelMap();
        try {
            acao("persistir");
            Usuario usuario = us.obterPorAtributo(ctx, "username", programaGravar.getUsername());
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);
                ProgramaTreino p = ps.prepararPersistenciaJSON(ctx, programaGravar);
                if(!p.getCliente().getProfessorSintetico().getCodigo().equals(p.getProfessorCarteira().getCodigo())){
                    ProfessorSintetico profAntesAlteracao = p.getCliente().getProfessorSintetico();
                    p.getCliente().setProfessorSintetico(p.getProfessorCarteira());
                    cs.alterar(ctx,  p.getCliente());
                    cs.inserirLogAlteracaoProfessorAluno(ctx, profAntesAlteracao, p.getCliente().getProfessorSintetico(), p.getCliente().getMatricula(), "AÇÃO ProgramaTreinoJSONControle");
                }
                p = ps.gravarProgramaSemTratarExcecao(ctx, p, usuario.getProfessor());
                ProgramaWriteJSON json = new ProgramaWriteJSON(p);
                json.preencherObjetivosPrograma(p);
                json.preencherProgramaTreinoFichas(p);
                json.setUsername(programaGravar.getUsername());
                mm.addAttribute(STATUS_SUCESSO, json);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(vex).replaceAll("\\n", "") );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/importar-programas-by-dto", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap importarProgramas(@PathVariable String ctx, @RequestBody final List<ProgramaFichaJSON> programaGravar) {
        ModelMap mm = new ModelMap();
        try {
            String ret = ps.importarProgramasJson(ctx, programaGravar);
            mm.addAttribute(STATUS_SUCESSO, ret);
        } catch (ValidacaoException vex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(vex).replaceAll("\\n", ""));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/excluir", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap excluir(@PathVariable String ctx, @RequestParam final String username,
            @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                if (p != null) {
                    ps.excluir(ctx, p, username, true);
                    mm.addAttribute(STATUS_SUCESSO, MSG_DADOS_EXCLUIDOS);
                } else {
                    throw new ServiceException(MSG_DADOS_NAOENCONTRADOS);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/revisar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap revisar(@PathVariable String ctx, @RequestParam final String username,
            @RequestParam final Integer codigoPrograma, @RequestParam final String dataProximaRevisao,
            @RequestParam final String justificativa) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                p.manterAntesAlteracao();
                Date proximaRevisao = Calendario.getDate(Calendario.MASC_DATA, dataProximaRevisao);
                ps.gravarHistoricoRevisoes(ctx, p, justificativa, proximaRevisao, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("programa.revisado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ultimoPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ultimoPrograma(@PathVariable final String ctx,
            @RequestParam final String username, @RequestParam final Integer codigoCliente) {
        ModelMap mm = new ModelMap();
        try {
            acao("ultimoPrograma");
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)) {
                List<ProgramaTreino> lista = ps.obterProgramasPorCliente(ctx,
                        codigoCliente, null, null, null, 1);
                ProgramaTreino prog = lista != null && !lista.isEmpty() ? lista.get(0) : null;
                if (prog != null) {
                    ProgramaWriteJSON json = new ProgramaWriteJSON(prog);
                    json.preencherObjetivosPrograma(prog);
                    json.preencherProgramaTreinoFichas(prog);
                    json.setUsername(username);
                    json.setFrequencia(obterAndamento(ctx, prog));
                    mm.addAttribute(RETURN, json);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarUltimoPrograma", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarUltimoProgramaApp(@PathVariable final String ctx,
                            @RequestParam final String userName, @RequestParam final Integer codigoAluno) {
        ModelMap mm = new ModelMap();
        try {
            acao("ultimoPrograma");
            Usuario usuario = us.obterPorAtributo(ctx, "username", userName);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)) {
                List<ProgramaTreino> lista = ps.obterProgramasPorCliente(ctx,
                        codigoAluno, null, null, null, 1);
                ProgramaTreino prog = lista != null && !lista.isEmpty() ? lista.get(0) : null;
                if (prog != null) {
                    ps.atualizarNrTreinosRealizados(ctx, prog.getCodigo());
                    ProgramaWriteAppJSON json = new ProgramaWriteAppJSON(prog);
                    json.preencherObjetivosPrograma(prog);
                    final String urlBase = TemaAtividade.getURLBase(ctx);
                    json.preencherProgramaTreinoFichas(prog, urlBase);
                    json.setUsername(userName);
                    json.setFrequencia(obterAndamento(ctx, prog));
                    mm.addAttribute("sucesso", json);
                }
                lista = null;
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/historicoExecucoes", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap historicoExecucoes(@PathVariable final String ctx,
            @RequestParam final Integer codigoCliente,
            @RequestParam final Integer maxResults,
            @RequestParam final Integer index) {
        ModelMap mm = new ModelMap();
        try {
            if (codigoCliente != null && codigoCliente > 0) {
                List<TreinoRealizado> treinosRealizados = ps.obterTreinosRealizado(ctx, null,
                        Calendario.anterior(Calendar.YEAR, Calendario.hoje()),
                        Calendario.proximo(Calendar.DAY_OF_MONTH, Calendario.hoje()),
                        codigoCliente, maxResults, index);
                List<TreinoRealizadoHistoricoJSON> arrJSON = new ArrayList<TreinoRealizadoHistoricoJSON>();
                for (TreinoRealizado tr : treinosRealizados) {
                    TreinoRealizadoHistoricoJSON trHist = new TreinoRealizadoHistoricoJSON(
                            tr.getCodigo(),
                            tr.getProgramaTreinoFicha().getPrograma().getNome(),
                            tr.getDataInicio(), tr.getProgramaTreinoFicha().getFicha().getNome(),
                            (tr.getProfessor() != null ? tr.getProfessor().getNome() : ""), tr.getProgramaTreinoFicha().getFicha().getAtividades().size());
                    arrJSON.add(trHist);
                }
                mm.addAttribute(RETURN, arrJSON);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/ultimaAtualizacao", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ultimaAtualizacao(@PathVariable String ctx,
            @RequestParam final String dataAtual,
            @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
            p.setDataUltimaAtualizacao(Calendario.getDate(Calendario.MASC_DATAHORA, dataAtual));
            ps.alterar(ctx, p);
            mm.addAttribute(RETURN, STATUS_SUCESSO);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/lembreteCompromisso", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap lembreteCompromisso(@PathVariable String ctx) {
        ModelMap mm = new ModelMap();
        try {
            TaskCompromissoTreino task = new TaskCompromissoTreino(ctx);
            task.execute(null);
            mm.addAttribute(RETURN, "Processo de lembrete treino executado com sucesso!");
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(EndpointDocController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @RequestMapping(value = "{ctx}/renovar", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap renovar(@PathVariable String ctx, @RequestParam final String username,
            @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                ps.renovarProgramaTreino(ctx, p, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("programa.renovado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, vex.getMensagens() );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/programaultimasexecucoes", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap ultimasExcucoesFichasPrograma(@PathVariable final String ctx, @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Map<Integer, String> array = ps.obterUltimosTreinosPrograma(ctx, codigoPrograma);
            mm.addAttribute(STATUS_SUCESSO, array);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/andamento", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap andamentoPrograma(@PathVariable final String ctx, @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            ProgramaTreinoAndamento andamento = ps.consultarAndamentoSimples(ctx, codigoPrograma);
            mm.addAttribute("nrTreinos", andamento.getNrTreinos());
            mm.addAttribute("totalAulasPrevistas", andamento.getTotalAulasPrevistas());
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/sync", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap sync(@PathVariable String ctx, @RequestParam String dataHora) {
        ModelMap mm = new ModelMap();
        List<ProgramaTreinoJSON> arrJSON = new ArrayList();

        try {
            acao("ProgramaTreinoWriteJSON.sync");
            ConfiguracaoSistema configSeriesSet = configService.consultarPorTipo(ctx, ConfiguracoesEnum.AGRUPAMENTO_SERIES_SET);
            Date dataBase = Calendario.getDate(Calendario.MASC_DATAHORA, dataHora);
            List<ProgramaTreino> programas = syncs.obterLista(ctx, TipoClassSincronizarEnum.ProgramaTreino, dataBase);
            for (ProgramaTreino programaTreino : programas) {
                if (programaTreino.getCliente() != null) {
                    Usuario usuario = us.consultarPorCliente(ctx, programaTreino.getCliente().getCodigo());
                    ProgramaTreino programaVigente = ps.obterUltimoProgramaVigente(ctx, programaTreino.getCliente());
                    ProgramaTreinoJSON programaJSON = preencherProgramaJSON(programaVigente, ctx, null, configSeriesSet.getValorAsBoolean(), "", null);
                    programaJSON.setUserName(usuario.getUserName());
                    obterClienteMensagemAviso(ctx, programaTreino, programaJSON);
                    arrJSON.add(programaJSON);
                } else {
                    ps.excluirHistoricoRevisaoSincronizacao(ctx, programaTreino);
                }
            }
            mm.addAttribute(RETURN, arrJSON);
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
        }
        return mm;
    }

    public ProgramaTreinoService getProgramaTreinoService() {
        return (ProgramaTreinoService) UtilContext.getBean(ProgramaTreinoService.class);
    }

    @RequestMapping(value = "{ctx}/app/consultarProgramaBase", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarProgramaBaseApp(@PathVariable String ctx,
                                  @RequestParam final String userName, @RequestParam final Integer codigoCliente) {
        ModelMap mm = new ModelMap();
        try {
            acao("default");
            Usuario usuario = us.obterPorAtributo(ctx, "username", userName);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.INCLUIR);
                ClienteSintetico cli = cs.obterPorId(ctx, codigoCliente);
                if (cli != null) {
                    ProgramaTreino programa = ps.gerarProgramaDefault(ctx, cli, usuario, null, null);
                    ProgramaWriteAppJSON json = new ProgramaWriteAppJSON(programa);
                    json.setUsername(userName);
                    mm.addAttribute("sucesso", json);
                } else {
                    throw new ValidacaoException("cliente.naoencontrado.treino");
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/consultarHistoricoExecs", method = RequestMethod.GET)
    public @ResponseBody
    ModelMap consultarHistoricoExecsApp(@PathVariable final String ctx,
                                @RequestParam final Integer codigoCliente,
                                @RequestParam final Integer maxResults,
                                @RequestParam final Integer index) {
        ModelMap mm = new ModelMap();
        try {
            if (codigoCliente != null && codigoCliente > 0) {
                List<TreinoRealizado> treinosRealizados = ps.obterTreinosRealizado(ctx, null,
                        Calendario.anterior(Calendar.YEAR, Calendario.hoje()),
                        Calendario.proximo(Calendar.DAY_OF_MONTH, Calendario.hoje()),
                        codigoCliente, maxResults, index);
                List<TreinoRealizadoHistoricoAppJSON> arrJSON = new ArrayList<TreinoRealizadoHistoricoAppJSON>();

                for (TreinoRealizado tr : treinosRealizados) {
                    Integer codigo = null;
                    String nomePrograma = null;
                    String nomeFicha = null;
                    String nomeProfessor = null;
                    Integer nrAtividades = null;
                    Integer nrAtividdesConcluidas = null;
                    String nomeNivel = null;
                    Timestamp datainIcio = null;
                    if(tr != null)
                    {
                        codigo = tr.getCodigo();
                        nomeProfessor = tr.getNomeProfessor();
                        datainIcio = tr.getDataInicio() != null ? new Timestamp(tr.getDataInicio().getTime()) : null;
                        if(tr.getProgramaTreinoFicha() != null)
                        {
                            nomePrograma = tr.getProgramaTreinoFicha().getPrograma() != null ?
                                    tr.getProgramaTreinoFicha().getPrograma().getNome() : null;
                            nomeFicha = tr.getProgramaTreinoFicha().getNomeFicha();

                            if(tr.getProgramaTreinoFicha().getFicha() != null) {
                                Ficha ficha = tr.getProgramaTreinoFicha().getFicha();
                                List<AtividadeFicha> atividadeFichas = fichaService.obterAtividadesFicha(ctx, ficha.getCodigo());
                                nrAtividades = atividadeFichas.size();
                                nomeNivel = ficha.getNivel() != null ? ficha.getNivel().getNome() : null;
                            }
                        }
                        nrAtividdesConcluidas = ps.obterNumeroAtividadesRealizadas(ctx, codigo);
                    }

                    TreinoRealizadoHistoricoAppJSON trHist = new TreinoRealizadoHistoricoAppJSON(
                            codigo,
                            nomePrograma,
                            nomeFicha,
                            nomeProfessor,
                            nrAtividades,
                            nrAtividdesConcluidas,
                            nomeNivel,
                            datainIcio);

                    arrJSON.add(trHist);
                }
                mm.addAttribute("sucesso", arrJSON);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/removerPrograma", method = RequestMethod.DELETE)
    public @ResponseBody
    ModelMap removerProgramaApp(@PathVariable String ctx, @RequestParam final String username,
                     @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                if (p != null) {
                    ps.excluir(ctx, p, username, true);
                    mm.addAttribute(STATUS_SUCESSO, MSG_DADOS_EXCLUIDOS);
                } else {
                    throw new ServiceException(MSG_DADOS_NAOENCONTRADOS);
                }
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/revisaoPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap revisaoProgramaApp(@PathVariable String ctx, @RequestParam final String username,
                     @RequestParam final Integer codigoPrograma, @RequestParam final String justificativa) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                if(p == null)
                {
                    throw new ServiceException("Programa não encontrado");
                }
                p.manterAntesAlteracao();
                ps.gravarHistoricoRevisoes(ctx, p, justificativa, null, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("programa.revisado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/persistirPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap persistirProgramaApp(@PathVariable String ctx, @RequestBody final ProgramaWriteAppJSON programaGravar,
                                  @RequestParam (required = false, defaultValue = "1") Integer origem) {
        ModelMap mm = new ModelMap();
        try {
            acao("persistir");
            Usuario usuario = us.obterPorAtributo(ctx, "username", programaGravar.getUsername());
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EDITAR);

                ProgramaTreino p = ps.prepararPersistenciaAppJSON(ctx, programaGravar, origem);
                if(p == null)
                {
                    throw new ServiceException("Programa de treino não localizado");
                }
                if(p.getCliente().getProfessorSintetico() != null && !p.getCliente().getProfessorSintetico().getCodigo().equals(p.getProfessorCarteira().getCodigo())){
                    ProfessorSintetico profAntesAlteracao = p.getCliente().getProfessorSintetico();
                    p.getCliente().setProfessorSintetico(p.getProfessorCarteira());
                    cs.alterar(ctx,  p.getCliente());
                    cs.inserirLogAlteracaoProfessorAluno(ctx, profAntesAlteracao, p.getCliente().getProfessorSintetico(), p.getCliente().getMatricula(), "AÇÃO ProgramaTreinoJSONControle");
                }
                p = ps.gravarProgramaSemTratarExcecao(ctx, p, usuario.getProfessor());
                ProgramaWriteAppJSON json = new ProgramaWriteAppJSON(p);
                ps.obterObjetivosDoPrograma(ctx, p);
                json.preencherObjetivosPrograma(p);
                final String urlBase = TemaAtividade.getURLBase(ctx);
                p.setProgramaFichas(ps.obterProgramasFichasPorPrograma(ctx, p.getCodigo()));
                json.preencherProgramaTreinoFichas(p, urlBase);
                json.setUsername(programaGravar.getUsername());
                mm.addAttribute(STATUS_SUCESSO, json);
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(vex).replaceAll("\\n", "") );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAcao();
            leaveAccessControl();
        }
        return mm;
    }

    @RequestMapping(value = "{ctx}/app/renovarPrograma", method = RequestMethod.POST)
    public @ResponseBody
    ModelMap renovarProgramaApp(@PathVariable String ctx, @RequestParam final String username,
                     @RequestParam final Integer codigoPrograma) {
        ModelMap mm = new ModelMap();
        try {
            Usuario usuario = us.obterPorAtributo(ctx, "username", username);
            if (usuario != null && !usuario.getTipo().equals(TipoUsuarioEnum.ALUNO)
                    && usuario.getProfessor() != null) {
                accessControl(usuario, RecursoEnum.PROGRAMA_TREINO, TipoPermissaoEnum.EXCLUIR);
                ProgramaTreino p = ps.obterPorId(ctx, codigoPrograma);
                ps.obterObjetivosDoPrograma(ctx, p);
                ps.renovarProgramaTreino(ctx, p, usuario.getProfessor());
                mm.addAttribute(STATUS_SUCESSO, getViewUtils().getMensagem("Programa renovado"));
            } else {
                throw new ServiceException(getViewUtils().getMensagem("mobile.usuarioinvalido"));
            }
        } catch (ValidacaoException vex)  {
            mm.addAttribute(STATUS_ERRO, vex.getMensagens() );
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.INFO, null, vex);
        }  catch (ServiceException ex) {
            mm.addAttribute(STATUS_ERRO, getViewUtils().getMensagem(ex.getMessage()));
            Logger.getLogger(ProgramaTreinoJSONControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            leaveAccessControl();
        }
        return mm;
    }

    public ProgramaTreinoAndamento getProgramaTreinoAndamento() {
        return programaTreinoAndamento;
    }

    public void setProgramaTreinoAndamento(ProgramaTreinoAndamento programaTreinoAndamento) {
        this.programaTreinoAndamento = programaTreinoAndamento;
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pre-definido/franqueadora", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPreDefinidosSlimFranqueadora(@RequestParam String nome,
                                                                                              @RequestParam String chaveRede) throws JSONException {
        try {
            return ResponseEntityFactory.ok(ps.obterProgramasPreDefinidosSlim(false, nome, chaveRede, true));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar programa de treino da franqueadora",
                  notes = "Consulta os detalhes completos de um programa de treino específico através do ID e chave da franqueadora.\n\n" +
                          "<strong>Permissões necessárias:</strong> PROGRAMA_TREINO\n\n" +
                          "<strong>Retorna:</strong> Dados completos do programa incluindo fichas, professor responsável e informações de progresso.",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Programa de treino consultado com sucesso",
                     response = ExemploRespostaProgramaTreinoResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/franqueadora/{id}/{chaveFranqueadora}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramaTreinoFranqueadora(
            @ApiParam(value = "ID único do programa de treino", required = true, defaultValue = "1")
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Chave de identificação da franqueadora", required = true, defaultValue = "FRANQ001")
            @PathVariable("chaveFranqueadora") final String chaveFranqueadora) {
        try {
            return ResponseEntityFactory.ok(ps.consultarProgramaTreino(id, chaveFranqueadora));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar aulas agendadas por aluno",
                  notes = "Consulta todas as aulas agendadas para um aluno específico em um período determinado.\n\n" +
                          "<strong>Autenticação:</strong> Requer identificação da empresa no cabeçalho\n\n" +
                          "<strong>Retorna:</strong> Lista das aulas agendadas com informações de horário, professor e turma.",
                  tags = "Programa de Treino")
    @ApiResponses({
        @ApiResponse(code = 200, message = "Aulas agendadas consultadas com sucesso",
                     response = ExemploRespostaListAulaAlunoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/aulasAgendadasPorAluno/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAulasAgendadasPorAluno(
            @ApiParam(value = "Contexto da aplicação", required = true, defaultValue = "academia")
            @PathVariable String ctx,
            @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
            @PathVariable("matricula") final Integer matricula,
            @ApiParam(value = "ID da empresa", required = true, defaultValue = "1")
            @RequestHeader("empresaId") Integer empresaId,
            @ApiParam(value = "Data de início do período de consulta no formato dd/MM/yyyy", required = false, defaultValue = "01/01/2024")
            @RequestParam (defaultValue = "01/01/2001") String dataInicio,
            @ApiParam(value = "Data de fim do período de consulta no formato dd/MM/yyyy", required = true, defaultValue = "31/12/2024")
            @RequestParam String dataFim) {
        try {
            return ResponseEntityFactory.ok(ps.consultarAulasAgendadasPorAluno(matricula, dataInicio, dataFim, ctx, null));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ApiOperation(value = "Consultar aulas agendadas por aluno (versão 2)",
                  notes = "Consulta todas as aulas agendadas para um aluno específico em um período determinado. " +
                         "Esta versão retorna as datas no formato dd/MM/yyyy (diferente da versão 1 que retorna yyyy-MM-dd). " +
                         "Inclui aulas de turmas, reposições e aulas desmarcadas. " +
                         "Requer permissão de acesso ao programa de treino.",
                  tags = "Programa de Treino")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Aulas agendadas consultadas com sucesso",
                        response = ExemploRespostaAulasAgendadasPorAlunoV2.class)
    })
    @ResponseBody
    @RequestMapping(value = "{ctx}/v2/aulasAgendadasPorAluno/{matricula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAulasAgendadasPorAlunoV2(
            @ApiParam(value = "Contexto da aplicação (chave da empresa)", required = true, defaultValue = "academia")
            @PathVariable String ctx,
            @ApiParam(value = "Número da matrícula do aluno", required = true, defaultValue = "12345")
            @PathVariable("matricula") final Integer matricula,
            @ApiParam(value = "Data de início do período de consulta no formato dd/MM/yyyy", required = false, defaultValue = "01/01/2024")
            @RequestParam (defaultValue = "01/01/2001") String dataInicio,
            @ApiParam(value = "Data de fim do período de consulta no formato dd/MM/yyyy", required = true, defaultValue = "31/12/2024")
            @RequestParam String dataFim,
            @ApiParam(value = "Código do contrato específico para filtrar as aulas (opcional)", required = false, defaultValue = "1001")
            @RequestParam (required = false) Integer contrato) {
        try {
            return ResponseEntityFactory.ok(ps.consultarAulasAgendadasPorAlunoV2(matricula, dataInicio, dataFim, ctx, contrato));
        } catch (Exception e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar o programa", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/listar-predefinidos/{ctx}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarProgramasPredefinidosFull(@PathVariable("ctx") final String ctx) throws JSONException {
        try {
            return ResponseEntityFactory.ok(ps.obterProgramasPreDefinidosPorChave(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas pré-definidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.PROGRAMA_TREINO)
    @RequestMapping(value = "/pendentes/{ctx}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarTreinoPorIA(@PathVariable("ctx") final String ctx) {
        try {
            List<ConsultaTreinoDTO> treinos = ps.obterProgramaTreinoGeradoPorIA(ctx);
            return ResponseEntityFactory.ok(treinos);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas de treino gerador por I.A.", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @RequestMapping(value = "{ctx}/criaProgramaTreinoPorIA", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<EnvelopeRespostaDTO> criaProgramaTreinoPorIA(
            @PathVariable String ctx,
            @RequestBody AnamneseTreinoPorIADTO anamneseTreinoPorIADTO) {
        try {
            return ResponseEntityFactory.ok(ps.preparaProgramaTreinoPorIA(anamneseTreinoPorIADTO, ctx));
        } catch (Exception e) {
            Logger.getLogger(AtividadeJSONControle.class.getName()).log(Level.SEVERE, "Erro ao criar programa por IA", e);
            return ResponseEntityFactory.erroInterno("erro.interno", e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/app/excluiProgramaTreinoPorId", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluiProgramaTreinoPorId(@PathVariable String ctx,
                                                                         @RequestParam Integer codigoPrograma) {
        try {
            ps.excluirProgramaTreino(codigoPrograma, ctx);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir programa", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroEstaSendoUtilizado(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "{ctx}/app/dados", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> dadosTreinos(@PathVariable String ctx,
                                                            @RequestParam(required = false) Integer codigoCliente,
                                                            @RequestParam(required = false) String periodoInicio,
                                                            @RequestParam(required = false) String periodoFinal) {
        try {
            return ResponseEntityFactory.ok(ps.dadosTreinos(ctx, codigoCliente, periodoInicio, periodoFinal));
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar os programas de treino gerador por I.A.", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
