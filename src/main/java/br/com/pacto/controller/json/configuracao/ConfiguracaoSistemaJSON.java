/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.controller.json.configuracao;

import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "Objeto que representa as configurações do sistema para uma empresa")
public class ConfiguracaoSistemaJSON {

    @ApiModelProperty(value = "Chave identificadora da empresa", example = "academia123")
    private String key;

    @ApiModelProperty(value = "Lista de configurações do sistema")
    private List<ConfigJSON> configuracoes;

    @ApiModelProperty(value = "Timestamp da última atualização das configurações", example = "1640995200000")
    private Long lastUpdate = 0L;

    public ConfiguracaoSistemaJSON() {
    }

    public ConfiguracaoSistemaJSON(String key, List<ConfiguracaoSistema> configuracoes) {
        this.key = key;
        this.configuracoes = new ArrayList<ConfigJSON>();
        for (ConfiguracaoSistema conf : configuracoes) {
            ConfigJSON cJson = new ConfigJSON(conf.getConfiguracao().name(),
                    conf.getValor(), conf.getConfiguracao().getTipo().name());
            this.configuracoes.add(cJson);
        }
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<ConfigJSON> getConfiguracoes() {
        return configuracoes;
    }

    public void setConfiguracoes(List<ConfigJSON> configuracoes) {
        this.configuracoes = configuracoes;
    }

    public Long getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Long lastUpdate) {
        this.lastUpdate = lastUpdate;
    }
}
