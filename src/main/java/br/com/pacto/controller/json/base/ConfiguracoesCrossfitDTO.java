package br.com.pacto.controller.json.base;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para configurações específicas do módulo CrossFit")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracoesCrossfitDTO {

    @ApiModelProperty(value = "Código do produto utilizado para inscrição de alunos em games de CrossFit. Define qual produto será lançado automaticamente quando um aluno se inscrever em competições", example = "123")
    private String produto_inscricao_game;

    @ApiModelProperty(value = "Configuração para alteração das nomenclaturas padrão do CrossFit. \n\n" +
            "<strong>Valores disponíveis:</strong>\n" +
            "- <strong>true:</strong> Utiliza nomenclaturas alternativas (WOD = Objetivo, TIPO WOD = Periodização, etc.)\n" +
            "- <strong>false:</strong> Mantém as nomenclaturas padrão do CrossFit", example = "false")
    private String troca_nomenclatura_crossfit;

    public String getProduto_inscricao_game() {
        return produto_inscricao_game;
    }

    public void setProduto_inscricao_game(String produto_inscricao_game) {
        this.produto_inscricao_game = produto_inscricao_game;
    }

    public String getTroca_nomenclatura_crossfit() {
        return troca_nomenclatura_crossfit;
    }

    public void setTroca_nomenclatura_crossfit(String troca_nomenclatura_crossfit) {
        this.troca_nomenclatura_crossfit = troca_nomenclatura_crossfit;
    }
}
