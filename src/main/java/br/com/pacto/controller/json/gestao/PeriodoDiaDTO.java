package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DiasSemanaDashboardBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "DTO contendo dados de execução de treinos por períodos do dia (manhã, tarde, noite).")
public class PeriodoDiaDTO {

    @ApiModelProperty(value = "Total de execuções no período da manhã.", example = "15")
    private Integer manha;

    @ApiModelProperty(value = "Total de execuções no período da tarde.", example = "22")
    private Integer tarde;

    @ApiModelProperty(value = "Total de execuções no período da noite.", example = "18")
    private Integer noite;

    @ApiModelProperty(value = "Total geral de execuções no dia.", example = "55")
    private Integer total;

    @ApiModelProperty(value = "Total de execuções via aplicativo no período da manhã.", example = "12")
    private Integer manhaApp;

    @ApiModelProperty(value = "Total de execuções via aplicativo no período da tarde.", example = "18")
    private Integer tardeApp;

    @ApiModelProperty(value = "Total de execuções via aplicativo no período da noite.", example = "14")
    private Integer noiteApp;

    @ApiModelProperty(value = "Total geral de execuções via aplicativo no dia.", example = "44")
    private Integer totalApp;

    public PeriodoDiaDTO() {
    }

    public PeriodoDiaDTO(DiasSemanaDashboardBI diasSemanaDashboardBI) {
        this.manha = diasSemanaDashboardBI.getTotalManha();
        this.tarde = diasSemanaDashboardBI.getTotalTarde();
        this.noite = diasSemanaDashboardBI.getTotalNoite();
        this.total = this.manha + this.tarde + this.noite;
    }

    public PeriodoDiaDTO(DiasSemanaDashboardBI diasSemanaDashboardBI, TreinoRealizadoAppDTO treinoRealizadoAppDTO) {
        this.manha = diasSemanaDashboardBI.getTotalManha();
        this.tarde = diasSemanaDashboardBI.getTotalTarde();
        this.noite = diasSemanaDashboardBI.getTotalNoite();
        this.total = this.manha + this.tarde + this.noite;

        this.manhaApp = treinoRealizadoAppDTO.getPeriodo().getManhaApp();
        this.tardeApp = treinoRealizadoAppDTO.getPeriodo().getTardeApp();
        this.noiteApp = treinoRealizadoAppDTO.getPeriodo().getNoiteApp();
        this.totalApp = this.manhaApp + this.tardeApp + this.noiteApp;
    }

    public Integer getManha() {
        return manha;
    }

    public void setManha(Integer manha) {
        this.manha = manha;
    }

    public Integer getTarde() {
        return tarde;
    }

    public void setTarde(Integer tarde) {
        this.tarde = tarde;
    }

    public Integer getNoite() {
        return noite;
    }

    public Integer getManhaApp() {
        if(manhaApp == null)
            return 0;
        return manhaApp;
    }

    public void setManhaApp(Integer manhaApp) {
        this.manhaApp = manhaApp;
    }

    public Integer getTardeApp() {
        if(tardeApp == null)
            return 0;
        return tardeApp;
    }

    public void setTardeApp(Integer tardeApp) {
        this.tardeApp = tardeApp;
    }

    public Integer getNoiteApp() {
        if(noiteApp == null)
            return 0;
        return noiteApp;
    }

    public void setNoiteApp(Integer noiteApp) {
        this.noiteApp = noiteApp;
    }

    public Integer getTotalApp() {
        return totalApp;
    }

    public void setTotalApp(Integer totalApp) {
        this.totalApp = totalApp;
    }

    public void setNoite(Integer noite) {
        this.noite = noite;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
