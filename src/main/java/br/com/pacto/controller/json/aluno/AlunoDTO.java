package br.com.pacto.controller.json.aluno;

import br.com.pacto.bean.pessoa.SexoEnum;
import br.com.pacto.bean.pessoa.TipoTelefoneEnum;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 28/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do aluno do aluno")
public class AlunoDTO {

    @ApiModelProperty(value = "Código identificador do aluno", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Nome completo do aluno", example = "João da <PERSON>")
    private String nome;

    @ApiModelProperty(value = "Código do nível do aluno", example = "2")
    private Integer nivelId;

    @ApiModelProperty(value = "Código do professor responsável", example = "45")
    private Integer professorId;

    @ApiModelProperty(value = "Data de nascimento do aluno", example = "2000-01-15")
    private Date dataNascimento;

    @ApiModelProperty(value = "Sexo do aluno. \n\n<strong>Valores disponíveis</strong>" +
            "- N (Não Informado)\n" +
            "- M (Masculino)\n" +
            "- F (Feminino)\n", allowableValues = "N,M,F", example = "M")
    private SexoEnum sexo;

    @ApiModelProperty(value = "Lista de e-mails do aluno", example = "[\"<EMAIL>\"]")
    private List<String> emails;

    @ApiModelProperty(value = "Lista de telefones do aluno")
    private List<TelefoneDTO> fones;

    @ApiModelProperty(value = "Indica se o aluno utiliza o aplicativo", example = "true")
    private Boolean usarApp;

    @ApiModelProperty(value = "Nome de usuário do aluno no aplicativo", example = "joao.silva")
    private String appUsername;

    @ApiModelProperty(value = "Senha do aluno no aplicativo", example = "senha123")
    private String appPassword;

    @ApiModelProperty(value = "Situação do aluno.\n\n " +
            "<strong>Valores disponíveis</strong>" +
            "- AT - Ativo\n" +
            "- IN - Inativo\n" +
            "- VI - Visitante\n" +
            "- TR - Trancado\n" +
            "- AE - Atestado\n" +
            "- CA - Cancelado\n" +
            "- CR - Carência\n" +
            "- DE - Desistente\n" +
            "- VE - Vencido\n" +
            "- OU - Outros", example = "AT", allowableValues = "AT,IN,VI,TR,AE,CA,CR,DE,VE,OU")
    private String situacaoAluno;

    @ApiModelProperty(value = "Data da imagem do aluno (metadado para atualização)", example = "20250610")
    private String imagemData;

    @ApiModelProperty(value = "Extensão do arquivo da imagem", example = "jpg")
    private String extensaoImagem;

    @ApiModelProperty(value = "Código externo do aluno", example = "789")
    private String codigoExterno;


    public AlunoDTO() {
    }

    public AlunoDTO(AlunoCadastroSimplesDTO alunoCadastroSimplesDTO) {
        this.nome = alunoCadastroSimplesDTO.getNome();
        try {
            this.dataNascimento = new Date(alunoCadastroSimplesDTO.getDataNascimento());
        } catch (Exception e) {
            Uteis.logar(e, AlunoDTO.class);
        }
        if (alunoCadastroSimplesDTO.getSexo() != null && !alunoCadastroSimplesDTO.getSexo().isEmpty()) {
            try {
                this.sexo = SexoEnum.valueOf(alunoCadastroSimplesDTO.getSexo().substring(0, 1).toUpperCase());
            } catch (IllegalArgumentException e) {
                this.sexo = SexoEnum.N;
            }
        } else {
            this.sexo = SexoEnum.N;
        }
        if (!UteisValidacao.emptyString(alunoCadastroSimplesDTO.getEmail())) {
            this.appUsername = alunoCadastroSimplesDTO.getEmail();
            SecureRandom random = new SecureRandom();
            this.appPassword = new BigInteger(130, random).toString(32);
            this.appPassword = this.appPassword.length() > 8 ? this.appPassword.substring(0, 8) : this.appPassword;
            this.emails = new ArrayList<String>() {{
                add(alunoCadastroSimplesDTO.getEmail());
            }};
        }
        this.fones = new ArrayList<TelefoneDTO>() {{
            if (!UteisValidacao.emptyString(alunoCadastroSimplesDTO.getCelular())) {
                TelefoneDTO telefoneDTO = new TelefoneDTO();
                telefoneDTO.setTipo(TipoTelefoneEnum.CELULAR);
                telefoneDTO.setNumero(alunoCadastroSimplesDTO.getCelular());
                add(telefoneDTO);
            }
        }};
        this.usarApp = true;
        this.situacaoAluno = "ATIVO";
        this.imagemData = alunoCadastroSimplesDTO.getImagemData();
        this.extensaoImagem = ".jpg";
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getNivelId() {
        return nivelId;
    }

    public void setNivelId(Integer nivelId) {
        this.nivelId = nivelId;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public SexoEnum getSexo() {
        return sexo;
    }

    public void setSexo(SexoEnum sexo) {
        this.sexo = sexo;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public List<TelefoneDTO> getFones() {
        return fones;
    }

    public void setFones(List<TelefoneDTO> fones) {
        this.fones = fones;
    }

    public Boolean getUsarApp() {
        return usarApp;
    }

    public void setUsarApp(Boolean usarApp) {
        this.usarApp = usarApp;
    }

    public String getAppUsername() {
        return appUsername;
    }

    public void setAppUsername(String appUsername) {
        this.appUsername = appUsername;
    }

    public String getAppPassword() {
        return appPassword;
    }

    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public String getImagemData() {
        return imagemData;
    }

    public void setImagemData(String imagemData) {
        this.imagemData = imagemData;
    }

    public String getExtensaoImagem() {
        return extensaoImagem;
    }

    public void setExtensaoImagem(String extensaoImagem) {
        this.extensaoImagem = extensaoImagem;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }
}
