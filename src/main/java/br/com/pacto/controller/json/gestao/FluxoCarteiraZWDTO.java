package br.com.pacto.controller.json.gestao;

import br.com.pacto.bean.bi.DashboardBI;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO contendo dados de fluxo da carteira de alunos, incluindo novos, trocas e saídas.")
public class FluxoCarteiraZWDTO {

    @ApiModelProperty(value = "Total de novos alunos na carteira.", example = "15")
    private Integer novos;

    @ApiModelProperty(value = "Total de alunos que trocaram de professor.", example = "8")
    private Integer trocaram;

    @ApiModelProperty(value = "Total de alunos que saíram da carteira.", example = "5")
    private Integer sairam;

    public FluxoCarteiraZWDTO() {

    }

    public FluxoCarteiraZWDTO(DashboardBI dash) {
        this.novos = dash.getTotalNovosCarteiraNovos();
        this.trocaram = dash.getTotalNovosCarteiraTrocaram();
        this.sairam = dash.getTotalTrocaramCarteira();
    }

    public Integer getNovos() {
        return novos;
    }

    public void setNovos(Integer novos) {
        this.novos = novos;
    }

    public Integer getTrocaram() {
        return trocaram;
    }

    public void setTrocaram(Integer trocaram) {
        this.trocaram = trocaram;
    }

    public Integer getSairam() {
        return sairam;
    }

    public void setSairam(Integer sairam) {
        this.sairam = sairam;
    }
}
