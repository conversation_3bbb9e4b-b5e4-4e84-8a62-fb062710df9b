package br.com.pacto.controller.json.gestao;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.objeto.Calendario;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@ApiModel(description = "DTO para filtros de gestão de programas de treino, contendo critérios de busca e período.")
public class FiltroGestaoProgramaDTO extends SuperJSON {

    @ApiModelProperty(value = "Data inicial do período para consulta", example = "2024-01-01")
    private Date inicio = Calendario.inicioMes(Calendario.anterior(Calendar.MONTH, Calendario.hoje()));

    @ApiModelProperty(value = "Data final do período para consulta", example = "2024-12-31")
    private Date fim = Calendario.fimMes(Calendario.hoje());

    @ApiModelProperty(value = "Mês específico para consulta", example = "12")
    private Integer mes;

    @ApiModelProperty(value = "Ano específico para consulta", example = "2024")
    private Integer ano;

    @ApiModelProperty(value = "Indica se a busca deve ser feita pelo nome do programa", example = "false")
    private Boolean nome = false;

    @ApiModelProperty(value = "Indica se a busca deve ser feita pelo nome do aluno", example = "true")
    private Boolean aluno = false;

    @ApiModelProperty(value = "Parâmetro de busca rápida", example = "Silva")
    private String parametro;

    @ApiModelProperty(value = "Lista de IDs dos colaboradores para filtro")
    private List<Integer> colaboradorIds;

    @ApiModelProperty(value = "Lista de origens de execução da ficha para filtro")
    private List<String> origemExecucaoFicha;

    public FiltroGestaoProgramaDTO(JSONObject filters) throws JSONException {

        if (filters != null) {
            this.parametro = filters.optString("quicksearchValue");

            JSONArray colunasVisiveis = filters.optJSONArray("quicksearchFields");
            if (colunasVisiveis != null) {
                for (int i = 0; i < colunasVisiveis.length(); i++) {
                    if (colunasVisiveis.get(i).equals("aluno")) {
                        this.aluno = true;
                    }
                    if (colunasVisiveis.get(i).equals("nome")) {
                        this.nome = true;
                    }
                }
            }

            JSONArray colaboradorIds = filters.optJSONArray("Professores");
            if (colaboradorIds == null) {
                colaboradorIds = filters.optJSONArray("professoresIds");
            }
            this.colaboradorIds = new ArrayList<>();
            if (colaboradorIds != null) {
                for (int i = 0; i < colaboradorIds.length(); i++) {
                    getColaboradorIds().add(colaboradorIds.getInt(i));
                }
            }

            if (filters.has("dataInicio")) {
                this.inicio = Calendario.getInstanceDate(filters.optLong("dataInicio"));
            }
            if (filters.has("dataFim")) {
                this.fim = Calendario.getInstanceDate(filters.optLong("dataFim"));
            }
            JSONArray origemExecucoes = filters.optJSONArray("origemExecucao");
            if (origemExecucoes != null) {
                List<String> origemList = new ArrayList<>();
                for (int i = 0; i < origemExecucoes.length(); i++) {
                    origemList.add(origemExecucoes.getString(i));
                }
                this.origemExecucaoFicha = origemList;
            }
        }
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Boolean getNome() {
        return nome;
    }

    public void setNome(Boolean nome) {
        this.nome = nome;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public List<Integer> getColaboradorIds() {
        return colaboradorIds;
    }

    public void setColaboradorIds(List<Integer> colaboradorIds) {
        this.colaboradorIds = colaboradorIds;
    }

    public Boolean getAluno() {
        return aluno;
    }

    public void setAluno(Boolean aluno) {
        this.aluno = aluno;
    }

    public List<String> getOrigemExecucaoFicha() {
        return origemExecucaoFicha;
    }

    public void setOrigemExecucaoFicha(List<String> origemExecucaoFicha) {
        this.origemExecucaoFicha = origemExecucaoFicha;
    }
}
