package br.com.pacto.controller.json.gestao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 30/10/2019
 */
@ApiModel(description = "Item da lista de views de gráfico BI disponíveis")
public class BIGraficoResponseListaDTO {
    @ApiModelProperty(value = "Nome descritivo da view de gráfico BI", example = "Agenda")
    private String nome;

    @ApiModelProperty(value = "Indica se é uma view padrão do sistema", example = "true")
    private boolean defaut = false;

    @ApiModelProperty(value = "Identificador único da view de gráfico BI", example = "Agenda")
    private String id;

     public BIGraficoResponseListaDTO( String nome, boolean defaut, String id){
         this.nome = nome;
         this.defaut = defaut;
         this.id = id;

    }

    public BIGraficoResponseListaDTO(){

    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isDefaut() {
        return defaut;
    }

    public void setDefaut(boolean defaut) {
        this.defaut = defaut;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
