package br.com.pacto.controller.json.empresa;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados essenciais da empresa para transferência")
public class EmpresaDTO {

    @ApiModelProperty(value = "Código único identificador da empresa", example = "1", required = true)
    private int codigo;

    @ApiModelProperty(value = "Nome da empresa", example = "Academia Pacto Goiânia", required = true)
    private String nome;

    @ApiModelProperty(value = "Fuso horário padrão da empresa", example = "America/Sao_Paulo")
    private String timeZoneDefault;

    @ApiModelProperty(value = "Chave da imagem/logo da empresa ou URL completa", example = "https://cdn1.pactorian.net/logo_empresa_123.jpg")
    private String keyImgEmpresa;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getKeyImgEmpresa() {
        return keyImgEmpresa;
    }

    public void setKeyImgEmpresa(String keyImgEmpresa) {
        this.keyImgEmpresa = keyImgEmpresa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    public void setTimeZoneDefault(String timeZoneDefault) {
        this.timeZoneDefault = timeZoneDefault;
    }
}
