package br.com.pacto.controller.json.aulaDia;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.turma.HorarioTurmaResponseDTO;
import br.com.pacto.controller.json.turma.TurmasController;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.AgendaExcecoes;
import br.com.pacto.service.intf.agenda.AgendaService;
import br.com.pacto.service.intf.agendatotal.AgendaTotalService;
import br.com.pacto.service.intf.aula.AulaService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.gympass.BookingGymPassService;
import br.com.pacto.service.intf.gympass.GymPassBookingService;
import br.com.pacto.service.intf.gympass.LogGymPassService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.aula.ExemploRespostaAulaColetivaResponseDTO;
import br.com.pacto.swagger.respostas.aula.ExemploRespostaAulaResponseDTO;
import br.com.pacto.swagger.respostas.aula.ExemploRespostaListAulaResponseDTOPaginacao;
import br.com.pacto.swagger.respostas.aula.edicao.ExemploRespostaEdicaoAulaTemporaria;
import br.com.pacto.swagger.respostas.aula.tv.ExemploRespostaListTVAulaDTO;
import br.com.pacto.swagger.respostas.fila.ExemploRespostaInseridoNaFila;
import br.com.pacto.swagger.respostas.fila.ExemploRespostaListFilaDeEsperaDTOPaginacao;
import br.com.pacto.swagger.respostas.horario.turma.ExemploRespostaListHorarioTurmaResponseDTO;
import br.com.pacto.swagger.respostas.horario.turma.ExemploRespostaListHorarioTurmaResponseDTOPaginacao;
import br.com.pacto.swagger.respostas.horario.turma.ExemploRespostaRemoverHorarioTurma;
import br.com.pacto.swagger.respostas.log.ExemploRespostaDetalheLogGymPassTO;
import br.com.pacto.swagger.respostas.log.ExemploRespostaListLogGymPassTO;
import br.com.pacto.swagger.respostas.produto.ExemploRespostaListProductDTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import br.com.pacto.util.UtilContext;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.controller.json.base.SuperControle.STATUS_ERRO;

@Controller
@RequestMapping("/psec/aulas")
public class AulasController extends SuperController {


    private AulaService aulaService;
    private BookingGymPassService bookingGymPassService;
    private LogGymPassService logGymPassService;
    private GymPassBookingService gymPassBookingService;
    private final AgendaService agendaNovaService;
    private final AgendaTotalService agendaTotalService;
    private final ConfiguracaoSistemaService configService;
    private final SessaoService sessaoService;
    private List<FilaDeEsperaDTO> filaOrdenada;


    @Autowired
    public AulasController(AulaService aulaService,
                           LogGymPassService logGymPassService,
                           BookingGymPassService bookingGymPassService,
                           GymPassBookingService gymPassBookingService, AgendaService agendaNovaService, AgendaTotalService agendaTotalService, ConfiguracaoSistemaService configService, SessaoService sessaoService) {
        this.agendaNovaService = agendaNovaService;
        this.agendaTotalService = agendaTotalService;
        this.configService = configService;
        this.sessaoService = sessaoService;
        Assert.notNull(aulaService, "O serviço de aulas não foi injetado corretamente");
        this.aulaService = aulaService;
        this.bookingGymPassService = bookingGymPassService;
        this.logGymPassService = logGymPassService;
        this.gymPassBookingService = gymPassBookingService;
    }

    @ApiOperation(
            value = "Consultar aulas",
            notes = "Consulta as informações das aulas cadastradas no sistema.",
            tags = "Agenda de Aulas"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.\n\n Ordena por um atributo contido no objeto da resposta.\n\n" +
                    "<strong>Ordens disponíveis</strong>\n\n" +
                    "- <strong>ASC</strong> - Ordena pelo atributo escolhido de forma ascendente\n" +
                    "- <strong>DESC</strong> - Ordena pelo atributo escolhido de forma descendente\n\n" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAulaResponseDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAulas(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Filtra pelo nome da aula.\n" +
                    "- <strong>professor:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>ambiente:</strong> Filtra por um ou mais ambientes (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>modalidadeIds:</strong> Filtra por uma ou mais modalidades (Deve ser informado como uma lista de códigos/IDs ex: [5, 8]).\n" +
                    "- <strong>dataInicio:</strong> Filtra pela data de início da aula (Deve ser informado como um timestamp em milissegundos).\n" +
                    "- <strong>dataFim:</strong> Filtra pela data de fim da aula (Deve ser informado como um timestamp em milissegundos).",
                    defaultValue = "{\"quicksearchValue\":\"Zumba\", \"professor\":[1, 2], \"ambiente\":[10], \"modalidadeIds\":[5, 8], \"dataInicio\":1749505862000, \"dataFim\":1749509462000}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO,
            @ApiParam(value = "Código da empresa que as aulas estão vinculadas", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.listarAulas(filtros, paginadorDTO, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar aulas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar logs do GymPass",
            notes = "Consulta as informações dos logs do GymPass.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.\n\n Ordena por um atributo contido no objeto da resposta.\n\n" +
                    "<strong>Ordens disponíveis</strong>\n\n" +
                    "- <strong>ASC</strong> - Ordena pelo atributo escolhido de forma ascendente\n" +
                    "- <strong>DESC</strong> - Ordena pelo atributo escolhido de forma descendente\n\n" +
                    "Deve ser informado como atributo,ordem", defaultValue = "codigo,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListLogGymPassTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "gympass/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGympass(@ApiIgnore @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          @ApiIgnore PaginadorDTO paginadorDTO,
                                                          @ApiIgnore @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(bookingGymPassService.listarLog(paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar logs do GymPass sincronizado",
            notes = "Consulta as informações dos logs do GymPass sincronizado.",
            tags = "Logs"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.\n\n Ordena por um atributo contido no objeto da resposta.\n\n" +
                    "<strong>Ordens disponíveis</strong>\n\n" +
                    "- <strong>ASC</strong> - Ordena pelo atributo escolhido de forma ascendente\n" +
                    "- <strong>DESC</strong> - Ordena pelo atributo escolhido de forma descendente\n\n" +
                    "Deve ser informado como atributo,ordem", defaultValue = "codigo,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListLogGymPassTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "gympass/log/sinc", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGympassSinc(@ApiIgnore @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                              @ApiIgnore PaginadorDTO paginadorDTO,
                                                              @ApiIgnore @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logGymPassService.listarLog(paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar os detalhes de um log do GymPass",
            notes = "Consulta as informações detalhadas de um log GymPass pelo código dele.",
            tags = "Logs"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaDetalheLogGymPassTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "log/gympass/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGympassDetalhe(
            @ApiParam(value = "Código identificador do log que será consultado", required = true, defaultValue = "1")
            @PathVariable Integer id) throws JSONException {
        try {
            return ResponseEntityFactory.ok(bookingGymPassService.listarLogDetalhe(id));
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Cadastrar aula coletiva (V2)",
            notes = "Cadastra uma aula coletiva no sistema. Essa é a segunda versão do endpoint para cadastro de aula coletiva, por favor utilize ele ao invés do V1.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaColetivaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/v2/criar-aula", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroAulaV2(
            @ApiParam(value = "Código da empresa que a aula será vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Informações para cadastro da aula")
            @RequestBody AulaColetivaResponseDTO aulaDTO) {
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAulaV2(aulaDTO, null, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Cadastrar aula",
            notes = "Cadastra uma aula no sistema.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastroAula(
            @ApiParam(value = "Código da empresa que a aula será vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
            @ApiParam(value = "Informações para cadastro da aula")
            @RequestBody AulaDTO aulaDTO) {
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAula(aulaDTO, null, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Deletar aula",
            notes = "Remove uma aula no sistema.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses(
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    )
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAula(
            @ApiParam(value = "Código identificador da aula que será excluída", required = true, defaultValue = "1")
            @PathVariable("id") Integer id) {
        try {
            aulaService.removerAula(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover a aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar aula coletiva",
            notes = "Consulta as informações de uma aula coletiva.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaColetivaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "obter-aula-coletiva/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAulaColetiva(
            @ApiParam(value = "Código da aula que será consultada", defaultValue = "1", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Código da empresa que a aula está vinculada", defaultValue = "1")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aulaService.detalhesAulaColetiva(id, empresaId, null));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da aula coletiva", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar horários de uma aula coletiva",
            notes = "Consulta os horários de uma aula coletiva.",
            tags = "Agenda de Aulas"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.\n\n Ordena por um atributo contido no objeto da resposta.\n\n" +
                    "<strong>Ordens disponíveis</strong>\n\n" +
                    "- <strong>ASC</strong> - Ordena pelo atributo escolhido de forma ascendente\n" +
                    "- <strong>DESC</strong> - Ordena pelo atributo escolhido de forma descendente\n\n" +
                    "Deve ser informado como atributo,ordem", defaultValue = "codigo,desc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListHorarioTurmaResponseDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "obter-horarios/{codigoAula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterHorariosAulaColetiva(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>professor:</strong> Filtra por um ou mais professores (Deve ser informado como uma lista de códigos/IDs ex: [1, 2]).\n" +
                    "- <strong>ambiente:</strong> Filtra por um ou mais ambientes (Deve ser informado como uma lista de códigos/IDs ex: [10, 15]).\n" +
                    "- <strong>nivelTurma:</strong> Filtra por um ou mais níveis de turma (Deve ser informado como uma lista de códigos/IDs ex: [3, 4]).\n" +
                    "- <strong>dias:</strong> Filtra por um ou mais dias da semana (Deve ser informado como uma lista ex: [\"SEGUNDA\", \"TERCA\"]).\n" +
                    "- <strong>situacao:</strong> Filtra pela situação do horário (Deve ser informado como uma lista ex: [\"ATIVO\", \"INATIVO\"]).\n" +
                    "- <strong>quicksearchValue:</strong> Filtra pelo horário de início ou fim da turma (ex: \"08:00\").",
                    defaultValue = "{\"professor\":[1], \"ambiente\":[10, 15], \"nivelTurma\":[3], \"dias\":[\"SEGUNDA\"], \"situacao\":[\"ATIVO\"], \"quicksearchValue\":\"08:00\"}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO,
            @ApiParam(value = "Código da aula que será consultado os horários", defaultValue = "1", required = true)
            @PathVariable Integer codigoAula) throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.obterHorariosAulaColetiva(filtros, paginadorDTO, codigoAula), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os horários da aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Validar existência de alunos em horário de aula coletiva",
            notes = "Valida a existência de alunos em um horário de aula coletiva. Irá retornar true se o horário existir e false se não existir.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/horario-aula/validar/{codigoHorario}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> validarHorarioAula(@ApiParam(value = "Código do horário de aula que será consultado", defaultValue = "1", required = true)
                                                                  @PathVariable Integer codigoHorario) throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.existeAlunosHorarioAulaColetivaFutura(codigoHorario));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar validar se existem alunos futuros no horário", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Consultar detalhes de uma aula",
            notes = "Consulta as informações detalhadas de uma aula.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAula(@ApiParam(value = "Código da aula que será consultada", required = true, defaultValue = "1")
                                                            @PathVariable("id") Integer id,
                                                            @ApiParam(value = "Código da empresa que a aula está vinculada", defaultValue = "1", required = true)
                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aulaService.detalhesAula(id, empresaId, null));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da aula", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar detalhes de uma aula pelo dia que ela será ministrada",
            notes = "Consulta as informações detalhadas de uma aula pelo dia em que ela será ministrada.\n" +
                    "Este endpoint é útil para aulas recorrentes, quando se deseja consultar apenas as informações da aula na data em que será ministrada.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "horario/{id}/{dataAula}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAulaHorario(@ApiParam(value = "Código da aula que será consultada", required = true, defaultValue = "1")
                                                                   @PathVariable("id") Integer id,
                                                                   @ApiParam(value = "Data que a aula ocorrerá (Padrão: yyyyMMdd)", required = true, defaultValue = "20250610")
                                                                   @PathVariable("dataAula") String dataAula,
                                                                   @ApiParam(value = "Código da empresa que a aula está vinculada", defaultValue = "1", required = true)
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            AulaResponseDTO editada = aulaService.edicaoAulaTemporaria(id, dataAula);
            if (editada == null) {
                return ResponseEntityFactory.ok(aulaService.detalhesAula(null, empresaId, id));
            }
            return ResponseEntityFactory.ok(editada);
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter os detalhes da aula", e);
            return ResponseEntityFactory.erroRegistroDuplicado("mensagem_nao_tratada", e.getMessage());
        }
    }


    @ApiOperation(
            value = "Atualizar aula coletiva (V2)",
            notes = "Atualiza as informações de uma aula coletiva. Esta é a segunda versão deste endpoint, utilize-o em vez da versão V1.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaColetivaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "v2/atualizar/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAulaV2(@ApiParam(value = "Código da aula que será atualizada", required = true, defaultValue = "1")
                                                             @PathVariable("id") Integer codigoAula,
                                                             @ApiParam(value = "Informações para atualização da aula")
                                                             @RequestBody AulaColetivaResponseDTO aulaDTO,
                                                             @ApiParam(value = "Código da empresa que a aula está vinculada", defaultValue = "1", required = true)
                                                             @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAulaV2(aulaDTO, codigoAula, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Criar ou atualizar horários de aulas",
            notes = "Cria ou atualiza horários de aulas. É necessário enviar uma lista de horários.\n\n" +
                    "Para criar é necessário enviar os horários sem o codigo, " +
                    "para atualizar é necessário enviar o atributo codigo com o valor do horário que terá as informações atualizados.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListHorarioTurmaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/save-or-update-horarios", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdateHorarios(@ApiParam(value = "Horários de aula que serão atualizados ou criados.")
                                                                    @RequestBody List<HorarioTurmaResponseDTO> horarioDTO) {
        try {
            return ResponseEntityFactory.ok(aulaService.saveOrUpdateHorarios(horarioDTO));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar salvar ou alterar horário da aula coletiva", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Deletar horário de aula",
            notes = "Exclui as informações de um horário de aula.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaRemoverHorarioTurma.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/remover-horario/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerHorarioTurma(@ApiParam(value = "Código do horário que será excluído", required = true, defaultValue = "1")
                                                                   @PathVariable Integer id,
                                                                   @ApiParam(value = "Código da empresa que o horário está vinculado", defaultValue = "1", required = true)
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(aulaService.removerHorarioAula(id, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(TurmasController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover horario da aula coletiva", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(
            value = "Atualizar aula",
            notes = "Atualiza as informações de uma aula.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAula(@ApiParam(value = "Código da aula que será atualizada", defaultValue = "1", required = true)
                                                           @PathVariable("id") Integer id,
                                                           @ApiParam(value = "Informações para atualizar a aula")
                                                           @RequestBody AulaDTO aulaDTO,
                                                           @ApiParam(value = "Código da empresa que a aula está vinculada", defaultValue = "1", required = true)
                                                           @RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(aulaService.cadastroAula(aulaDTO, id, empresaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Alterar aula temporariamente",
            notes = "Altera as informações de uma aula de forma temporária.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaEdicaoAulaTemporaria.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}/temporaria/{dataAula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> alterarAulaTemporariamente(@ApiParam(value = "Código da aula que será alterada", defaultValue = "1", required = true)
                                                                          @PathVariable("id") Integer id,
                                                                          @ApiParam(value = "Data que a aula ocorrerá (Formato: yyyyMMdd)", defaultValue = "20250610", required = true)
                                                                          @PathVariable("dataAula") String dataAula,
                                                                          @ApiParam(value = "Informações da aula que será atualizada temporariamente")
                                                                          @RequestBody AulaDTO aulaDTO) {
        try {
            return ResponseEntityFactory.ok(aulaService.editarAulaTemporariamente(aulaDTO, id, dataAula));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Replicar aula temporariamente",
            notes = "Replica as informações de uma aula de forma temporária.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaEdicaoAulaTemporaria.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "{id}/replicar/{dataAula}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarAulaTemporariamente(@ApiParam(value = "Código da aula que será replicada", defaultValue = "1", required = true)
                                                                           @PathVariable("id") Integer id,
                                                                           @ApiParam(value = "Data que a aula ocorrerá (Formato: yyyyMMdd)", defaultValue = "20250610", required = true)
                                                                           @PathVariable("dataAula") String dataAula,
                                                                           @ApiParam(value = "Informações da aula que será replicada temporariamente")
                                                                           @RequestBody AulaDTO aulaDTO) {
        try {
            return ResponseEntityFactory.ok(aulaService.replicarAulaTemporariamente(aulaDTO, id, dataAula));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar alterar a aula", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Consultar TV Aulas",
            notes = "Consulta as informações das aulas no formato TV Aula.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListTVAulaDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "tv-aula", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> aulasParaTvAula(@ApiParam(value = "Código da empresa que as aulas estão vinculadas", required = true, defaultValue = "1")
                                                               @RequestHeader("empresaId") Integer empresaId,
                                                               @ApiIgnore
                                                               HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(aulaService.listaAulasHorario(empresaId, request));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar aulas para a tv aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar uma TV Aula",
            notes = "Consulta as informações de uma aula no formato TV Aula.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListTVAulaDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "tv-aula/{horarioAulaId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> detalhesAulaParaTvAula(@ApiParam(value = "Código da empresa que a aula está vinculada", defaultValue = "1", required = true)
                                                                      @RequestHeader("empresaId") Integer empresaId,
                                                                      @ApiParam(value = "Código do horário da aula que será consultada", defaultValue = "1", required = true)
                                                                      @PathVariable("horarioAulaId") Integer horarioAulaId,
                                                                      @ApiIgnore HttpServletRequest request) {
        try {
            return ResponseEntityFactory.ok(aulaService.detalhesAulaHorario(request, empresaId, horarioAulaId));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar obter detalhes da aula", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Reseta as informações de uma aula GymPass",
            notes = "Reseta as informações de uma aula GymPass.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida). Não retorna corpo de resposta.", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "reset/gympass/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reset(@ApiParam(value = "Código da aula que será resetada", defaultValue = "1", required = true)
                                                     @PathVariable Integer id,
                                                     @ApiParam(value = "Código identificador da empresa que a aula está vinculada", required = true, defaultValue = "1")
                                                     @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            aulaService.resetAula(id, empresaId);
            return ResponseEntityFactory.ok();
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar logs do booking", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar produtos do GymPass",
            notes = "Consulta os produtos do GymPass.",
            tags = "Produto"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListProductDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "gympass/listar-produtos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarProdutosGympass(
            @ApiParam(value = "Código identificador da empresa que o produto está vinculado", required = true, defaultValue = "true")
            @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws JSONException {
        try {
            return ResponseEntityFactory.ok(gymPassBookingService.listarProdutos(empresaId));
        } catch (Exception e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar produtos gympass", e);
            return ResponseEntityFactory.erroInterno("erro_log", e.getMessage());
        }
    }


    @ApiOperation(
            value = "Replicar aula",
            notes = "Clona as informações de uma aula.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/clonar-aula/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> clonarAula(@ApiParam(value = "Código da aula que será clonada", defaultValue = "1", required = true)
                                                          @PathVariable("id") Integer codigoAulaOriginal,
                                                          @ApiParam(value = "Código da empresa no ZW que a aula está vinculada", defaultValue = "1", required = true)
                                                          @RequestHeader(value = "empresaId", required = true) Integer empresaIdZw) {
        try {
            return ResponseEntityFactory.ok(aulaService.clonarAula(codigoAulaOriginal, empresaIdZw));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar clonar a aula", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Inserir aluno na fila de espera de uma aula",
            notes = "Insere um aluno na fila de espera de uma aula.",
            tags = "Agenda de Aulas"
    )
    @ResponseBody
    @RequestMapping(value = "/inserirNaFila", method = RequestMethod.POST)
    public ModelMap inserirNaFila(@ApiParam(value = "Código do horário da aula que o aluno será inserido na fila de espera", defaultValue = "1")
                                  @RequestParam final Integer codigoHorarioTurma,
                                  @ApiParam(value = "Dia que a aula ocorrerá (Formato: yyyyMMdd)", defaultValue = "20250610")
                                  @RequestParam final String dia,
                                  @ApiParam(value = "Código do aluno que será inserido na fila de espera", defaultValue = "3")
                                  @RequestParam final Integer codigoAluno) throws ServiceException {
        ModelMap mm = new ModelMap();
        String retorno = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);

        try {
            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            retorno = agendaTotalService.inserirNaFilaDeEspera(ctx, codigoHorarioTurma, dia, codigoAluno);
            if (retorno.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, retorno);
            } else {
                mm.addAttribute("Fila", retorno);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }


    @ApiOperation(
            value = "Remover aluno da fila de espera de uma aula (v1)",
            notes = "Remove um aluno da fila de espera de uma aula. Essa é a primeira versão desse recurso. " +
                    "Recomendamos que utilize o segunda versão do recurso.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaResponseDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/removerDaFila", method = RequestMethod.DELETE)
    public ModelMap removerDaFila(@ApiParam(value = "Código do horário da aula que o aluno será inserido na fila de espera", defaultValue = "1")
                                  @RequestParam final Integer codigoHorarioTurma,
                                  @ApiParam(value = "Dia que a aula ocorrerá (Formato: yyyyMMdd)", defaultValue = "20250610")
                                  @RequestParam final String dia,
                                  @ApiParam(value = "Código do aluno que será inserido na fila de espera", defaultValue = "3")
                                  @RequestParam final Integer codigoAluno) {
        ModelMap mm = new ModelMap();
        String result = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            result = agendaTotalService.removerDaFilaDeEspera(ctx, codigoHorarioTurma, dia, codigoAluno);
            if (result.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Remover aluno da fila de espera de uma aula (V2)",
            notes = "Remove um aluno da fila de espera de uma aula. Essa é a segunda versão desse recurso. " +
                    "Recomendamos que você utilize essa versão ao invés de utilizar a primeira.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAulaResponseDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/removerDaFilaV2", method = RequestMethod.DELETE)
    public ModelMap removerDaFilaV2(@ApiParam(value = "Código do horário da aula que o aluno será inserido na fila de espera", defaultValue = "1")
                                    @RequestParam final Integer codigoHorarioTurma,
                                    @ApiParam(value = "Dia que a aula ocorrerá (Formato: yyyyMMdd)", defaultValue = "20250610")
                                    @RequestParam final String dia,
                                    @ApiParam(value = "Código do aluno que será inserido na fila de espera", defaultValue = "3")
                                    @RequestParam final Integer codigoAluno,
                                    @ApiParam(value = "Código do usuário que adicionará o cliente a fila de espera")
                                    @RequestParam(required = false) final Integer codigoUsuario) {
        ModelMap mm = new ModelMap();
        String result = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            result = agendaTotalService.removerDaFilaDeEsperaV2(ctx, codigoHorarioTurma, dia, codigoAluno, codigoUsuario);
            if (result.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar fila de espera de uma aula",
            notes = "Consulta as informações de uma fila de espera de uma aula. As informações incluem os clientes que estão na fila de espera, horário da aula e outras informações.",
            tags = "Agenda de Aulas"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListFilaDeEsperaDTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/consultar-fila-espera/{codigoHorarioTurma}/{dia}", method = RequestMethod.GET)
    public ResponseEntity<EnvelopeRespostaDTO> consultarFila(@ApiParam(value = "Código do horário da turma", required = true, defaultValue = "1")
                                                             @PathVariable final Integer codigoHorarioTurma,
                                                             @ApiParam(value = "Dia que a aula será ministrada (Formato: yyyyMMdd)", defaultValue = "20250610", required = true)
                                                             @PathVariable final String dia,
                                                             @ApiIgnore PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<FilaDeEsperaDTO> filaEspera = new ArrayList<>();
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfiguracaoSistemaService css = UtilContext.getBean(ConfiguracaoSistemaService.class);
            ConfiguracaoSistema cfg = css.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);
            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            filaEspera = aulaService.consultarFilaEspera(codigoHorarioTurma, dia);
            paginadorDTO.setQuantidadeTotalElementos(1l);
            return ResponseEntityFactory.ok(filaEspera, paginadorDTO);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Ordenar aluno em uma fila de espera de uma aula",
            notes = "Ordena um aluno em uma fila de espera de aula. A ordem pode ser realizada de forma crescente ou decrescente. Também é possível inserir o aluno numa posição específica da fila.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListFilaDeEsperaDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/ordenar-fila-espera", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ordenarFilaEspera(
            @ApiParam(value = "Código do horário da turma que a fila de espera está vincualda", defaultValue = "1")
            @RequestParam final Integer codigoHorarioTurma,

            @ApiParam(value = "Dia em que a aula ocorrerá, no formato 'YYYYMMDD'.", defaultValue = "20250610", required = true)
            @RequestParam final String dia,

            @ApiParam(value = "Matrícula do aluno que será movido na fila de espera.", defaultValue = "1234", required = true)
            @RequestParam final Integer matricula,

            @ApiParam(value = "Define a ação de ordenação. Valores possíveis: 'incremento' (sobe 1 posição), 'decremento' (desce 1 posição), 'input' (move para posição específica).",
                    allowableValues = "incremento,decremento,input", defaultValue = "input")
            @RequestParam(required = false) final String tipoOrdenacao,

            @ApiParam(value = "A nova posição do aluno na fila. Obrigatório apenas quando tipoOrdenacao for 'input'.", defaultValue = "1")
            @RequestParam(required = false) final Integer novaOrdem
    ) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfiguracaoSistema cfg = configService.consultarPorTipo(ctx, ConfiguracoesEnum.HABILITAR_FILA_ESPERA);

            if (!cfg.getValorAsBoolean()) {
                throw new ServiceException(AgendaExcecoes.FILA_DE_ESPERA_DESABILITADA.name());
            }
            List<FilaDeEsperaDTO> filaOrdenada = aulaService.ordenarFilaEspera(codigoHorarioTurma, dia, matricula, tipoOrdenacao, novaOrdem);
            return ResponseEntityFactory.ok(filaOrdenada);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }


    @ApiOperation(
            value = "Remover aluno da fila de espera de uma aula (Módulo CRM)",
            notes = "Remove o aluno da fila de espera de uma aula. Esse recurso faz parte do módulo CRM.",
            tags = "Agenda de Aulas"
    )
    @ResponseBody
    @RequestMapping(value = "/removerDaFilaTurmaCrm", method = RequestMethod.DELETE)
    public ModelMap removerDaFila(@ApiParam(value = "Código do horário da aula", defaultValue = "1")
                                  @RequestParam final Integer codigoHorarioTurma,
                                  @ApiParam(value = "Código do aluno que será removido da fila de espera", defaultValue = "1")
                                  @RequestParam(required = false) final Integer codigoAluno,
                                  @ApiParam(value = "Código do passivo", defaultValue = "1")
                                  @RequestParam(required = false) final Integer passivo) {
        ModelMap mm = new ModelMap();
        String result = "";
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            result = agendaTotalService.removerDaFilaDeEsperaTurmaCrm(ctx, codigoHorarioTurma, codigoAluno, passivo);
            if (result.contains("Erro")) {
                return mm.addAttribute(STATUS_ERRO, result);
            } else {
                mm.addAttribute("Fila", result);
            }
        } catch (Exception ex) {
            mm.addAttribute(STATUS_ERRO, ex.getMessage());
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mm;
    }

    @ApiOperation(
            value = "Consultar fila de espera de uma aula (CRM)",
            notes = "Consulta as informações de uma fila de espera de uma aula. Esse recurso faz parte do módulo CRM. As informações incluem os clientes que estão na fila de espera, horário da aula e outras informações.",
            tags = "Agenda de Aulas"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListFilaDeEsperaDTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/consultar-fila-espera-turma-crm/{codigoHorarioTurma}", method = RequestMethod.GET)
    public ResponseEntity<EnvelopeRespostaDTO> consultarFilaTurmaCrm(@ApiParam(value = "Código do horário da turma", defaultValue = "1", required = true)
                                                                     @PathVariable final Integer codigoHorarioTurma,
                                                                     @ApiIgnore PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<FilaDeEsperaDTO> filaEspera;
            filaEspera = aulaService.consultarFilaEsperaTurmaCrm(codigoHorarioTurma);
            paginadorDTO.setQuantidadeTotalElementos(1L);
            return ResponseEntityFactory.ok(filaEspera, paginadorDTO);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Ordenar fila de espera de uma aula (CRM)",
            notes = "Ordena um aluno em uma fila de espera de aula. A ordem pode ser realizada de forma crescente ou decrescente. Também é possível inserir o aluno numa posição específica da fila.\n\n Esse recurso faz parte do módulo CRM.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListFilaDeEsperaDTOPaginacao.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/ordenar-fila-espera-turma-crm", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ordenarFilaEspera(
            @ApiParam(value = "Código do horário da turma que a fila de espera está vincualda", defaultValue = "1")
            @RequestParam final Integer codigoHorarioTurma,

            @ApiParam(value = "Matrícula do aluno que será movido na fila de espera.", defaultValue = "1234")
            @RequestParam(required = false) final Integer matricula,

            @ApiParam(value = "Código do passivo", defaultValue = "1")
            @RequestParam(required = false) final Integer passivo,

            @ApiParam(value = "Define a ação de ordenação. Valores possíveis: 'incremento' (sobe 1 posição), 'decremento' (desce 1 posição), 'input' (move para posição específica).",
                    allowableValues = "incremento,decremento,input", defaultValue = "input")
            @RequestParam(required = false) final String tipoOrdenacao,

            @ApiParam(value = "A nova posição do aluno na fila. Obrigatório apenas quando tipoOrdenacao for 'input'.", defaultValue = "1")
            @RequestParam(required = false) final Integer novaOrdem
    ) {
        try {
            List<FilaDeEsperaDTO> filaOrdenada = aulaService.ordenarFilaEsperaTurmaCrm(codigoHorarioTurma, matricula, passivo, tipoOrdenacao, novaOrdem);
            return ResponseEntityFactory.ok(filaOrdenada);
        } catch (ServiceException ex) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, null, ex);
            return ResponseEntityFactory.erroInterno(ex.getChaveExcecao(), ex.getMessage());
        }
    }

    @ApiOperation(
            value = "Inserir aluno na fila de espera de uma aula (CRM)",
            notes = "Insere um aluno na fila de espera de uma aula.\n\n Esse recurso faz parte do módulo CRM.",
            tags = "Agenda de Aulas"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaInseridoNaFila.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.AGENDA)
    @RequestMapping(value = "/inserirNaFilaTurmaCrm", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inserirNaFilaTurmaCrm(@ApiParam(value = "Código do horário da aula que o aluno será inserido na fila de espera", defaultValue = "1")
                                                                     @RequestParam Integer codigoHorarioTurma,
                                                                     @ApiParam(value = "Código do aluno que será inserido na fila de espera", defaultValue = "1")
                                                                     @RequestParam(value = "codigoAluno", required = false) Integer codigoAluno,
                                                                     @ApiParam(value = "Código do Passivo", defaultValue = "1")
                                                                     @RequestParam(value = "codigoPassivo", required = false) Integer codigoPassivo,
                                                                     @ApiParam(value = "Passivo")
                                                                     @RequestBody String passivo) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            PassivoDTO passivoDTO = null;
            try {
                passivoDTO = JSONMapper.getObject(new JSONObject(passivo), PassivoDTO.class);
                passivoDTO.setResponsavelCadastro(sessaoService.getUsuarioAtual().getId());
                passivoDTO.setEmpresa(sessaoService.getUsuarioAtual().getEmpresaAtual());
            } catch (Exception ignore) {
            }

            return ResponseEntityFactory.ok(agendaTotalService.inserirNaFilaDeEsperaTurmaCrm(ctx, codigoHorarioTurma, codigoAluno, passivoDTO, codigoPassivo));
        } catch (ServiceException e) {
            Logger.getLogger(AulasController.class.getName()).log(Level.SEVERE, "Erro ao tentar inserir na fila de turma crm", e);
            if (e.getCause() == null || e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }


}
