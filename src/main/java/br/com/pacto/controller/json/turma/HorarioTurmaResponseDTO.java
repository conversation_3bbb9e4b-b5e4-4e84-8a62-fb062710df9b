package br.com.pacto.controller.json.turma;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Detalhes do horário da turma")
public class HorarioTurmaResponseDTO {
    @ApiModelProperty(value = "Código identificador do horário", example = "101")
    private Integer codigo;
    @ApiModelProperty(value = "Nome do professor", example = "<PERSON>")
    private String professor;
    @ApiModelProperty(value = "Identificador da turma", example = "TURMA123")
    private String identificadorTurma;
    @ApiModelProperty(value = "Código identificador do professor", example = "12")
    private Integer professorId;
    @ApiModelProperty(value = "Código identificador da turma", example = "345")
    private Integer turma;
    @ApiModelProperty(value = "Nome do ambiente onde a aula será realizada", example = "Sala A")
    private String ambiente;
    @ApiModelProperty(value = "Código identificador do ambiente", example = "5")
    private Integer ambienteId;
    @ApiModelProperty(value = "Descrição do horário da turma", example = "Segunda a sexta, das 7h às 8h")
    private String horarioTurma;
    @ApiModelProperty(value = "Hora de início da aula", example = "07:00")
    private String horaInicial;
    @ApiModelProperty(value = "Hora de término da aula", example = "08:00")
    private String horaFinal;
    @ApiModelProperty(value = "Duração da aula", example = "60 minutos")
    private String duracao;
    @ApiModelProperty(value = "Dia da aula", example = "20250610")
    private String dia;
    @ApiModelProperty(value = "Número máximo de alunos permitidos", example = "20")
    private Integer maxAlunos;
    @ApiModelProperty(value = "Tolerância antes da aula em minutos", example = "10")
    private Integer toleranciaMin;
    @ApiModelProperty(value = "Tolerância após o início da aula em minutos", example = "5")
    private Integer toleranciaAposMin;
    @ApiModelProperty(value = "Nível da turma", example = "Intermediário")
    private String nivelTurma;
    @ApiModelProperty(value = "Situação atual do horário", example = "Ativo")
    private String situacao;
    @ApiModelProperty(value = "Código identificador do nível da turma", example = "2")
    private Integer nivelTurmaId;
    @ApiModelProperty(value = "Número do dia da semana (1 = Domingo, 7 = Sábado)", example = "2")
    private Integer diaSemanaNumero;
    @ApiModelProperty(value = "Indica se o horário está disponível para venda", example = "true")
    private Boolean horarioDisponivelVenda;
    @ApiModelProperty(value = "Indica se a marcação no app está liberada", example = "true")
    private Boolean liberadoMarcacaoApp;
    @ApiModelProperty(value = "Data que entrou na turma", example = "2025-01-10")
    private String dataEntrouTurma;
    @ApiModelProperty(value = "Data que saiu da turma", example = "2025-05-10")
    private String dataSaiuTurma;
    @ApiModelProperty(value = "Número de alunos que entraram por reposição", example = "3")
    private Integer nrAlunoEntraramPorReposicao;
    @ApiModelProperty(value = "Número de alunos que saíram por reposição", example = "1")
    private Integer nrAlunoSairamPorReposicao;
    @ApiModelProperty(value = "Limite de vagas agregadas", example = "5")
    private Integer limiteVagasAgregados;
    @ApiModelProperty(value = "Lista das capacidades por categoria do horário")
    private List<HorarioCapacidadeCategoriaDTO> horarioCapacidadeCategoria;
    @ApiModelProperty(value = "Quantidade máxima de alunos experimentais permitidos", example = "3")
    private Integer qtdeMaximaAlunoExperimental;

    public HorarioTurmaResponseDTO() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(String horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public String getDuracao() {
        return duracao;
    }

    public void setDuracao(String duracao) {
        this.duracao = duracao;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public Integer getMaxAlunos() {
        return maxAlunos;
    }

    public void setMaxAlunos(Integer maxAlunos) {
        this.maxAlunos = maxAlunos;
    }

    public Integer getToleranciaMin() {
        return toleranciaMin;
    }

    public void setToleranciaMin(Integer toleranciaMin) {
        this.toleranciaMin = toleranciaMin;
    }

    public Integer getToleranciaAposMin() {
        return toleranciaAposMin;
    }

    public void setToleranciaAposMin(Integer toleranciaAposMin) {
        this.toleranciaAposMin = toleranciaAposMin;
    }

    public String getNivelTurma() {
        return nivelTurma;
    }

    public void setNivelTurma(String nivelTurma) {
        this.nivelTurma = nivelTurma;
    }

    public Integer getProfessorId() {
        return professorId;
    }

    public void setProfessorId(Integer professorId) {
        this.professorId = professorId;
    }

    public Integer getAmbienteId() {
        return ambienteId;
    }

    public void setAmbienteId(Integer ambienteId) {
        this.ambienteId = ambienteId;
    }

    public Integer getNivelTurmaId() {
        return nivelTurmaId;
    }

    public void setNivelTurmaId(Integer nivelTurmaId) {
        this.nivelTurmaId = nivelTurmaId;
    }

    public String getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(String horaInicial) {
        this.horaInicial = horaInicial;
    }

    public String getHoraFinal() {
        return horaFinal;
    }

    public void setHoraFinal(String horaFinal) {
        this.horaFinal = horaFinal;
    }

    public Boolean getHorarioDisponivelVenda() {
        if (this.horarioDisponivelVenda == null) {
            return Boolean.FALSE;
        }
        return horarioDisponivelVenda;
    }

    public void setHorarioDisponivelVenda(Boolean horarioDisponivelVenda) {
        this.horarioDisponivelVenda = horarioDisponivelVenda;
    }

    public Boolean getLiberadoMarcacaoApp() {
        if (this.liberadoMarcacaoApp == null) {
            return Boolean.FALSE;
        }
        return liberadoMarcacaoApp;
    }

    public void setLiberadoMarcacaoApp(Boolean liberadoMarcacaoApp) {
        this.liberadoMarcacaoApp = liberadoMarcacaoApp;
    }

    public String getIdentificadorTurma() {
        return identificadorTurma;
    }

    public void setIdentificadorTurma(String identificadorTurma) {
        this.identificadorTurma = identificadorTurma;
    }

    public Integer getTurma() {
        return turma;
    }

    public void setTurma(Integer turma) {
        this.turma = turma;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getDiaSemanaNumero() {
        return diaSemanaNumero;
    }

    public void setDiaSemanaNumero(Integer diaSemanaNumero) {
        this.diaSemanaNumero = diaSemanaNumero;
    }

    public String getDataEntrouTurma() {
        return dataEntrouTurma;
    }

    public void setDataEntrouTurma(String dataEntrouTurma) {
        this.dataEntrouTurma = dataEntrouTurma;
    }

    public String getDataSaiuTurma() {
        return dataSaiuTurma;
    }

    public void setDataSaiuTurma(String dataSaiuTurma) {
        this.dataSaiuTurma = dataSaiuTurma;
    }

    public Integer getNrAlunoEntraramPorReposicao() {
        return nrAlunoEntraramPorReposicao;
    }

    public void setNrAlunoEntraramPorReposicao(Integer nrAlunoEntraramPorReposicao) {
        this.nrAlunoEntraramPorReposicao = nrAlunoEntraramPorReposicao;
    }

    public Integer getNrAlunoSairamPorReposicao() {
        return nrAlunoSairamPorReposicao;
    }

    public void setNrAlunoSairamPorReposicao(Integer nrAlunoSairamPorReposicao) {
        this.nrAlunoSairamPorReposicao = nrAlunoSairamPorReposicao;
    }

    public String getDiaSemana_Apresentar() {
        if (this.dia == null) {
            this.dia = "";
        }
        if (this.dia.equals("DM")) {
            return "Domingo";
        }
        if (this.dia.equals("SG")) {
            return "Segunda";
        }
        if (this.dia.equals("TR")) {
            return "Terça";
        }
        if (this.dia.equals("QA")) {
            return "Quarta";
        }
        if (this.dia.equals("QI")) {
            return "Quinta";
        }
        if (this.dia.equals("SX")) {
            return "Sexta";
        }
        if (this.dia.equals("SB")) {
            return "Sábado";
        }
        return (this.dia);
    }

    public Integer getLimiteVagasAgregados() {
        return limiteVagasAgregados;
    }

    public void setLimiteVagasAgregados(Integer limiteVagasAgregados) {
        this.limiteVagasAgregados = limiteVagasAgregados;
    }


    public List<HorarioCapacidadeCategoriaDTO> getHorarioCapacidadeCategoria() {
        return horarioCapacidadeCategoria;
    }

    public void setHorarioCapacidadeCategoria(List<HorarioCapacidadeCategoriaDTO> horarioCapacidadeCategoria) {
        this.horarioCapacidadeCategoria = horarioCapacidadeCategoria;
    }

    public Integer getQtdeMaximaAlunoExperimental() {
        return qtdeMaximaAlunoExperimental;
    }

    public void setQtdeMaximaAlunoExperimental(Integer qtdeMaximaAlunoExperimental) {
        this.qtdeMaximaAlunoExperimental = qtdeMaximaAlunoExperimental;
    }
}
