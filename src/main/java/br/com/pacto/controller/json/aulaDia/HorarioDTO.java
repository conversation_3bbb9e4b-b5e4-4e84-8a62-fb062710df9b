package br.com.pacto.controller.json.aulaDia;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do horário")
public class HorarioDTO {
    //horarios: [{inicio: "12:00", fim: "13:00"}, {inicio: "14:00", fim: "15:00"}]
    @ApiModelProperty(value = "Horário de início", example = "19:00")
    private String inicio;
    @ApiModelProperty(value = "Horário de término", example = "200:00")
    private String fim;
    @ApiModelProperty(value = "Código identificador do horário", example = "1")
    private Integer id;

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
