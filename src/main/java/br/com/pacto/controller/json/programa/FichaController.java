package br.com.pacto.controller.json.programa;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.ficha.FichaDTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.controller.json.atividade.FiltroFichaPredefinidaJSON;
import br.com.pacto.objeto.to.AtividadeFichaEndpointTO;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.ficha.FichaService;
import br.com.pacto.service.intf.programa.ProgramaTreinoService;
import br.com.pacto.swagger.respostas.ExemploRespostaVazia;
import br.com.pacto.swagger.respostas.programa.*;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 03/08/2018.
 */
@Controller
@RequestMapping("/psec/fichas")
public class FichaController {

    private final FichaService fichaService;
    private final ProgramaTreinoService programaTreinoService;

    @Autowired
    public FichaController(FichaService fichaService, ProgramaTreinoService programaTreinoService) {
        Assert.notNull(fichaService, "O serviço de ficha não foi injetado corretamente");
        this.fichaService = fichaService;
        Assert.notNull(programaTreinoService, "O serviço de programa treino não foi injetado corretamente");
        this.programaTreinoService = programaTreinoService;
    }

    @Autowired
    private HttpServletRequest request;

    @ApiOperation(
            value = "Consultar fichas predefinidas",
            notes = "Consulta fichas predefinidas disponíveis no sistema com suporte a filtros e paginação. " +
                    "Permite filtrar por categoria, nome e outros critérios específicos para localizar fichas de treino pré-configuradas.",
            tags = "Ficha Predefinida"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "<strong>Campos disponíveis para ordenação:</strong>" +
                    "<ul>" +
                    "<li>nome - Ordena pelo nome da ficha</li>" +
                    "<li>categoria - Ordena pela categoria da ficha</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFichasPreDefinidas.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/pre-definido", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarFichasPreDefinidas(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n" +
                    "- <strong>quicksearchValue:</strong> Termo de busca para nome da ficha.\n" +
                    "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' será aplicado (Deve ser informado como uma lista ex: [\"nome\"]).\n" +
                    "- <strong>situacaoFichaPreDefinida:</strong> Filtra pela situação da ficha predefinida (Deve ser informado como uma lista ex: [\"true\", \"false\"]).",
                    defaultValue = "{\"quicksearchValue\":\"Iniciante\", \"quicksearchFields\":[\"nome\"], \"situacaoFichaPreDefinida\":[\"true\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiParam(value = "ID da categoria para filtrar as fichas", defaultValue = "1")
            @RequestParam(value = "categoriaId", required = false) Integer categoriaId,
            @ApiParam(value = "Nome da ficha para busca específica", defaultValue = "Iniciante")
            @RequestParam(value = "nome", required = false) String nome,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroFichaPredefinidaJSON filtroFichaPredefinidaJSON = null;
            if (filtros != null) {
                filtroFichaPredefinidaJSON = new FiltroFichaPredefinidaJSON(filtros);
            }
            return ResponseEntityFactory.ok(fichaService.consultarFichasPreDefinidas(filtroFichaPredefinidaJSON, categoriaId, nome, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar os aparelhos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter mensagens recomendadas para fichas",
            notes = "Obtém uma lista de mensagens recomendadas que podem ser utilizadas nas fichas de treino. " +
                    "Essas mensagens servem como sugestões para orientar os alunos durante a execução dos exercícios.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaMensagensRecomendadas.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/mensagens-recomendadas-ficha", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterMensagensRecomendadas() {
        try {
            return ResponseEntityFactory.ok(fichaService.obterMensagensRecomendadas());
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as mensagens recomendadas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Criar nova ficha",
            notes = "Cria uma nova ficha de treino no sistema. Pode ser criada a partir de uma ficha predefinida ou como uma ficha completamente nova. " +
                    "A ficha será associada a um programa de treino específico.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarFicha(
            @ApiParam(value = "ID da ficha predefinida para usar como base", defaultValue = "1")
            @RequestParam(value = "preDefinidoId", required = false) Integer preDefinidoId,
            @ApiParam(value = "Chave de origem da ficha para identificação", defaultValue = "FICHA_ORIGEM_001")
            @RequestParam(value = "chaveOrigemFicha", required = false) String chaveOrigemFicha,
            @ApiParam(value = "Nome personalizado para a nova ficha", defaultValue = "Minha Ficha Personalizada")
            @RequestParam(value = "nomeDaFicha", required = false) String nomeDaFicha,
            @ApiParam(value = "ID do programa de treino ao qual a ficha será associada", defaultValue = "10", required = true)
            @RequestParam(value = "programaId", required = true) Integer programaId) {
        try {
            return ResponseEntityFactory.ok(fichaService.criarFicha(preDefinidoId, programaId, request, chaveOrigemFicha, nomeDaFicha));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao criar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Editar ficha existente",
            notes = "Edita uma ficha de treino existente no sistema. Permite alterar informações como nome, categoria, atividades, " +
                    "tipo de execução e outras configurações da ficha.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarFicha(
            @ApiParam(value = "ID único da ficha a ser editada", defaultValue = "1", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Dados da ficha para atualização", required = true)
            @RequestBody FichaDTO fichaTO) {
        try {
            fichaTO.setId(id);
            return ResponseEntityFactory.ok(fichaService.editarFicha(fichaTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao alterar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Criar ficha predefinida",
            notes = "Cria uma nova ficha predefinida no sistema. Fichas predefinidas servem como modelos que podem ser reutilizados " +
                    "para criar fichas personalizadas para diferentes alunos.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/criarPredefinida", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarPredefinida(
            @ApiParam(value = "Dados da ficha predefinida para criação", required = true)
            @RequestBody FichaDTO fichaTO) {
        try {
            return ResponseEntityFactory.ok(fichaService.criarPredefinida(fichaTO, request));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao alterar uma ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Reordenar atividades da ficha",
            notes = "Reordena as atividades de uma ficha de treino conforme a sequência especificada. " +
                    "Permite reorganizar a ordem de execução dos exercícios na ficha.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}/ordenar-atividades", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reordenarAtividades(
            @ApiParam(value = "ID único da ficha cujas atividades serão reordenadas", defaultValue = "1", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Lista com os IDs das atividades na nova ordem desejada", defaultValue = "[3, 1, 2, 5, 4]", required = true)
            @RequestParam("atividadesIds") List<Integer> atividadesIds) {
        try {
            return ResponseEntityFactory.ok(fichaService.reordenarAtividades(id, atividadesIds));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao reordenar as atividades da ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

//    @ResponseBody
//    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
//    @RequestMapping(value = "/{id}/aplicar-padrao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<EnvelopeRespostaDTO> reordenarAtividades(@PathVariable("id") Integer id,
//                                                                   @RequestBody AtividadeFichaPadraoSeriesTO padroSeriesTO) {
//        try {
//            return ResponseEntityFactory.ok(fichaService.aplicarPadraoSeries(id, padroSeriesTO));
//        } catch (ServiceException e) {
//            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao aplicar padrao as series da ficha", e);
//            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
//        }
//    }

    @ApiOperation(
            value = "Excluir ficha",
            notes = "Exclui uma ficha de treino do sistema. A exclusão é permanente e remove todos os dados associados à ficha.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirFicha(
            @ApiParam(value = "ID único da ficha a ser excluída", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            fichaService.excluirPorId(id, request);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Listar categorias de fichas",
            notes = "Lista todas as categorias de fichas disponíveis no sistema. As categorias são utilizadas para organizar " +
                    "e classificar as fichas de treino por tipo ou modalidade.",
            tags = "Categoria Ficha"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaCategoriasFicha.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.CATEGORIA_FICHAS)
    @RequestMapping(value = "/categorias", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarCategoriaFichas() {
        try {
            return ResponseEntityFactory.ok(fichaService.carregarCategoriaFichas());
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao carregar categoria de fichas", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Padronizar séries das atividades da ficha",
            notes = "Aplica um padrão de séries (repetições, carga, descanso) para atividades selecionadas de uma ficha. " +
                    "Permite configurar múltiplas atividades com os mesmos parâmetros de execução de forma eficiente.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = br.com.pacto.swagger.respostas.ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}/aplicar-padrao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> padronizarSeriesDaAtividadeFicha(
            @ApiParam(value = "ID único da ficha onde será aplicado o padrão", defaultValue = "1", required = true)
            @PathVariable("id") Integer fichaId,
            @ApiParam(value = "Dados do padrão a ser aplicado nas atividades selecionadas", required = true)
            @RequestBody AtividadeFichaEndpointTO atividadeFichaEndpointTO
    ) {
        try {
            programaTreinoService.padronizarSeries(fichaId, atividadeFichaEndpointTO);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao padronizar series das atividades da ficha", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Tornar ficha predefinida",
            notes = "Converte uma ficha comum em uma ficha predefinida, permitindo que ela seja utilizada como modelo " +
                    "para criar novas fichas para outros alunos.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "/{id}/tornar-predefinida", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> tornarFichaPreDefinida(
            @ApiParam(value = "ID único da ficha a ser convertida em predefinida", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            fichaService.criarFichaPredefinida(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(ProgramaTreinoController.class.getName()).log(Level.SEVERE, "Erro ao tentar colocar o ficha como predefinida", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(
            value = "Atualizar situação da ficha",
            notes = "Atualiza a situação de uma ficha predefinida, permitindo ativar ou desativar a ficha no sistema. " +
                    "Fichas desativadas não aparecem nas consultas de fichas disponíveis.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaVazia.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarStituacaoAtividade(
            @ApiParam(value = "ID único da ficha cuja situação será atualizada", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id,
            @ApiParam(value = "Dados da ficha com a nova situação", required = true)
            @RequestBody FichaDTO fichaTO) {
        try {
            fichaTO.setId(id);
            fichaService.atualizarSituacaoFichaPredefinida(fichaTO, id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar ficha predefinida", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter ficha predefinida por ID",
            notes = "Obtém os detalhes completos de uma ficha predefinida específica através do seu ID. " +
                    "Retorna todas as informações da ficha incluindo atividades, séries e configurações.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaFichaResponseTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "obterFichaPredefinida/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterFichaPredefinida(
            @ApiParam(value = "ID único da ficha predefinida a ser consultada", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(fichaService.obterFichaPredefinidaById(id));
        } catch (ServiceException e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar ficha predefinida", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Obter grupos musculares por ficha",
            notes = "Obtém todos os grupos musculares trabalhados pelas atividades de uma ficha específica. " +
                    "Útil para análise da distribuição muscular e planejamento de treinos complementares.",
            tags = "Ficha Predefinida"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaGruposMusculares.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.FICHAS_PRE_DEFINIDAS)
    @RequestMapping(value = "grupos-musculares/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterGruposMuscularesPorFicha(
            @ApiParam(value = "ID único da ficha para consulta dos grupos musculares", defaultValue = "1", required = true)
            @PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(fichaService.consultarGruposMuscularesPorFichaTreino(id));
        } catch (Exception e) {
            Logger.getLogger(FichaController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar grupos musculares por ficha", e);
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }
}
