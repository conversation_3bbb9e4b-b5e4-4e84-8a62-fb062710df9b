package br.com.pacto.controller.json.programa;

import br.com.pacto.controller.json.ficha.FichaDeTreinoGeradaPorIADTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Programa de treino gerado por Inteligência Artificial")
public class ProgramaDeTreinoGeradoPorIADTO {

    @ApiModelProperty(value = "Nome do programa de treino gerado", example = "Programa IA - Hipertrofia Intermediário")
    private String nome;

    @ApiModelProperty(value = "ID único do workout gerado pela IA", example = "workout_12345_abc")
    private String workoutID;

    @ApiModelProperty(value = "Data de início do programa em timestamp", example = "1640995200000")
    private long inicio;

    @ApiModelProperty(value = "Data de término do programa em timestamp", example = "1648771200000")
    private long termino;

    @ApiModelProperty(value = "ID do aluno para quem o programa foi gerado", example = "456")
    private int alunoId;

    @ApiModelProperty(value = "Indica se o programa está em revisão pelo professor", example = "true")
    private boolean emRevisaoProfessor;

    @ApiModelProperty(value = "Indica se o programa foi gerado por IA (sempre true para esta classe)", example = "true")
    private boolean geradoPorIA;

    @ApiModelProperty(value = "Quantidade de dias por semana que o programa deve ser executado", example = "4")
    private Integer diasPorSemana;

    @ApiModelProperty(value = "Lista de fichas de treino que compõem o programa. Cada item é uma referência à classe FichaDeTreinoGeradaPorIADTO")
    private List<FichaDeTreinoGeradaPorIADTO> fichas;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getWorkoutID() {
        return workoutID;
    }

    public void setWorkoutID(String workoutID) {
        this.workoutID = workoutID;
    }

    public long getInicio() {
        return inicio;
    }

    public void setInicio(long inicio) {
        this.inicio = inicio;
    }

    public long getTermino() {
        return termino;
    }

    public void setTermino(long termino) {
        this.termino = termino;
    }

    public int getAlunoId() {
        return alunoId;
    }

    public void setAlunoId(int alunoId) {
        this.alunoId = alunoId;
    }

    public boolean isEmRevisaoProfessor() {
        return emRevisaoProfessor;
    }

    public void setEmRevisaoProfessor(boolean emRevisaoProfessor) {
        this.emRevisaoProfessor = emRevisaoProfessor;
    }

    public boolean isGeradoPorIA() {
        return geradoPorIA;
    }

    public void setGeradoPorIA(boolean geradoPorIA) {
        this.geradoPorIA = geradoPorIA;
    }

    public List<FichaDeTreinoGeradaPorIADTO> getFichas() {
        return fichas;
    }

    public void setFichas(List<FichaDeTreinoGeradaPorIADTO> fichas) {
        this.fichas = fichas;
    }

    public Integer getDiasPorSemana() {
        return diasPorSemana;
    }

    public void setDiasPorSemana(Integer diasPorSemana) {
        this.diasPorSemana = diasPorSemana;
    }
}