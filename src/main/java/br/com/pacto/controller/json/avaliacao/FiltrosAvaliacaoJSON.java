package br.com.pacto.controller.json.avaliacao;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.service.exception.ServiceException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

@ApiModel(description = "Filtros para consulta de avaliações físicas no Business Intelligence, permitindo refinar os resultados por período, profissional e indicadores específicos.")
public class FiltrosAvaliacaoJSON extends SuperJSON {

    @ApiModelProperty(value = "Parâmetro de busca rápida para filtrar por nome do aluno ou matrícula.", example = "João Silva")
    private String parametro;

    @ApiModelProperty(value = "Data de início do período para filtro das avaliações.")
    private Date dataInicio;

    @ApiModelProperty(value = "Data de fim do período para filtro das avaliações.")
    private Date dataFim;

    @ApiModelProperty(value = "Código do professor responsável pelas avaliações.", example = "123")
    private int codProfessor;

    @ApiModelProperty(value = "Indicador específico de avaliação física. Valores possíveis: 'IND_AF_TODAS', 'IND_AF_NOVAS', 'IND_AF_REAVALIACOES', 'IND_AF_PREVISTAS', 'IND_AF_REALIZADAS', 'IND_AF_ATRASADAS', 'IND_AF_FUTURAS', 'IND_AF_ATIVOS_SEM', 'IND_AF_ATIVOS_AVALIACAO_ATRASADA', 'IND_AF_PERDERAM_PERCENTUAL', 'IND_AF_PERDERAM_PESO', 'IND_AF_GANHARAM_MASSA', 'IND_AF_PARQ_POSITIVO', 'IND_AF_OBJETIVOS'.", example = "IND_AF_REALIZADAS")
    private String indicador;

    @ApiModelProperty(value = "Objetivo específico da avaliação física para filtro.", example = "Emagrecimento")
    private String objetivo;

    @ApiModelProperty(value = "Tipo de avaliação para filtro. Valores possíveis: 'TODOS_PROFESSORES', 'ALUNOS_SEM_VINCULO', 'TODOS_PROFESSORES_INATIVOS'.", example = "TODOS_PROFESSORES")
    private String tipoAvaliacao;

    @ApiModelProperty(value = "Código do avaliador físico responsável pelas avaliações.", example = "456")
    private Integer codAvaliador;

    public FiltrosAvaliacaoJSON(JSONObject filters) throws ServiceException {
        if (filters != null) {
            try {
                if(!filters.isNull("quicksearchValue")){
                    parametro = filters.getString("quicksearchValue");
                }
                if(!filters.isNull("dataInicio")){
                    dataInicio = new Date(filters.getLong("dataInicio"));
                }
                if(!filters.isNull("dataFim")){
                    dataFim = new Date(filters.getLong("dataFim"));
                }
                if(!filters.isNull("professorId")){
                    codProfessor = filters.getInt("professorId");
                }
                if(!filters.isNull("indicador")){
                    indicador = filters.getString("indicador");
                }
                if(!filters.isNull("objetivo")){
                    objetivo = filters.getString("objetivo");
                }
                if(!filters.isNull("tipoAvaliacao")){
                    tipoAvaliacao = filters.getString("tipoAvaliacao");
                }

                if(!filters.isNull("avaliadorId")){
                    codAvaliador = filters.getInt("avaliadorId");
                }
            } catch (JSONException e) {
                throw new ServiceException(e);
            }
        }
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public int getCodProfessor() {
        return codProfessor;
    }

    public Integer getCodAvaliador() {
        return codAvaliador;
    }

    public void setCodProfessor(Integer codProfessor) {
        this.codProfessor = codProfessor;
    }

    public String getIndicador() {
        return indicador;
    }

    public void setIndicador(String indicador) {
        this.indicador = indicador;
    }

    public String getParametro() {
        return parametro;
    }

    public void setParametro(String parametro) {
        this.parametro = parametro;
    }

    public String getObjetivo() {
        return objetivo;
    }

    public void setObjetivo(String objetivo) {
        this.objetivo = objetivo;
    }

    public String getTipoAvaliacao() {
        return tipoAvaliacao;
    }

    public void setTipoAvaliacao(String tipoAvaliacao) {
        this.tipoAvaliacao = tipoAvaliacao;
    }
}
