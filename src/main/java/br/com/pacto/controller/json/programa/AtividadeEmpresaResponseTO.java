package br.com.pacto.controller.json.programa;

import br.com.pacto.bean.atividade.AtividadeEmpresa;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by joa<PERSON> moita on 03/10/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da empresa habilitada para uma atividade física")
public class AtividadeEmpresaResponseTO {

    @ApiModelProperty(value = "Código único identificador da empresa habilitada para a atividade", example = "25")
    private Integer id;
    @ApiModelProperty(value = "Identificador personalizado da empresa para a atividade", example = "Academia Pacto - Unidade Centro")
    private String identificador;
    @ApiModelProperty(value = "Informações básicas da empresa")
    private EmpresaBasicaResponseTO empresa;

    public AtividadeEmpresaResponseTO(AtividadeEmpresa ae) {
        this.id = ae.getCodigo();
        this.identificador = ae.getIdentificador();
        this.empresa = new EmpresaBasicaResponseTO(ae.getEmpresa());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public EmpresaBasicaResponseTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaBasicaResponseTO empresa) {
        this.empresa = empresa;
    }

}