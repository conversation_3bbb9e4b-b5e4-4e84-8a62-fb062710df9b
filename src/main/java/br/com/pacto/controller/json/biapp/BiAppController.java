package br.com.pacto.controller.json.biapp;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.controller.json.avaliacao.BIAvalicaoFisicaController;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.intf.gestao.BiAppService;
import br.com.pacto.swagger.respostas.biapp.ExemploRespostaBiAppDTO;
import br.com.pacto.swagger.respostas.biapp.ExemploRespostaUsuariosBiAppPaginacao;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

@Controller
@RequestMapping("/psec/bi-app")
public class BiAppController {

    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private BiAppService biAppService;

    @ApiOperation(
            value = "Consultar dashboard principal do BI App Treino",
            notes = "Retorna os dados consolidados do dashboard de Business Intelligence do aplicativo, incluindo estatísticas de usuários ativos, usuários que utilizam o app, usuários sem app e percentual de adoção.",
            tags = "BI App"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBiAppDTO.class)
    })
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> bi(
            @ApiParam(value = "Código da empresa para consulta dos dados", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.biApp(chave, empresaId, false));
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar dashboard do BI App com recarga forçada",
            notes = "Retorna os dados consolidados do dashboard de Business Intelligence do aplicativo com recarga forçada dos dados, ignorando cache. Inclui estatísticas de usuários ativos, usuários que utilizam o app, usuários sem app e percentual de adoção.",
            tags = "BI App"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBiAppDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/reload", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> biReload(
            @ApiParam(value = "Código da empresa para consulta dos dados", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.biApp(chave, empresaId, true));
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar usuários que utilizam o aplicativo",
            notes = "Retorna a lista paginada de usuários ativos que possuem e utilizam o aplicativo móvel, com possibilidade de filtro por professor e busca rápida.",
            tags = "BI App"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada.", dataType = "long", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página.", dataType = "long", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC'.<br/>" +
                    "<strong>Campos disponíveis para ordenação:</strong><br/>" +
                    "<ul>" +
                    "<li><strong>matricula:</strong> Matrícula do aluno</li>" +
                    "<li><strong>nomeAbreviado:</strong> Nome do aluno</li>" +
                    "<li><strong>nomeProfessor:</strong> Nome do professor responsável</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> 'nomeAbreviado,ASC' ou 'matricula,DESC'",
                    dataType = "string", paramType = "query", defaultValue = "nomeAbreviado,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUsuariosBiAppPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/usam-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> usam(
            @ApiParam(value = "Código do professor para filtrar usuários vinculados", defaultValue = "123")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON. " +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong> Busca rápida por matrícula (número) ou nome do aluno (texto)</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> {\"quicksearchValue\":\"João\"} ou {\"quicksearchValue\":\"12345\"}",
                    defaultValue = "{\"quicksearchValue\":\"João Silva\"}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "Código da empresa para consulta dos dados", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    true,
                    false, false,
                    false, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar usuários ativos totais",
            notes = "Retorna a lista paginada de todos os usuários ativos da empresa, independentemente de utilizarem ou não o aplicativo móvel, com possibilidade de filtro por professor e busca rápida.",
            tags = "BI App"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada.", dataType = "long", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página.", dataType = "long", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC'.<br/>" +
                    "<strong>Campos disponíveis para ordenação:</strong><br/>" +
                    "<ul>" +
                    "<li><strong>matricula:</strong> Matrícula do aluno</li>" +
                    "<li><strong>nomeAbreviado:</strong> Nome do aluno</li>" +
                    "<li><strong>nomeProfessor:</strong> Nome do professor responsável</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> 'nomeAbreviado,ASC' ou 'matricula,DESC'",
                    dataType = "string", paramType = "query", defaultValue = "nomeAbreviado,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUsuariosBiAppPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/ativos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> ativosTotal(
            @ApiParam(value = "Código do professor para filtrar usuários vinculados", defaultValue = "123")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON. " +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong> Busca rápida por matrícula (número) ou nome do aluno (texto)</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> {\"quicksearchValue\":\"João\"} ou {\"quicksearchValue\":\"12345\"}",
                    defaultValue = "{\"quicksearchValue\":\"Maria Santos\"}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "Código da empresa para consulta dos dados", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    false,
                    false, false,
                    true, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar usuários que não utilizam o aplicativo",
            notes = "Retorna a lista paginada de usuários ativos que não possuem ou não utilizam o aplicativo móvel, com possibilidade de filtro por professor e busca rápida.",
            tags = "BI App"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada.", dataType = "long", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página.", dataType = "long", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC'.<br/>" +
                    "<strong>Campos disponíveis para ordenação:</strong><br/>" +
                    "<ul>" +
                    "<li><strong>matricula:</strong> Matrícula do aluno</li>" +
                    "<li><strong>nomeAbreviado:</strong> Nome do aluno</li>" +
                    "<li><strong>nomeProfessor:</strong> Nome do professor responsável</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> 'nomeAbreviado,ASC' ou 'matricula,DESC'",
                    dataType = "string", paramType = "query", defaultValue = "nomeAbreviado,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUsuariosBiAppPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/nao-usam-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> naoUsam(
            @ApiParam(value = "Código do professor para filtrar usuários vinculados", defaultValue = "123")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON. " +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong> Busca rápida por matrícula (número) ou nome do aluno (texto)</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> {\"quicksearchValue\":\"João\"} ou {\"quicksearchValue\":\"12345\"}",
                    defaultValue = "{\"quicksearchValue\":\"Carlos Oliveira\"}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "Código da empresa para consulta dos dados", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    false,
                    false, true,
                    false, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar usuários inativos que utilizam o aplicativo",
            notes = "Retorna a lista paginada de usuários inativos que possuem e utilizam o aplicativo móvel, com possibilidade de filtro por professor e busca rápida.",
            tags = "BI App"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Número da página a ser retornada.", dataType = "long", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "size", value = "Número de itens por página.", dataType = "long", paramType = "query", defaultValue = "20"),
            @ApiImplicitParam(name = "sort", value = "Campo para ordenação dos resultados, seguido de 'ASC' ou 'DESC'.<br/>" +
                    "<strong>Campos disponíveis para ordenação:</strong><br/>" +
                    "<ul>" +
                    "<li><strong>matricula:</strong> Matrícula do aluno</li>" +
                    "<li><strong>nomeAbreviado:</strong> Nome do aluno</li>" +
                    "<li><strong>nomeProfessor:</strong> Nome do professor responsável</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> 'nomeAbreviado,ASC' ou 'matricula,DESC'",
                    dataType = "string", paramType = "query", defaultValue = "nomeAbreviado,ASC")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaUsuariosBiAppPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "/inativos-app", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inativosUsam(
            @ApiParam(value = "Código do professor para filtrar usuários vinculados", defaultValue = "123")
            @RequestParam(value = "professorId", required = false) Integer idProfessor,
            @ApiParam(value = "Filtros de busca em formato JSON. " +
                    "<br/><strong>Filtros disponíveis:</strong>" +
                    "<ul>" +
                    "<li><strong>quicksearchValue:</strong> Busca rápida por matrícula (número) ou nome do aluno (texto)</li>" +
                    "</ul>" +
                    "<strong>Exemplo:</strong> {\"quicksearchValue\":\"João\"} ou {\"quicksearchValue\":\"12345\"}",
                    defaultValue = "{\"quicksearchValue\":\"Ana Costa\"}")
            @RequestParam(value = "filters", required = false) String filter,
            @ApiParam(value = "Código da empresa para consulta dos dados", defaultValue = "1", required = true)
            @RequestHeader("empresaId") Integer empresaId,
            @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            final String chave = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(biAppService.listaUsuariosApp(chave,
                    filter,
                    empresaId,
                    idProfessor,
                    false,
                    true, false,
                    false, paginadorDTO), paginadorDTO);
        } catch (Exception e) {
            Logger.getLogger(BIAvalicaoFisicaController.class.getName()).log(Level.SEVERE, "Erro ao consultar Dash de app", e);
            return ResponseEntityFactory.erroInterno("erro_obter_dash_app", e.getMessage());
        }
    }


}
