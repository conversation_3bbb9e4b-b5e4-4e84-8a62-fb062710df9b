package br.com.pacto.controller.json.lesao;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.lesao.FiltroLesaoJSON;
import br.com.pacto.bean.lesao.LesaoDTO;
import br.com.pacto.controller.json.wod.TiposWodController;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.lesao.LesaoService;
import br.com.pacto.swagger.respostas.lesao.ExemploRespostaAlunoLesaoDTO;
import br.com.pacto.swagger.respostas.lesao.ExemploRespostaIndiceLesaoDTO;
import br.com.pacto.swagger.respostas.lesao.ExemploRespostaLesaoDTO;
import br.com.pacto.swagger.respostas.lesao.ExemploRespostaListAlunoLesaoDTOPaginacao;
import br.com.pacto.swagger.respostas.lesao.ExemploRespostaListLesaoDTOPaginacao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;

@Api(tags = "BI Cross")
@Controller
@RequestMapping("/psec/lesao")
public class LesaoController {
    @Autowired
    private SessaoService sessaoService;
    private final LesaoService lesaoService;


    @Autowired
    public LesaoController(LesaoService lesaoService){
        Assert.notNull(lesaoService, "O serviço de tipos lesao não foi injetado corretamente");
        this.lesaoService = lesaoService;
    }

    @ApiOperation(value = "Cadastrar lesão", notes = "Cadastra uma nova lesão no sistema")
    @ApiResponses({
            @ApiResponse(code = 200, message = "lesão cadastrada com sucesso", response = ExemploRespostaLesaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarLesao(@ApiParam(value = "Dados da lesão a ser cadastrada") @RequestBody LesaoDTO lesaoDTO){
        Integer codigoUsuario = sessaoService.getUsuarioAtual().getId();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(lesaoService.gravarLesao(lesaoDTO, ctx, codigoUsuario, false));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar Lesão", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Editar lesão", notes = "Edita uma lesão existente no sistema")
    @ApiResponses({
            @ApiResponse(code = 200, message = "lesão editada com sucesso", response = ExemploRespostaLesaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> editarLesao(@ApiParam(value = "Dados da lesao a ser editada") @RequestBody LesaoDTO lesaoDTO) {
        Integer codigoUsuario = sessaoService.getUsuarioAtual().getId();
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(lesaoService.gravarLesao(lesaoDTO, ctx, codigoUsuario, false));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar Lesão", e);
            if (e.getCause().getMessage() == null) {
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            } else {
                return ResponseEntityFactory.erroRegistroDuplicado(e.getChaveExcecao(), e.getCause().getMessage());
            }
        }
    }

    @ApiOperation(value = "Consultar lesões", notes = "Lista lesões cadastradas no sistema com paginacao e filtros")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Pagina da requisicao", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que sao procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenacao das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponiveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "id,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de lesão retornada com sucesso", response = ExemploRespostaListLesaoDTOPaginacao.class)
    })
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarLesao(@ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
            "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
            "<strong>Filtros disponiveis:</strong>\n" +
            "- <strong>quicksearchValue:</strong> Termo de busca para codigo do cliente.\n" +
            "- <strong>quicksearchFields:</strong> Define em qual campo o 'quicksearchValue' sera aplicado (Deve ser informado como uma lista ex: [\"nome\"]).",
            defaultValue = "{\"quicksearchValue\":\"12345\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros, @ApiIgnore PaginadorDTO paginadorDTO) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            //FiltroNivelWodJSON filtroNivelWodJSON = new FiltroNivelWodJSON(filtros);
            FiltroLesaoJSON filtroLesaoJSON = new FiltroLesaoJSON(filtros);
            return ResponseEntityFactory.ok(lesaoService.listarLesao(ctx, filtroLesaoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o Nível wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Buscar lesão por ID", notes = "Busca uma lesão especifica pelo seu identificador")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lesão encontrada com sucesso", response = ExemploRespostaLesaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarLesao(@ApiParam(value = "Identificador unico da lesao", required = true, defaultValue = "1") @PathVariable("id") final Integer id) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(lesaoService.buscarLesao(id,ctx));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar o Nível wod", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Montar indice de lesões por ano", notes = "Monta o indice de lesões por gravidade para um ano especifico")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Indice de lesões montado com sucesso", response = ExemploRespostaIndiceLesaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/bi-cross/indice-lesoes/{ano}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> montarIndiceLesao(@ApiParam(value = "Identificador da empresa", required = true, defaultValue = "1") @RequestHeader("empresaId") Integer empresaId,
                                                           @ApiParam(value = "Ano para consulta do indice de lesoes", required = true, defaultValue = "2024") @PathVariable("ano") final Integer ano) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(lesaoService.montarIndiceLesao(ctx, empresaId, ano));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao montar o índice de lesões", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(value = "Listar lesões para BI Cross", notes = "Lista lesões para analise de Business Intelligence do Crossfit")
    @ApiResponses({
            @ApiResponse(code = 200, message = "Lista de lesões para BI retornada com sucesso", response = ExemploRespostaAlunoLesaoDTO.class)
    })
    @ResponseBody
    @RequestMapping(value = "/bi-cross/listar-lesoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarLesoesBiCross(@ApiParam(value = "Identificador da empresa", required = true, defaultValue = "1") @RequestHeader("empresaId") Integer empresaId,
                                                            @ApiParam(value = "Filtros de busca para BI Cross, deve ser informado como um JSON.\n\n" +
                                                                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                                                                    "<strong>Filtros disponiveis:</strong>\n" +
                                                                    "- <strong>gravidadeEnun:</strong> Gravidade da lesao (LEVE, MODERADA, GRAVE ou TODAS).\n" +
                                                                    "- <strong>ano:</strong> Ano para filtrar as lesoes.\n" +
                                                                    "- <strong>quicksearchValue:</strong> Termo de busca para nome do aluno.",
                                                                    defaultValue = "{\"gravidadeEnun\":\"LEVE\", \"ano\":2024, \"quicksearchValue\":\"Silva\"}", required = true)
                                                            @RequestParam(value = "filters", required = true) JSONObject filtros) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(lesaoService.listarLesoesBiCross(ctx, empresaId, filtros));
        } catch (ServiceException e) {
            Logger.getLogger(TiposWodController.class.getName()).log(Level.SEVERE, "Erro ao listar lesões para BI", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
