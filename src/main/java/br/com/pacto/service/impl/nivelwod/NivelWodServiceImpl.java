/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.nivelwod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.nivelwod.NivelWod;
import br.com.pacto.bean.wod.FiltroNivelWodJSON;
import br.com.pacto.bean.wod.NivelWodResponseTO;
import br.com.pacto.bean.wod.NivelWodTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.nivelwod.NivelWodDao;
import br.com.pacto.enumerador.wod.CategoriaNivelWodEnum;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.NivelWodExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.TipoWodExcecoes;
import br.com.pacto.service.intf.nivelwod.NivelWodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static br.com.pacto.objeto.Uteis.incluirLog;

/**
 *
 * <AUTHOR>
 */
@Service
public class NivelWodServiceImpl implements NivelWodService {

    @Autowired
    private NivelWodDao nivelWodDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private LogDao logDao;

    @Override
    public List<NivelWodResponseTO> listarNiveisWod(FiltroNivelWodJSON filtroNivelWodJSON, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<NivelWod> niveis = nivelWodDao.listarNiveisWod(ctx, filtroNivelWodJSON, paginadorDTO);
            List<NivelWodResponseTO> ret = new ArrayList<>();
            for (NivelWod nivel : niveis) {
                ret.add(new NivelWodResponseTO(nivel));
            }
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(NivelWodExcecoes.ERRO_BUSCAR_NIVEIS_WOD, e);
        }
    }


    @Override
    public NivelWodResponseTO buscarNivelWod(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            NivelWod nivel = obterPorCodigo(ctx, id);
            if (nivel == null) {
                throw new ServiceException(NivelWodExcecoes.ERRO_BUSCAR_NIVEIS_WOD);
            }
            return new NivelWodResponseTO(nivel);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_BUSCAR_TIPO_WOD, e);
        }
    }

    @Override
    public NivelWodResponseTO cadastrarNivelWod(NivelWodTO nivelWodTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            NivelWod nivel = this.obterPorNome(ctx, nivelWodTO.getNome());
            if(nivel!=null){
                throw new ServiceException(NivelWodExcecoes.ERRO_CADASTRAR_NIVEL_NOME_EXISTE);
            }
            NivelWod novoNivelWod = new NivelWod();
            novoNivelWod.setNome(nivelWodTO.getNome());
            novoNivelWod.setCategoria(CategoriaNivelWodEnum.PERSONALIZADO.getId());
            NivelWod  nivelWod = nivelWodDao.insert(ctx, novoNivelWod);

            incluirLog(ctx, novoNivelWod.getCodigo().toString(),
                    "", "", novoNivelWod.getDescricaoParaLog(null),
                    "INCLUSÃO", "INCLUSÃO DE NÍVEL WOD",
                    EntidadeLogEnum.NIVELWOD, "NivelWod", sessaoService, logDao, null, null);
            return new NivelWodResponseTO(nivelWod);
        } catch (Exception e) {
            throw new ServiceException(NivelWodExcecoes.ERRO_CADASTRO_NIVEIS_WOD, e);
        }
    }

    @Override
    public NivelWodResponseTO editarNivelWod(NivelWodTO nivelWodTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            NivelWod  nivelWodAntes = nivelWodDao.findById(ctx, nivelWodTO.getId());
            NivelWod nivel = this.obterPorCodigo(ctx, nivelWodTO.getId());
            if(nivel==null){
                throw new ServiceException(NivelWodExcecoes.ERRO_NIVEIS_WOD_NAO_ENCONTRADO);
            }
            if(nivelWodTO.getNome().equals(nivel.getNomePadrao())){
                nivel.setCategoria(CategoriaNivelWodEnum.PADRAO.getId());
                nivel.setNome(nivelWodTO.getNome());
            }else if(nivel.getNomePadrao()!=null){
                nivel.setCategoria(CategoriaNivelWodEnum.PADRAOPERSONALIZADO.getId());
                nivel.setNome(nivelWodTO.getNome());
            }else{
                nivel.setCategoria(CategoriaNivelWodEnum.PERSONALIZADO.getId());
                nivel.setNome(nivelWodTO.getNome());
            }
            NivelWod  nivelWod = nivelWodDao.update(ctx, nivel);

            incluirLog(ctx, nivel.getCodigo().toString(),
                    "", nivelWodAntes.getDescricaoParaLog(nivelWod),
                    nivel.getDescricaoParaLog(nivelWodAntes), "ALTERAÇÃO",
                    "ALTERAÇÃO DE NÍVEL WOD", EntidadeLogEnum.NIVELWOD, "NivelWod",
                    sessaoService, logDao, false);

            return new NivelWodResponseTO(nivelWod);
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_CADASTRAR_TIPO_WOD, e);
        }
    }

    @Override
    public NivelWodResponseTO reverterNivelWod(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            NivelWod  nivelWodAntes = nivelWodDao.findById(ctx, id);
            NivelWod nivel = this.obterPorCodigo(ctx, id);
            if(nivel==null){
                throw new ServiceException(NivelWodExcecoes.ERRO_NIVEIS_WOD_NAO_ENCONTRADO);
            }
            if(nivel.getNome() != null){
                nivel.setCategoria(CategoriaNivelWodEnum.PADRAO.getId());
                nivel.setNome(nivel.getNomePadrao());
            }
            NivelWod  nivelWod = nivelWodDao.update(ctx, nivel);
            incluirLog(ctx, nivel.getCodigo().toString(),
                    "", nivelWodAntes.getDescricaoParaLog(nivelWod),
                    nivel.getDescricaoParaLog(nivelWodAntes), "ALTERAÇÃO",
                    "REVERSÃO DE NÍVEL WOD", EntidadeLogEnum.NIVELWOD, "NivelWod",
                    sessaoService, logDao, false);

            return new NivelWodResponseTO(nivelWod);
        } catch (Exception e) {
            throw new ServiceException(TipoWodExcecoes.ERRO_CADASTRAR_TIPO_WOD, e);
        }
    }

    @Override
    public void excluirNivelWod(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            NivelWod nivel = this.obterPorCodigo(ctx, id);
            if(nivel==null){
                throw new ServiceException(NivelWodExcecoes.ERRO_NIVEIS_WOD_NAO_ENCONTRADO);
            }
            if(CategoriaNivelWodEnum.getFromId(nivel.getCategoria()).equals(CategoriaNivelWodEnum.PERSONALIZADO)){

                incluirLog(ctx, nivel.getCodigo().toString(), "",
                        nivel.getDescricaoParaLog(null), "", "EXCLUSÃO",
                        "EXCLUSÃO DE NÍVEL WOD", EntidadeLogEnum.NIVELWOD, "NivelWod", sessaoService, logDao, null, null);

                nivelWodDao.delete(ctx, nivel);
            }else{
                throw new ServiceException(NivelWodExcecoes.ERRO_EXCLUIR_NIVEL_WOD_CATEGORIA);
            }
        } catch (Exception e) {
            throw new ServiceException(NivelWodExcecoes.ERRO_EXCLUIR_NIVEL_WOD, e);
        }
    }



    @Override
    public NivelWod obterPorCodigo(String ctx, Integer codigo) throws ServiceException {
        String s = "select obj from NivelWod obj where codigo = :codigo";
        Map<String, Object> p = new HashMap();
        p.put("codigo", codigo);
        return nivelWodDao.obterObjetoPorParam(ctx, s, p);
    }

    @Override
    public NivelWod obterPorNome(String ctx, String nome) throws ServiceException {
        String s = "SELECT obj FROM NivelWod obj where upper(obj.nome) = :nome";
        Map<String, Object> p = new HashMap();
        p.put("nome", nome.toUpperCase());
        return nivelWodDao.obterObjetoPorParam(ctx, s, p);
    }

    @Override
    public List<NivelWodResponseTO>  listarNiveisWodPorChave(String ctx) throws ServiceException{
        try {
            java.sql.Connection con = nivelWodDao.getConnection(ctx);
            ArrayList<NivelWodResponseTO> listaNivelWod = new ArrayList<>();
            try (ResultSet rs = con.prepareStatement("SELECT * FROM nivelwod order by codigo ").executeQuery()) {
                while (rs.next()) {
                    NivelWodResponseTO nivelWodResponseTO = new NivelWodResponseTO();
                    nivelWodResponseTO.setId(rs.getInt("codigo"));
                    nivelWodResponseTO.setCategoria(CategoriaNivelWodEnum.getFromId(rs.getInt("categoria")).getDescricao());
                    nivelWodResponseTO.setNome(rs.getString("nome"));
                    nivelWodResponseTO.setNomePadrao(rs.getString("nomepadrao"));
                    listaNivelWod.add(nivelWodResponseTO);
                }
            }
            return listaNivelWod;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

}
