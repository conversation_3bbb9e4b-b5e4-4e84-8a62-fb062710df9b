/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.tipoEvento;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.disponibilidade.Disponibilidade;
import br.com.pacto.bean.disponibilidade.HorarioDisponibilidade;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.bean.tipoEvento.TipoEvento;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.agendamento.TipoAgendamentoDuracaoDTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoCadDTO;
import br.com.pacto.controller.json.tipoEvento.TipoAgendamentoDTO;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.disponibilidade.DisponibilidadeDao;
import br.com.pacto.dao.intf.disponibilidade.HorarioDisponibilidadeDao;
import br.com.pacto.dao.intf.tipoevento.TipoEventoDao;
import br.com.pacto.enumerador.agenda.TipoAgendamentoEnum;
import br.com.pacto.enumerador.agenda.TipoDuracaoEvento;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.dto.UsuarioSimplesDTO;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.impl.log.AlteracoesTO;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.service.impl.notificacao.excecao.TipoEventoExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.intf.agenda.AgendamentoService;
import br.com.pacto.service.intf.tipoEvento.TipoEventoService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;

import java.sql.ResultSet;
import java.util.*;

import br.com.pacto.util.enumeradores.PaletaCoresEnum;
import br.com.pacto.util.impl.UtilReflection;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static br.com.pacto.objeto.Uteis.incluirLog;

/**
 *
 * <AUTHOR>
 */
@Service
public class TipoEventoServiceImpl implements TipoEventoService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private TipoEventoDao tipoEventoDao;
    @Autowired
    private DisponibilidadeDao disponibilidadeDao;
    @Autowired
    private HorarioDisponibilidadeDao horarioDisponibilidadeDao;
    @Autowired
    private AgendamentoService agendamentoService;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private LogDao logDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public TipoEventoDao getTipoEventoDao() {
        return this.tipoEventoDao;
    }

    public void setTipoEventoDao(TipoEventoDao tipoEventoDao) {
        this.tipoEventoDao = tipoEventoDao;
    }

    @Override
    public TipoEvento alterar(final String ctx, TipoEvento object) throws ValidacaoException, ServiceException {
        try {
            validarDados(ctx, object);

            if (!object.getAtivo()) {
                boolean existeAgendamentoFuturo = agendamentoService.existeAgendamentoFuturo(ctx, object);
                if (existeAgendamentoFuturo) {
                    throw new ValidacaoException("Existem agendamentos futuros para este tipo de evento! Exclua-os antes de tentar inativar.");
                }
            }

            return getTipoEventoDao().update(ctx, object);
        } catch (ValidacaoException ex) {
            throw new ValidacaoException(ex.getMensagens());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public void excluir(final String ctx, TipoEvento object) throws ServiceException {
        try {
            getTipoEventoDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public TipoEvento inserir(final String ctx, TipoEvento object) throws ValidacaoException, ServiceException {
        try {
            validarDados(ctx, object);
            return getTipoEventoDao().insert(ctx, object);
        } catch (ValidacaoException ex) {
            throw new ValidacaoException(ex.getMensagens());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public TipoEvento obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getTipoEventoDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public TipoEvento obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getTipoEventoDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<TipoEvento> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getTipoEventoDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<TipoEvento> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getTipoEventoDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<TipoEvento> obterTodos(final String ctx, boolean somenteAtivos) throws ServiceException {
        try {
            if (somenteAtivos) {
                return getTipoEventoDao().findListByAttributes(ctx, new String[]{"ativo"}, new Object[]{somenteAtivos}, "nome", 0);
            } else {
                return getTipoEventoDao().findListByAttributes(ctx, null, null, "nome", 0);
            }
        } catch (Exception ex) {
            Uteis.logar(ex, TipoEventoServiceImpl.class);
            throw new ServiceException(ex);
        }
    }

    @Override
    public  TipoAgendamentoCadDTO cadastrarTipoAgendamento(TipoAgendamentoCadDTO tipoAgendamento ) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TipoEvento tipoEvento =  new TipoEvento(tipoAgendamento);
            if(tipoEventoDao.exists(ctx,tipoEvento,"nome")){
                throw new ServiceException(TipoEventoExcecoes.VALIDACAO_JA_EXISTE_UM_TIPO_AGENDAMENTO);
            }
            TipoEvento novoTipoEvento = getTipoEventoDao().insert(ctx, tipoEvento);

            incluirLog(ctx, novoTipoEvento.getCodigo().toString(), "", "",
                    novoTipoEvento.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE TIPO AGENDAMENTO",
                    EntidadeLogEnum.TIPOEVENTO, "Tipo Agendamento", sessaoService, logDao, null, null);

            return tipoAgendamento;
        } catch (Exception ex) {
            throw new ServiceException(TipoEventoExcecoes.ERRO_TENTAR_INCLUIR_TIPO_AGENDAMENTO, ex);
        }

    }

    @Override
    public  void removerTipoAgendamento(Integer id) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            TipoEvento tipoEvento = getTipoEventoDao().findById(ctx, id);
            getTipoEventoDao().delete(ctx,id);
            incluirLog(ctx, tipoEvento.getCodigo().toString(), "", tipoEvento.getDescricaoParaLog(null),
                    "", "EXCLUSÃO", "EXCLUSÃO DE TIPO AGENDAMENTO",
                    EntidadeLogEnum.TIPOEVENTO, "Tipo Agendamento", sessaoService, logDao, null, null);
        } catch (Exception ex) {
            Uteis.logar(ex, TipoEventoServiceImpl.class);
            throw new ServiceException(TipoEventoExcecoes.ERRO_EXCLUIR_EXISTE_AGENDAMENTOS,ex);
        }

    }

    @Override
    public  TipoAgendamentoDTO buscarTiposAgendamento(Integer tipoAgendamento) throws ServiceException{
        try {
           String ctx = sessaoService.getUsuarioAtual().getChave();
           prepare(ctx);
            TipoAgendamentoDTO tipoAgendamentoReturn = new TipoAgendamentoDTO(getTipoEventoDao().findById(ctx, tipoAgendamento));
            return tipoAgendamentoReturn;
        } catch (Exception ex) {
            Uteis.logar(ex, TipoEventoServiceImpl.class);
            throw new ServiceException(ex);
        }

    }

    @Override
    public  TipoAgendamentoDTO atualizarTipoAgendamento(TipoAgendamentoCadDTO tipoAgendamento) throws ServiceException{
            try {
                String ctx = sessaoService.getUsuarioAtual().getChave();
                TipoEvento tipoEvento =  new TipoEvento(tipoAgendamento);
                validarDados(ctx, tipoEvento);

                if (!tipoEvento.getAtivo()) {
                    boolean existeAgendamentoFuturo = agendamentoService.existeAgendamentoFuturo(ctx, tipoEvento);
                    if (existeAgendamentoFuturo) {
                        throw new Exception(TipoEventoExcecoes.ERRO_EXISTE_AGENDAMENTOS.name());
                    }
                }
                TipoEvento objAntes = UtilReflection.copy(getTipoEventoDao().findById(ctx, tipoEvento.getCodigo()));
                TipoAgendamentoDTO tipoAgendamentoDTO = new TipoAgendamentoDTO(getTipoEventoDao().update(ctx, tipoEvento));

                incluirLog(ctx, tipoEvento.getCodigo().toString(), null,
                        objAntes.getDescricaoParaLog(tipoEvento), tipoEvento.getDescricaoParaLog(objAntes),
                        "ALTERAÇÃO", "ALTERAÇÃO DE TIPO AGENDAMENTO", EntidadeLogEnum.TIPOEVENTO,
                        "Tipo Agendamento", sessaoService, logDao, null, null);

                return tipoAgendamentoDTO;
            } catch (Exception ex) {
                throw new ServiceException(TipoEventoExcecoes.getFromMessage(ex, TipoEventoExcecoes.ERRO_TENTAR_ATUALIZAR_TIPO_AGENDAMENTO),
                        ex);
            }
    }


    @Override
    public List<TipoAgendamentoDTO> coltultarTtipoAgendamento(String nomeTipoAgendamento, Boolean ativos, PaginadorDTO paginadorDTO, boolean agenda) throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        List<TipoAgendamentoDTO> tipos = new ArrayList<>();
        tipos.addAll(getTipoEventoDao().consultarTipoEvento(ctx,nomeTipoAgendamento, ativos, paginadorDTO));
        if (agenda) {
            tipos.addAll(disponibilidadeDao.consultarDisponibilidadeTipoAgendamento(ctx, nomeTipoAgendamento, ativos, paginadorDTO));
        }
        return tipos;
    }
    @Override
    public List<TipoAgendamentoDTO> obterTodosAtivos() throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();

            prepare(ctx);

            List<TipoAgendamentoDTO> listaReturn = new ArrayList<>();

            List<TipoEvento> tiposEvento = obterTodos(ctx, true);

            for (TipoEvento tipoEvento : tiposEvento) {
                listaReturn.add(new TipoAgendamentoDTO(tipoEvento));
            }

            return listaReturn;
        } catch (ServiceException e) {
            throw new ServiceException(TipoEventoExcecoes.ERRO_BUSCAR_TODOS_TIPOS_EVENTO, e);
        }
    }

    private void createEvento(TipoEvento tpEvento , TipoAgendamentoDTO tpAegendamento){

        tpEvento.setNome(tpAegendamento.getNome());
    }

    public void validarDados(final String ctx, TipoEvento object) throws ServiceException {
        List<String> msgs = new ArrayList<String>();
        if (validacao.emptyString(object.getNome())) {
            msgs.add("obrigatorio.nome");
        }
        if (validacao.isNull(object.getComportamento())) {
            msgs.add("msg.comportamento");
        }
        if (validacao.isNull(object.getCor())) {
            msgs.add("msg.cor");
        }
        if (getTipoEventoDao().exists(ctx, object, "nome")) {
            throw new ServiceException(TipoEventoExcecoes.VALIDACAO_JA_EXISTE_UM_TIPO_AGENDAMENTO);
        }
        if (msgs.isEmpty()) {
            return;
        }
        throw new ValidacaoException(msgs);
    }

    @Override
    public List<TipoEvento> consultarQuaisTiposUsuarioTemPermissao(String ctx,
            Usuario usuario, boolean somenteAtivos) throws ServiceException {
        List<TipoEvento> todos = obterTodos(ctx, somenteAtivos);
        List<TipoEvento> listaRetorno = new ArrayList<TipoEvento>();
        for (TipoEvento tipo : todos) {
            if (usuario.isItemHabilitado(tipo.getComportamento().toString(), "CONSULTAR")) {
                tipo.setEscolhido(true);
                listaRetorno.add(tipo);

            }
        }
        return listaRetorno;

    }

    @Override
    public List<TipoEvento> consultarTiposNoMesmoNSU(String ctx, Integer nsu) throws ServiceException {
        try {
            String hql = "SELECT DISTINCT(tipoEvento) FROM Agendamento WHERE nsu = :nsu";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nsu", nsu);
            return getTipoEventoDao().findByParam(ctx, hql, params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<TipoEvento> consultarPorTipo(String ctx, TipoAgendamentoEnum tipo) throws ServiceException {
        try {
            String hql = "SELECT obj FROM TipoEvento obj WHERE obj.comportamento = :t";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("t", tipo);
            return getTipoEventoDao().findByParam(ctx, hql, params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<TipoAgendamentoDTO> obterTodosAtivosApp( String ctx ) throws ServiceException {
        try {

            prepare(ctx);

            List<TipoAgendamentoDTO> listaReturn = new ArrayList<>();

            List<TipoEvento> tiposEvento = obterTodosApp(ctx, true);

            for (TipoEvento tipoEvento : tiposEvento) {
                listaReturn.add(new TipoAgendamentoDTO(tipoEvento));
            }

            List<Disponibilidade> disponibilidades = obterTodasDisponibilidadesApp(ctx, true);

            for (Disponibilidade disp : disponibilidades) {
                if (disp != null) {
                    listaReturn.add(new TipoAgendamentoDTO(disp));
                }
            }

            return listaReturn;
        } catch (ServiceException e) {
            throw new ServiceException(TipoEventoExcecoes.ERRO_BUSCAR_TODOS_TIPOS_EVENTO, e);
        }
    }

    @Override
    public List<TipoEvento> obterTodosApp(final String ctx, boolean somenteAtivos) throws ServiceException {
        try {
            if (somenteAtivos) {
                return getTipoEventoDao().findListByAttributes(ctx, new String[]{"ativo","permitirApp"}, new Object[]{somenteAtivos,true}, "nome", 0);
            } else {
                return getTipoEventoDao().findListByAttributes(ctx, null, null, "nome", 0);
            }
        } catch (Exception ex) {
            Uteis.logar(ex, TipoEventoServiceImpl.class);
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Disponibilidade> obterTodasDisponibilidadesApp(final String ctx, boolean somenteAtivos) throws ServiceException {
        List<Disponibilidade> disponibilidades = new ArrayList<>();
        Set<Disponibilidade> disponibilidadesSet = new HashSet<>(); // ajuste para não retornar objeto duplicado

        try {
            List<HorarioDisponibilidade> horariosDisp = horarioDisponibilidadeDao.findListByAttributes(ctx, new String[]{"ativo", "permieagendarapptreino"}, new Object[]{somenteAtivos, true}, "codigo", 0);
            if (horariosDisp != null) {
                horariosDisp.forEach(h -> {
                    if (h.getDisponibilidade() != null) {
                        disponibilidadesSet.add(h.getDisponibilidade());
                    }
                });
            }
            disponibilidades.addAll(disponibilidadesSet);
            return disponibilidades;
        } catch (Exception ex) {
            Uteis.logar(ex, TipoEventoServiceImpl.class);
            throw new ServiceException(ex);
        }
    }

    @Override
    public TipoAgendamentoDuracaoDTO obterPorId(Integer id) throws ServiceException{
        UsuarioSimplesDTO usuarioAtual = this.sessaoService.getUsuarioAtual();
        TipoEvento tipoEvento = obterPorId(usuarioAtual.getChave(), id);
        return new TipoAgendamentoDuracaoDTO(tipoEvento);
    }

    public List<LogTO> listarLog(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigoTipo) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoTp = null;
            try {
                codigoTp = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append(" inner join tipoevento_aud ta on ta.rev = c.id \n");
            sql.append(" left join tipoevento_aud ta2 on ta2.codigo = ta.codigo and ta.revtype = 2 and ta2.revtype = 0  \n");
            sql.append(" where (ta.revtype <> 2 or ta2.codigo is not null) \n");
            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ta.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }
            if(UteisValidacao.emptyNumber(codigoTipo)){
                sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
                if (codigoTp != null){
                    sql.append(" ta.codigo = ").append(codigoTp).append(" or \n");
                }
                if (!UteisValidacao.emptyString(quicksearchValue)) {
                    sql.append("  upper(ta.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(ta2.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
                }
                sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
            } else {
                sql.append(" and ta.codigo = ").append(codigoTipo).append("  \n");
                if (!UteisValidacao.emptyString(quicksearchValue)) {
                    sql.append(" and ( upper(ta.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(ta2.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%') \n");
                }
            }

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = tipoEventoDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "ta2.nome as nomeexcluido, ta.codigo, ta.nome, ta.apenasalunoscarteira, ta.ativo, ta.comportamento,\n" +
                                    "ta.cor, ta.dias as em_dias, ta.duracao,\n" +
                                    "ta.duracaominutosmax, ta.duracaominutosmin,\n" +
                                    "ta.intervalominimofalta, ta.nragendamentos,\n" +
                                    "ta.permitirapp, c.\"timestamp\", c.username, ta.revtype, c.id ")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "nome", "apenasalunoscarteira", "ativo", "comportamento",
                            "cor", "em_dias", "duracao", "duracaominutosmax", "duracaominutosmin",
                            "intervalominimofalta", "nragendamentos",
                            "permitirapp");
                    valoresAlterados.put("comportamento", TipoAgendamentoEnum.getFromId(rs.getInt("comportamento")).getDescricao());
                    valoresAlterados.put("duracao", TipoDuracaoEvento.getFromId(rs.getInt("duracao")).name());
                    valoresAlterados.put("cor", PaletaCoresEnum.getFromId(rs.getInt("cor")).getDescricao());
                    Map<String, String> valoresAnteriores = null;
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            revtype.equals(TipoRevisaoEnum.DELETE) ? rs.getString("nomeexcluido") : rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = tipoEventoDao.createStatement(ctx,
                                "select ta.nome, ta.apenasalunoscarteira, ta.ativo, ta.comportamento, \n" +
                                        "   ta.cor, ta.dias as em_dias, ta.duracao, \n" +
                                        "   ta.duracaominutosmax, ta.duracaominutosmin, \n" +
                                        "   ta.intervalominimofalta, ta.nragendamentos, \n" +
                                        "   ta.permitirapp from tipoevento_aud ta where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                valoresAnteriores = Uteis.montarMapa(rsAnterior, "nome", "apenasalunoscarteira", "ativo",
                                        "cor", "em_dias", "duracaominutosmax", "duracaominutosmin",
                                        "intervalominimofalta", "nragendamentos",
                                        "permitirapp");
                                valoresAnteriores.put("comportamento", TipoAgendamentoEnum.getFromId(rsAnterior.getInt("comportamento")).getDescricao());
                                valoresAnteriores.put("duracao", TipoDuracaoEvento.getFromId(rsAnterior.getInt("duracao")).name());
                                valoresAnteriores.put("cor", PaletaCoresEnum.getFromId(rsAnterior.getInt("cor")).getDescricao());
                            }
                        }
                    }
                    logTO.setAlteracoes(Uteis.compararMapas(valoresAnteriores, valoresAlterados));

                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = tipoEventoDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

}
