package br.com.pacto.service.impl.crossfit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR> 04/04/2019
 */
@ApiModel(description = "Detalhes de indicadores de Business Intelligence para Crossfit")
public class DetalhesIndicadorBICrossfitDTO {

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "12345")
    private Integer matricula;

    @ApiModelProperty(value = "Nome completo do aluno", example = "Maria Silva Santos")
    private String nome;

    @ApiModelProperty(value = "Situação atual do aluno no sistema", example = "ATIVO")
    private String situacaoAluno;

    @ApiModelProperty(value = "Data do evento/atividade registrada", example = "2024-06-15")
    private Date dia;

    public DetalhesIndicadorBICrossfitDTO() {
    }

    public DetalhesIndicadorBICrossfitDTO(Integer matricula, String nome, String situacaoAluno, Date dia) {
        this.matricula = matricula;
        this.nome = nome;
        this.situacaoAluno = situacaoAluno;
        this.dia = dia;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSituacaoAluno() {
        return situacaoAluno;
    }

    public void setSituacaoAluno(String situacaoAluno) {
        this.situacaoAluno = situacaoAluno;
    }

    public Date getdia() {
        return dia;
    }

    public void setdia(Date dia) {
        this.dia = dia;
    }
}
