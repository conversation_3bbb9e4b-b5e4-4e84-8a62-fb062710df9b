package br.com.pacto.service.impl.avaliacao;

import br.com.pacto.bean.avaliacao.RMLLinha;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Valores de referência RML para uma faixa etária específica.")
public class RMLValoresDTO {

    @ApiModelProperty(value = "Linha de referência RML (masculino ou feminino).")
    private RMLLinha linha;

    @ApiModelProperty(value = "Faixa etária para os valores de referência.", example = "20-29 anos")
    private String faixaIdade;

    @ApiModelProperty(value = "Valor mínimo para classificação excelente.", example = "≥ 36")
    private String excelente;

    @ApiModelProperty(value = "Valor mínimo para classificação acima da média.", example = "29-35")
    private String acimaMedia;

    @ApiModelProperty(value = "Valor mínimo para classificação média.", example = "22-28")
    private String media;

    @ApiModelProperty(value = "Valor mínimo para classificação abaixo da média.", example = "17-21")
    private String abaixoMedia;

    @ApiModelProperty(value = "Valor máximo para classificação fraco.", example = "≤ 16")
    private String fraco;

    public RMLLinha getLinha() {
        return linha;
    }

    public void setLinha(RMLLinha linha) {
        this.linha = linha;
    }

    public String getFaixaIdade() {
        return faixaIdade;
    }

    public void setFaixaIdade(String faixaIdade) {
        this.faixaIdade = faixaIdade;
    }

    public String getExcelente() {
        return excelente;
    }

    public void setExcelente(String excelente) {
        this.excelente = excelente;
    }

    public String getAcimaMedia() {
        return acimaMedia;
    }

    public void setAcimaMedia(String acimaMedia) {
        this.acimaMedia = acimaMedia;
    }

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getAbaixoMedia() {
        return abaixoMedia;
    }

    public void setAbaixoMedia(String abaixoMedia) {
        this.abaixoMedia = abaixoMedia;
    }

    public String getFraco() {
        return fraco;
    }

    public void setFraco(String fraco) {
        this.fraco = fraco;
    }
}
