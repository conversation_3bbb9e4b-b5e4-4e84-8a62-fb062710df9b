package br.com.pacto.service.impl.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "Tabela de valores de referência para testes de Resistência Muscular Localizada (RML).")
public class RMLConfigDTO {

    @ApiModelProperty(value = "Valores de referência para flexões de braço por faixa etária e classificação.")
    private List<RMLValoresDTO> bracos = new ArrayList<>();

    @ApiModelProperty(value = "Valores de referência para abdominais por faixa etária e classificação.")
    private List<RMLValoresDTO> abdomen = new ArrayList<>();

    public List<RMLValoresDTO> getBracos() {
        return bracos;
    }

    public void setBracos(List<RMLValoresDTO> bracos) {
        this.bracos = bracos;
    }

    public List<RMLValoresDTO> getAbdomen() {
        return abdomen;
    }

    public void setAbdomen(List<RMLValoresDTO> abdomen) {
        this.abdomen = abdomen;
    }
}
