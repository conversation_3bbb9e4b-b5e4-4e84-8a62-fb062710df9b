/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.aparelho;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.nivel.NivelResponseTO;
import br.com.pacto.bean.nivel.NivelTO;
import br.com.pacto.bean.sincronizacao.TipoRevisaoEnum;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.nivel.FiltroNivelJSON;
import br.com.pacto.dao.intf.cliente.ClienteSinteticoDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.nivel.NivelDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.log.AlteracoesTO;
import br.com.pacto.service.impl.log.LogTO;
import br.com.pacto.service.impl.notificacao.excecao.NivelExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.impl.JSFUtilities;
import br.com.pacto.util.impl.UtilReflection;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static br.com.pacto.objeto.Uteis.incluirLog;

/**
 *
 * <AUTHOR>
 */
@Service
public class NivelServiceImpl implements NivelService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private NivelDao nivelDao;
    @Autowired
    private ClienteSinteticoDao clienteSinteticoDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private LogDao logDao;

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public void setViewUtils(ViewUtils viewUtils) {
        this.viewUtils = viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public void setValidacao(Validacao validacao) {
        this.validacao = validacao;
    }

    public ClienteSinteticoDao getClienteSinteticoDao() {return clienteSinteticoDao; }

    public void setClienteSinteticoDao(ClienteSinteticoDao clienteSinteticoDao) {this.clienteSinteticoDao = clienteSinteticoDao; }

    public NivelDao getNivelDao() {
        return this.nivelDao;
    }

    public void setNivelDao(NivelDao nivelDao) {
        this.nivelDao = nivelDao;
    }

    public Nivel alterar(final String ctx, Nivel object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getNivelDao().update(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void excluir(final String ctx, Nivel object) throws ServiceException {
        try {
            getNivelDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public Nivel inserir(final String ctx, Nivel object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            return getNivelDao().insert(ctx, object);
        } catch (ValidacaoException vex) {
            throw vex;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void validarDados(final String ctx, Nivel object) throws ServiceException {
        if (object.getNome() == null || object.getNome().trim().isEmpty()) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(NivelExcecoes.VALIDACAO_NOME_NIVEL);
            }
            throw new ValidacaoException("validacao.nome");
        }
        if (getNivelDao().exists(ctx, object, "nome")) {
            if (!JSFUtilities.isJSFContext()){
                throw new ServiceException(NivelExcecoes.VALIDACAO_NIVEL_JA_EXISTE);
            }
            throw new ValidacaoException("cadastros.existente");
        }
    }

    public Nivel obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getNivelDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public Nivel obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getNivelDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Nivel> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getNivelDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Nivel> obterPorParam(final String ctx, String query,
            Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getNivelDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<Nivel> obterTodos(final String ctx) throws ServiceException {
        try {
            return getNivelDao().findListByAttributes(ctx, null, null, "nome", 0);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public List<NivelResponseTO>consultar(String nome, Integer ordem)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        return getNivelDao().consultar(ctx, nome, ordem);
    }

    public List<NivelResponseTO>consultarNivel(FiltroNivelJSON filtros, PaginadorDTO paginadorDTO)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        if (!StringUtils.isBlank(paginadorDTO.getSort())) {
            paginadorDTO.setSort(paginadorDTO.getSort());
        } else {
            paginadorDTO.setSort("nome,ASC");        }

        List<Nivel> lista = getNivelDao().consultarNivel(ctx, filtros, paginadorDTO);
        List<NivelResponseTO> listaRet = new ArrayList<>();
        if (lista != null) {
            for (Nivel nivel : lista) {
                listaRet.add(new NivelResponseTO(nivel));
            }
        }


        return listaRet;
    }

    public NivelResponseTO inserir(NivelTO nivelTO)throws ServiceException{
        try{
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Nivel nivel = montarValidarDados(ctx,nivelTO);
            Nivel novoNivel = getNivelDao().insert(ctx, nivel);
            incluirLog(ctx, novoNivel.getCodigo().toString(), "", "", novoNivel.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE NÍVEL", EntidadeLogEnum.NIVEL, "Nível", sessaoService, logDao, null, null);
            return new NivelResponseTO(novoNivel);
        }catch (Exception e){
            throw new ServiceException(NivelExcecoes.ERRO_INCLUIR_NIVEL, e);
        }
    }

    
    public NivelResponseTO alterar(Integer id, NivelTO nivelTO)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        Nivel nivel = null;
        try{
            nivel = getNivelDao().findById(ctx, id);
            if (nivel == null || nivel.getCodigo() == null){
                throw new ServiceException(NivelExcecoes.NIVEL_NAO_ENCONTRADO);
            }
            Nivel objAntes = UtilReflection.copy(nivel);
            nivel = montarValidarDados(ctx,nivelTO);
            nivel = getNivelDao().update(ctx, nivel);

            incluirLog(ctx, nivel.getCodigo().toString(), null, objAntes.getDescricaoParaLog(nivel), nivel.getDescricaoParaLog(objAntes), "ALTERAÇÃO", "ALTERAÇÃO DE NÍVEL", EntidadeLogEnum.NIVEL, "Nível", sessaoService, logDao, null, null);

            return new NivelResponseTO(nivel);
        }catch (Exception e){
            throw new ServiceException(NivelExcecoes.ERRO_ALTERAR_NIVEL, e);
        }
    }
    private Nivel montarValidarDados(String ctx, NivelTO nivelTO)throws  ServiceException{
        Nivel nivel = new Nivel();
        nivel.setNome(nivelTO.getNome());
        nivel.setOrdem(nivelTO.getOrdem());
        nivel.setCodigo(nivelTO.getId());
        nivel.setAtivo(nivelTO.getAtivo());
        validarDados(ctx, nivel);
        if (nivelTO.getOrdem() == null){
            Number maiorOrdem = getNivelDao().consultarNivelMaiorOrdem(ctx, nivel.getCodigo());
            if (maiorOrdem != null) {
                nivel.setOrdem(maiorOrdem.intValue() + 1);
            } else {
                nivel.setOrdem(1);
            }
        } else if(nivelTO.getOrdem() < 1){
            throw new ServiceException(NivelExcecoes.VALIDACAO_ORDEM_INVALIDA);
        }
        return nivel;
    }

    public void excluir(Integer id)throws ServiceException{
        String ctx = sessaoService.getUsuarioAtual().getChave();
        prepare(ctx);
        Nivel nivel = null;
        try{
            nivel = getNivelDao().findById(ctx, id);

        }catch (Exception e){
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEL, e);
        }

        if ((nivel == null) || (nivel.getCodigo() == null) || (nivel.getCodigo() <= 0)){
            throw new ServiceException(NivelExcecoes.NIVEL_NAO_ENCONTRADO);
        }
       incluirLog(ctx, nivel.getCodigo().toString(), "", "", nivel.getDescricaoParaLog(null), "EXCLUSÃO", "EXCLUSÃO DE NÍVEL", EntidadeLogEnum.NIVEL, "Nível", sessaoService, logDao, null, null);


        try{
            if(getClienteSinteticoDao().consultarClientesSinteticosNiveis(id, ctx)){
                throw new ServiceException(NivelExcecoes.ERRO_EXCLUIR_NIVEL_VINCULO);
            }
            getNivelDao().delete(ctx, nivel);
            incluirLog(ctx, nivel.getCodigo().toString(), "", nivel.getDescricaoParaLog(null), "", "EXCLUSÃO", "EXCLUSÃO DE NÍVEL", EntidadeLogEnum.NIVEL, "Nível", sessaoService, logDao, null, null);
        }catch (Exception e){
            throw new ServiceException(NivelExcecoes.ERRO_EXCLUIR_NIVEL, e);
        }

    }

    @Override
    public List<NivelResponseTO> listarTodosNiveis() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            List<NivelResponseTO> listaRet = new ArrayList<>();

            List<Nivel> niveis = nivelDao.findAll(ctx);
            if (!UteisValidacao.emptyList(niveis)) {
                for (Nivel nivel : niveis) {
                    listaRet.add(new NivelResponseTO(nivel));
                }
            }

            return listaRet;
        } catch (Exception ex) {
            throw new ServiceException(NivelExcecoes.ERRO_BUSCAR_NIVEIS);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public List<LogTO> listarLog(JSONObject filtros, PaginadorDTO paginadorDTO, Integer codigoNivel) throws ServiceException{
        List<LogTO> listarLog = new ArrayList<>();
        try {
            String quicksearchValue = filtros.optString("quicksearchValue");
            JSONArray tipos = filtros.optJSONArray("tipo");
            Long inicio = filtros.optLong("dataInicio");
            Long fim = filtros.optLong("dataFim");
            Integer codigoTp = null;
            try {
                codigoTp = quicksearchValue == null ? null : Integer.valueOf(quicksearchValue);
            }catch (Exception e){
                //ignore
            }
            String ctx = sessaoService.getUsuarioAtual().getChave();

            StringBuilder sql = new StringBuilder();

            sql.append("select _campos_ \n");
            sql.append("from customrevisionentity c \n");
            sql.append(" inner join nivel_aud ta on ta.rev = c.id \n");
            sql.append(" left join nivel_aud ta2 on ta2.codigo = ta.codigo and ta.revtype = 2 and ta2.revtype = 0  \n");
            sql.append(" where (ta.revtype <> 2 or ta2.codigo is not null) \n");
            if (inicio != null && inicio > 0l) {
                Long dataInicio = Calendario.getDataComHoraZerada(new Date(inicio)).getTime();
                sql.append(" AND c.\"timestamp\" >= ").append(dataInicio).append(" \n");
            }
            if (fim != null && fim > 0l) {
                Long dataFim = Calendario.fimDoDia(new Date(fim)).getTime();
                sql.append(" AND c.\"timestamp\" <= ").append(dataFim).append(" \n");
            }

            if(tipos != null && tipos.length() > 0){
                String tiposCod = "";
                for(int i = 0; i < tipos.length(); i++){
                    tiposCod += "," + TipoRevisaoEnum.valueOf(tipos.getString(i)).getId();
                }
                sql.append(" and ta.revtype in ( ").append(tiposCod.replaceFirst(",", "")).append(" )");
            }
            if(UteisValidacao.emptyNumber(codigoNivel)){
                sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : " and (");
                if (codigoTp != null){
                    sql.append(" ta.codigo = ").append(codigoTp).append(" or \n");
                }
                if (!UteisValidacao.emptyString(quicksearchValue)) {
                    sql.append("  upper(ta.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(ta2.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%' \n");
                }
                sql.append(UteisValidacao.emptyString(quicksearchValue) ? "" : ")");
            } else {
                sql.append(" and ta.codigo = ").append(codigoNivel).append("  \n");
                if (!UteisValidacao.emptyString(quicksearchValue)) {
                    sql.append(" and ( upper(ta.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(ta2.nome) like '%").append(quicksearchValue.toUpperCase()).append("%' or \n");
                    sql.append("  upper(c.username) like '%").append(quicksearchValue.toUpperCase()).append("%') \n");
                }
            }

            Integer maxResults = 10;
            Integer indiceInicial = 0;
            if (paginadorDTO != null) {
                maxResults = paginadorDTO.getSize() == null ? 10 : paginadorDTO.getSize().intValue();
                indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
            }
            try (ResultSet rs = nivelDao.createStatement(ctx,
                    sql.toString().replace("_campos_", "ta2.nome as nomeexcluido, ta.codigo, ta.nome, ta.ordem, ta.ativo,\n" +
                                    "c.\"timestamp\", c.username, ta.revtype, c.id ")
                            .concat("order by \"timestamp\" desc, id desc limit ".concat(maxResults.toString()))
                            .concat(" offset ".concat(indiceInicial.toString())))) {

                while (rs.next()) {
                    TipoRevisaoEnum revtype = TipoRevisaoEnum.getFromId(rs.getInt("revtype"));
                    Integer codigo = rs.getInt("codigo");
                    Long timestamp = rs.getLong("timestamp");
                    Long rev = rs.getLong("id");
                    String username = rs.getString("username");
                    Map<String, String> valoresAlterados = Uteis.montarMapa(rs, "nome", "ordem", "ativo");
                    Map<String, String> valoresAnteriores = null;
                    LogTO logTO = new LogTO(codigo, revtype.getDescricaoLog(),
                            revtype.equals(TipoRevisaoEnum.DELETE) ? rs.getString("nomeexcluido") : rs.getString("nome"),
                            username,
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "dd/MM/yyyy HH:mm"),
                            Uteis.getDataAplicandoFormatacao(new Date(timestamp), "HH:mm:ss"),
                            new ArrayList());

                    AlteracoesTO alteracoesTO = null;
                    if (revtype.equals(TipoRevisaoEnum.UPDATE) || revtype.equals(TipoRevisaoEnum.DELETE)) {
                        try (ResultSet rsAnterior = nivelDao.createStatement(ctx,
                                "select ta.nome, ta.ordem, ta.ativo from nivel_aud ta where codigo = " + codigo +
                                        " and rev < " + rev +
                                        " order by rev desc limit 1 ")) {
                            if (rsAnterior.next()) {
                                valoresAnteriores = Uteis.montarMapa(rsAnterior, "nome", "ordem", "ativo");
                            }
                        }
                    }
                    logTO.setAlteracoes(Uteis.compararMapas(valoresAnteriores, valoresAlterados));

                    for (AlteracoesTO a : logTO.getAlteracoes()) {
                        logTO.setDescricao(logTO.getDescricao() + ("[" + a.getCampo() + ":'"
                                + (UteisValidacao.emptyString(a.getValorAnterior()) ? "" : (a.getValorAnterior() + "' para '"))
                                + a.getValorAlterado() + "']<br/>"));
                    }
                    listarLog.add(logTO);
                }
            }
            try (ResultSet rsCount = nivelDao.createStatement(ctx, sql.toString().replace("_campos_", " count(c.id) as cont "))) {
                if (rsCount.next()) {
                    paginadorDTO.setQuantidadeTotalElementos(rsCount.getLong("cont"));
                } else {
                    paginadorDTO.setQuantidadeTotalElementos(10l);
                }
            }


        }catch (Exception e){
            throw new ServiceException(e);
        }
        return listarLog;

    }

}
