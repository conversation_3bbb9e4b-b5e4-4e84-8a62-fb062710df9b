package br.com.pacto.service.impl.crossfit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR> 03/04/2019
 */
@ApiModel(description = "Dados de agendamentos de alunos para aulas de Crossfit")
public class AgendamentosAlunosDTO {

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "54321")
    private Integer matricula;

    @ApiModelProperty(value = "Nome completo do aluno", example = "<PERSON>")
    private String nome;

    @ApiModelProperty(value = "Modalidade da aula agendada", example = "Crossfit")
    private String modalidade;

    @ApiModelProperty(value = "Data e horário da aula agendada", example = "2024-06-20T18:00:00")
    private Date dia;

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }
}
