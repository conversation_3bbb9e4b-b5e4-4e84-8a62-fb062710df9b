package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações do músculo")
public class AtividadeMusculoResponseTO {
    @ApiModelProperty(value = "Código único identificador do músculo treinado na atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do músculo treino na atividade", example = "Latíssimo do dorso")
    private String nome;

    public AtividadeMusculoResponseTO() {

    }

    public AtividadeMusculoResponseTO(AtividadeMusculo am) {
        this.id = am.getMusculo().getCodigo();
        this.nome = am.getMusculo().getNome();
    }

    public AtividadeMusculoResponseTO(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
