package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da atividade física")
public class AtividadeTO {

    @ApiModelProperty(value = "Código único identificador da atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade física", example = "Treino de Costas")
    private String nome;
    @ApiModelProperty(value = "Indica se a atividade está ativa", example = "true")
    private Boolean ativa;
    @ApiModelProperty(value = "Indica se a série é de duração", example = "false")
    private Boolean serieApenasDuracao;
    @ApiModelProperty(value = "Tipo da atividade física. <br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
            "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
            "</ul>", example = "0", allowableValues = "0,1")
    private TipoAtividadeEndpointEnum tipo;
    @ApiModelProperty(value = "Descrição da atividade física", example = "Ativida física recomendada para fortalecimento do grupo muscular das costas")
    private String descricao;
    @ApiModelProperty(value = "Empresas que a atividade física está relacionada")
    private List<AtividadeEmpresaTO> empresas;
    @ApiModelProperty(value = "URL do vídeo da atividade", example = "www.youtube.com/video-exemplo")
    private String videoUri;
    @ApiModelProperty(value = "Lista de links de vídeos relacionados a atividade")
    private List<AtividadeVideoTO> linkVideos;
    @ApiModelProperty(value = "Lista de imagens da atividade")
    private List<AtividadeImagemTO> images;
    @ApiModelProperty(value = "Imagens que serão enviadas para o cadastro da atividade")
    private List<AtividadeImagemUploadTO> imageUploads;
    @ApiModelProperty(value = "Lista dos códigos das atividades relacionadas a que está sendo cadastrada", example = "[1,2,3]")
    private List<Integer> atividadesRelacionadas;
    @ApiModelProperty(value = "Lista dos códigos das categoriais relacionadas a atividade", example = "[1,2]")
    private List<Integer> categoriaAtividadeIds;
    @ApiModelProperty(value = "Lista dos códigos dos aparelhos relacionados as atividades", example = "[1,2]")
    private List<Integer> aparelhoIds;
    @ApiModelProperty(value = "Lista dos códigos dos grupos musculares que são exercitados por essa ativdade", example = "[1]")
    private List<Integer> grupoMuscularIds;
    @ApiModelProperty(value = "Lista dos códigos dos músculos que são exercitados por essa atividade", example = "[1]")
    private List<Integer> musculoIds;
    @ApiModelProperty(value = "Lista dos níveis relacionados a essa atividade", example = "[3]")
    private List<Integer> nivelIds;
    @ApiModelProperty(value = "Indica se é uma atividade do crossfit", example = "false")
    private Boolean crossfit = false;
    @ApiModelProperty(value = "Indica se deve usar na pescrição de treinos", example = "true")
    private Boolean usarNaPrescricao = Boolean.TRUE;

    public List<Integer> getAtividadesRelacionadas() {
        return atividadesRelacionadas;
    }

    public void setAtividadesRelacionadas(List<Integer> atividadesRelacionadas) {
        this.atividadesRelacionadas = atividadesRelacionadas;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<AtividadeEmpresaTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaTO> empresas) {
        this.empresas = empresas;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public List<AtividadeImagemTO> getImages() {
        return images;
    }

    public void setImages(List<AtividadeImagemTO> images) {
        this.images = images;
    }

    public List<AtividadeImagemUploadTO> getImageUploads() {
        return imageUploads;
    }

    public void setImageUploads(List<AtividadeImagemUploadTO> imageUploads) {
        this.imageUploads = imageUploads;
    }

    public List<Integer> getCategoriaAtividadeIds() {
        return categoriaAtividadeIds;
    }

    public void setCategoriaAtividadeIds(List<Integer> categoriaAtividadeIds) {
        this.categoriaAtividadeIds = categoriaAtividadeIds;
    }

    public List<Integer> getAparelhoIds() {
        return aparelhoIds;
    }

    public void setAparelhoIds(List<Integer> aparelhoIds) {
        this.aparelhoIds = aparelhoIds;
    }

    public List<Integer> getGrupoMuscularIds() {
        return grupoMuscularIds;
    }

    public void setGrupoMuscularIds(List<Integer> grupoMuscularIds) {
        this.grupoMuscularIds = grupoMuscularIds;
    }

    public List<Integer> getMusculoIds() {
        return musculoIds;
    }

    public void setMusculoIds(List<Integer> musculoIds) {
        this.musculoIds = musculoIds;
    }

    public List<Integer> getNivelIds() {
        return nivelIds;
    }

    public void setNivelIds(List<Integer> nivelIds) {
        this.nivelIds = nivelIds;
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public Boolean getUsarNaPrescricao() {
        return usarNaPrescricao;
    }

    public void setUsarNaPrescricao(Boolean usarNaPrescricao) {
        this.usarNaPrescricao = usarNaPrescricao;
    }

    public List<AtividadeVideoTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<AtividadeVideoTO> linkVideos) {
        this.linkVideos = linkVideos;
    }
}
