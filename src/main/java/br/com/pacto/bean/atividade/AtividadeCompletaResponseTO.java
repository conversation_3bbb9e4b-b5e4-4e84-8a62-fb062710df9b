package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da atividade")
public class  AtividadeCompletaResponseTO {

    @ApiModelProperty(value = "Código único identificador da atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade física", example = "Treino de Costas")
    private String nome;
    @ApiModelProperty(value = "Segundo código identificador da atividade pela Inteligência Artificial", example = "2")
    private Integer idIA2;
    @ApiModelProperty(value = "Nome original da atividade física criado pela inteligência artificial", example = "Treino de Costas Avançado")
    private String nomeOriginalIA;
    @ApiModelProperty(value = "Nome da pessoa que realizou a edição da atividade", example = "Augusto")
    private String editadoPor;
    @ApiModelProperty(value = "Data em timestamp da última edição realizada", example = "1743897600")
    private Long ultimaEdicao;
    @ApiModelProperty(value = "Indica se a atividade está ativa", example = "true")
    private Boolean ativa;
    @ApiModelProperty(value = "Indica se deve usar essa atividade na prescrição de treino", example = "true")
    private Boolean usarNaPrescricao;
    @ApiModelProperty(value = "Indica se esta atividade deve estar no treino", example = "true")
    private Boolean estaNoTreino;
    @ApiModelProperty(value = "Indica se a série é de duração", example = "false")
    private Boolean serieApenasDuracao;
    @ApiModelProperty(value = "Tipo da atividade física. <br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
            "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
            "</ul>", example = "0", allowableValues = "0,1")
    private TipoAtividadeEndpointEnum tipo;
    @ApiModelProperty(value = "Descrição da atividade física", example = "Ativida física recomendada para fortalecimento do grupo muscular das costas")
    private String descricao;
    @ApiModelProperty(value = "Empresas que a atividade física está relacionada")
    private List<AtividadeEmpresaResponseTO> empresas = new ArrayList<>();
    @ApiModelProperty(value = "URL do vídeo da atividade", example = "www.youtube.com/video-exemplo")
    private String videoUri;
    @ApiModelProperty(value = "Lista de imagens da atividade")
    private List<AtividadeImagemResponseTO> images = new ArrayList<>();
    @ApiModelProperty(value = "Lista de categorias da atividade")
    private List<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade = new ArrayList<>();
    @ApiModelProperty(value = "Lista de aparelhos da atividade física")
    private List<AtividadeAparelhoResponseTO> aparelhos = new ArrayList<>();
    @ApiModelProperty(value = "Lista de grupos musculares que são exercitados nessa atividade física")
    private List<AtividadeGrupoMuscularResponseTO> gruposMusculares = new ArrayList<>();
    @ApiModelProperty(value = "Lista de músculos que são exercitados nessa atividade física")
    private List<AtividadeMusculoResponseTO> musculos = new ArrayList<>();
    @ApiModelProperty(value = "Lista de níveis relacionado a atividade física")
    private List<AtividadeNivelResponseTO> niveis = new ArrayList<>();
    @ApiModelProperty(value = "Lista de atividades relacionadas a essa atividade")
    private List<AtividadeRelacionadaResponseTO> atividadesRelacionadas = new ArrayList<>();
    @ApiModelProperty(value = "Lista de links de vídeos relacionados a atividade")
    private List<AtividadeVideoTO> linkVideos = new ArrayList<>();

    public AtividadeCompletaResponseTO() {
    }

    public AtividadeCompletaResponseTO(Atividade atividade) {
        this(atividade, false);
    }

    public AtividadeCompletaResponseTO(Atividade atividade, Boolean estaNoTreino) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.idIA2 = atividade.getIdIA2();
        this.nomeOriginalIA = atividade.getNomeOriginalIA();
        this.editadoPor = atividade.getEditadoPor();
        this.ultimaEdicao = atividade.getUltimaEdicao();
        this.estaNoTreino = estaNoTreino;
        this.ativa = atividade.isAtivo();
        this.serieApenasDuracao = atividade.getSeriesApenasDuracao();
        if (atividade.getTipo() != null) {
            this.tipo = atividade.getTipo().getTipoAtividadeEndpointEnum();
        }
        this.descricao = atividade.getDescricao();
        if (atividade.getEmpresasHabilitadas() != null) {
            for (AtividadeEmpresa ae : atividade.getEmpresasHabilitadas()) {
                this.empresas.add(new AtividadeEmpresaResponseTO(ae));
            }
        }
        this.videoUri = atividade.getLinkVideo();
        this.usarNaPrescricao = atividade.getUsarNaPrescricao();
        if (atividade.getAnimacoes() != null) {
            for (AtividadeAnimacao aa : atividade.getAnimacoes()) {
                this.images.add(new AtividadeImagemResponseTO(aa));
            }
        }
        if (atividade.getCategorias() != null) {
            for (AtividadeCategoriaAtividade aca : atividade.getCategorias()) {
                this.categoriasAtividade.add(new AtividadeCategoriaAtividadeResponseTO(aca));
            }
        }
        if (!atividade.getAparelhos().isEmpty()) {
            List<AtividadeAparelho> aparelho = new ArrayList<AtividadeAparelho>();
            for (int i = 0; i < atividade.getAparelhos().size(); i++) {
                if (atividade.getAparelhos().get(i).getAparelho() != null) {
                    aparelho.add(atividade.getAparelhos().get(i));

                }
            }
            for (AtividadeAparelho aa : aparelho) {
                this.aparelhos.add(new AtividadeAparelhoResponseTO(aa));
            }
        }
        if (atividade.getGruposMusculares() != null) {
            for (AtividadeGrupoMuscular agm : atividade.getGruposMusculares()) {
                this.gruposMusculares.add(new AtividadeGrupoMuscularResponseTO(agm));
            }
        }
        if (atividade.getMusculos() != null) {
            for (AtividadeMusculo am : atividade.getMusculos()) {
                this.musculos.add(new AtividadeMusculoResponseTO(am));
            }
        }
        if (atividade.getNiveis() != null) {
            for (AtividadeNivel an : atividade.getNiveis()) {
                this.niveis.add(new AtividadeNivelResponseTO(an));
            }
        }
        if (atividade.getLinkVideos() != null) {
            for (AtividadeVideo aa : atividade.getLinkVideos()) {
                this.linkVideos.add(new AtividadeVideoTO(aa));
            }
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public Boolean getSerieApenasDuracao() {
        return serieApenasDuracao;
    }

    public void setSerieApenasDuracao(Boolean serieApenasDuracao) {
        this.serieApenasDuracao = serieApenasDuracao;
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<AtividadeEmpresaResponseTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<AtividadeEmpresaResponseTO> empresas) {
        this.empresas = empresas;
    }

    public String getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(String videoUri) {
        this.videoUri = videoUri;
    }

    public List<AtividadeImagemResponseTO> getImages() {
        return images;
    }

    public void setImages(List<AtividadeImagemResponseTO> images) {
        this.images = images;
    }

    public List<AtividadeCategoriaAtividadeResponseTO> getCategoriasAtividade() {
        return categoriasAtividade;
    }

    public void setCategoriasAtividade(List<AtividadeCategoriaAtividadeResponseTO> categoriasAtividade) {
        this.categoriasAtividade = categoriasAtividade;
    }

    public List<AtividadeAparelhoResponseTO> getAparelhos() {
        return aparelhos;
    }

    public void setAparelhos(List<AtividadeAparelhoResponseTO> aparelhos) {
        this.aparelhos = aparelhos;
    }

    public List<AtividadeGrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<AtividadeGrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

    public List<AtividadeMusculoResponseTO> getMusculos() {
        return musculos;
    }

    public void setMusculos(List<AtividadeMusculoResponseTO> musculos) {
        this.musculos = musculos;
    }

    public List<AtividadeNivelResponseTO> getNiveis() {
        return niveis;
    }

    public Boolean getEstaNoTreino() {
        return estaNoTreino;
    }

    public void setEstaNoTreino(Boolean estaNoTreino) {
        this.estaNoTreino = estaNoTreino;
    }

    public void setNiveis(List<AtividadeNivelResponseTO> niveis) {
        this.niveis = niveis;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeCompletaResponseTO that = (AtividadeCompletaResponseTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public Integer getIdIA2() {
        return idIA2;
    }

    public void setIdIA2(Integer idIA2) {
        this.idIA2 = idIA2;
    }

    public String getNomeOriginalIA() {
        return nomeOriginalIA;
    }

    public void setNomeOriginalIA(String nomeOriginalIA) {
        this.nomeOriginalIA = nomeOriginalIA;
    }

    public String getEditadoPor() {
        return editadoPor;
    }

    public void setEditadoPor(String editadoPor) {
        this.editadoPor = editadoPor;
    }

    public Long getUltimaEdicao() {
        return ultimaEdicao;
    }

    public void setUltimaEdicao(Long ultimaEdicao) {
        this.ultimaEdicao = ultimaEdicao;
    }

    public List<AtividadeRelacionadaResponseTO> getAtividadesRelacionadas() {
        return atividadesRelacionadas;
    }

    public void setAtividadesRelacionadas(List<AtividadeRelacionadaResponseTO> atividadesRelacionadas) {
        this.atividadesRelacionadas = atividadesRelacionadas;
    }

    public Boolean getUsarNaPrescricao() {
        return usarNaPrescricao;
    }

    public void setUsarNaPrescricao(Boolean usarNaPrescricao) {
        this.usarNaPrescricao = usarNaPrescricao;
    }

    public List<AtividadeVideoTO> getLinkVideos() {
        return linkVideos;
    }

    public void setLinkVideos(List<AtividadeVideoTO> linkVideos) {
        this.linkVideos = linkVideos;
    }
}
