package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações dos vídeos da atividade")
public class AtividadeVideoTO {

    @ApiModelProperty(value = "Código único identificador do vídeo", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Link do vídeo", example = "www.youtube.com/exemplo-atividade")
    private String linkVideo;
    @ApiModelProperty(value = "Indica se o vídeo é de um professor", example = "false")
    private Boolean professor = false;

    public AtividadeVideoTO() {
    }

    public AtividadeVideoTO(AtividadeVideo aa) {
        this.id = aa.getCodigo();
        this.linkVideo = aa.getLinkvideo();
        this.professor = aa.getProfessor();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLinkVideo() {
        return linkVideo;
    }

    public void setLinkVideo(String linkVideo) {
        this.linkVideo = linkVideo;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeVideoTO that = (AtividadeVideoTO) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
