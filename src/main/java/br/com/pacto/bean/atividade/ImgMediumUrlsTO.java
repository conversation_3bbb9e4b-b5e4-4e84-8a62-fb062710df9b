package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações de imagem em tamanho médio da atividade")
public class ImgMediumUrlsTO {

    @ApiModelProperty(value = "Link da imagem em tamanho médio", example = "https://exemplo.com/medium_supino.jpg")
    private String linkImg;

    @ApiModelProperty(value = "Indica se a imagem é de um professor", example = "false")
    private Boolean professor = false;

    public ImgMediumUrlsTO(){
    }

    public String getLinkImg() {
        return linkImg;
    }

    public void setLinkImg(String linkImg) {
        this.linkImg = linkImg;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }

}
