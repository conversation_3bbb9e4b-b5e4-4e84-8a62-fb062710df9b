package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da imagem da atividade")
public class AtividadeImagemTO {

    @ApiModelProperty(value = "Tipo da imagem da atividade<br/><strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - CATALOG (Imagem de catálogo)</li>" +
            "<li>1 - UPLOAD (Imagem enviada pelo usuário)</li>" +
            "</ul>", example = "1")
    private TipoAtividadeImagemEnum type;
    @ApiModelProperty(value = "Código único identificador da imagem", example = "1")
    private String id;
    @ApiModelProperty(value = "Indica se a imagem é do professor", example = "false")
    private Boolean professor;

    public TipoAtividadeImagemEnum getType() {
        return type;
    }

    public void setType(TipoAtividadeImagemEnum type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }
}
