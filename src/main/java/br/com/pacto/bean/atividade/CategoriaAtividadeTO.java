package br.com.pacto.bean.atividade;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;


@JsonInclude(JsonInclude.Include.NON_NULL)
public class CategoriaAtividadeTO {

    @ApiModelProperty(value = "Código único identificador da categoria de atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da categoria", example = "Musculação")
    private String nome;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }


}
