package br.com.pacto.bean.atividade;

import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Objects;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações simplificadas das atividades")
public class AtividadeSimplesResponseTO {

    @ApiModelProperty(value = "Código único identificador da atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade física", example = "Treino de Costas")
    private String nome;
    @ApiModelProperty(value = "Indica se esta atividade deve estar no treino", example = "true")
    private Boolean estaNoTreino;
    @ApiModelProperty(value = "Imagem da atividade", example = "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAANElEQVR4nO3BAQ0AAADCoPdPbQ43oA...")
    private String image;
    @ApiModelProperty(value = "Imagem pequena da atividade", example = "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAANElEQVR4nO3BAQ0AAADCoPdPbQ43oA...")
    private String imagePequena;
    @ApiModelProperty(value = "Imagem miniatura da atividade", example = "iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAANElEQVR4nO3BAQ0AAADCoPdPbQ43oA...")
    private String imageMiniatura;
    @ApiModelProperty(value = "Tipo da atividade física. <br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - NEUROMUSCULAR (Neuromuscular)</li>" +
            "<li>1 - CARDIOVASCULAR (Cardiovascular)</li>" +
            "</ul>", example = "0", allowableValues = "0,1")
    private TipoAtividadeEndpointEnum tipo;
    @ApiModelProperty(value = "Lista de grupos musculares que são exercitados nessa atividade física")
    private List<GrupoMuscularResponseTO> gruposMusculares;

    public AtividadeSimplesResponseTO(Atividade atividade, Boolean estaNoTreino) {
        this.id = atividade.getCodigo();
        this.nome = atividade.getNome();
        this.estaNoTreino = estaNoTreino;
        if (atividade.getTipo() != null) {
            this.tipo = atividade.getTipo().getTipoAtividadeEndpointEnum();
        }
        if (atividade.getAnimacoes() != null && !atividade.getAnimacoes().isEmpty()) {
            this.image = new AtividadeImagemResponseTO(atividade.getAnimacoes().get(0)).getUri();
            this.imagePequena = new AtividadeImagemResponseTO(atividade.getAnimacoes().get(0)).getFotoKeyPequena();
            this.imageMiniatura = new AtividadeImagemResponseTO(atividade.getAnimacoes().get(0)).getFotoKeyMiniatura();
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getEstaNoTreino() {
        return estaNoTreino;
    }

    public void setEstaNoTreino(Boolean estaNoTreino) {
        this.estaNoTreino = estaNoTreino;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AtividadeSimplesResponseTO that = (AtividadeSimplesResponseTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public TipoAtividadeEndpointEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAtividadeEndpointEnum tipo) {
        this.tipo = tipo;
    }

    public String getImagePequena() {
        return imagePequena;
    }

    public void setImagePequena(String imagePequena) {
        this.imagePequena = imagePequena;
    }

    public String getImageMiniatura() {
        return imageMiniatura;
    }

    public void setImageMiniatura(String imageMiniatura) {
        this.imageMiniatura = imageMiniatura;
    }

    public List<GrupoMuscularResponseTO> getGruposMusculares() {
        return gruposMusculares;
    }

    public void setGruposMusculares(List<GrupoMuscularResponseTO> gruposMusculares) {
        this.gruposMusculares = gruposMusculares;
    }

}
