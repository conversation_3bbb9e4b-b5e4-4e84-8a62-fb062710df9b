package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da categoria de atividade")
public class CategoriaAtividadeResponseTO {
    @ApiModelProperty(value = "Código único identificador da categoria de atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da categoria", example = "Musculação")
    private String nome;

    public CategoriaAtividadeResponseTO(CategoriaAtividade categoriaAtividade){
        this.id = categoriaAtividade.getCodigo();
        this.nome = categoriaAtividade.getNome();
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
