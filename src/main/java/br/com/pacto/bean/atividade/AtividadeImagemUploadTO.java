package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 23/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações das imagens que estão sendo enviadas para cadastro")
public class AtividadeImagemUploadTO {
    @ApiModelProperty(value = "Dados da imagem que está sendo cadastrada (Formato Byte)", example = "R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs=")
    private byte[] data;
    @ApiModelProperty(value = "Nome da imagem que será cadastrada no sistema", example = "Atividade")
    private String nome;
    @ApiModelProperty(value = "Indica se a imagem é de um professor", example = "false")
    private <PERSON><PERSON><PERSON> professor;

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getProfessor() {
        return professor;
    }

    public void setProfessor(Boolean professor) {
        this.professor = professor;
    }
}
