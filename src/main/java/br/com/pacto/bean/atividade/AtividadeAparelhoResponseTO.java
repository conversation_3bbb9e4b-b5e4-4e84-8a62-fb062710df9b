package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 29/07/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações dos aparelhos relacionados a atividade")
public class AtividadeAparelhoResponseTO {
    @ApiModelProperty(value = "Código único identificador do aparelho da atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome do aparelho", example = "Remada Cavalinho")
    private String nome;


    public AtividadeAparelhoResponseTO(){

    }

    public AtividadeAparelhoResponseTO(AtividadeAparelho aa){
        this.id = aa.getAparelho().getCodigo();
        this.nome = aa.getAparelho().getNome();
    }

    public AtividadeAparelhoResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

}
