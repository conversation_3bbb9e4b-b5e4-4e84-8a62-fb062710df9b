package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 22/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da empresa")
public class AtividadeEmpresaTO {

    @ApiModelProperty(value = "Código único identificador da empresa", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Identificador da empresa", example = "PACTO/GO")
    private String identificador;
    @ApiModelProperty(value = "Informações básicas da empresa")
    private EmpresaBasicaTO empresa;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public EmpresaBasicaTO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaBasicaTO empresa) {
        this.empresa = empresa;
    }

}
