package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da atividade relacionada a outra ativiade")
public class AtividadeRelacionadaResponseTO {
    @ApiModelProperty(value = "Código único identificador da atividade relacionada", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da atividade relacionada", example = "Treino Superior Costas")
    private String nome;
    @ApiModelProperty(value = "Lista de imagens da atividade")
    private List<AtividadeImagemResponseTO> imagem;

    public AtividadeRelacionadaResponseTO() {

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AtividadeImagemResponseTO> getImagem() {
        return imagem;
    }

    public void setImagem(List<AtividadeImagemResponseTO> imagem) {
        this.imagem = imagem;
    }
}
