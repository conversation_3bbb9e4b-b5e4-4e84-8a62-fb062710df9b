package br.com.pacto.bean.atividade;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 27/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações da categoria da atividade")
public class AtividadeCategoriaAtividadeResponseTO {
    @ApiModelProperty(value = "Código único identificador da categoria da atividade", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Nome da categoria da atividade", example = "MUSCULAÇÃO")
    private String nome;

    public AtividadeCategoriaAtividadeResponseTO(){

    }

    public AtividadeCategoriaAtividadeResponseTO(AtividadeCategoriaAtividade aca) {
        this.id = aca.getCategoriaAtividade().getCodigo();
        this.nome = aca.getCategoriaAtividade().getNome();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
