package br.com.pacto.bean.aparelho;

import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeAparelhoResponseTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 01/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AparelhoResponseTO {

    @ApiModelProperty(value = "Código identificador único do aparelho", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do aparelho", example = "Esteira Ergométrica")
    private String nome;

    @ApiModelProperty(value = "Lista de ajustes disponíveis para o aparelho")
    private List<AparelhoAjusteResponseTO> ajustes = new ArrayList<>();

    @ApiModelProperty(value = "Lista de atividades que podem ser realizadas no aparelho")
    private List<AtividadeAparelhoResponseTO> atividades = new ArrayList<>();

    public AparelhoResponseTO(){

    }

    public AparelhoResponseTO(Aparelho aparelho){
        this.id = aparelho.getCodigo();
        this.nome = aparelho.getNome();
        for (AparelhoAjuste aparelhoAjuste: aparelho.getAjustes()){
            getAjustes().add(new AparelhoAjusteResponseTO(aparelhoAjuste.getCodigo(), aparelhoAjuste.getNome()));
        }
        for (AtividadeAparelho atividadeAparelho: aparelho.getAtividades()){
            getAtividades().add(new AtividadeAparelhoResponseTO(atividadeAparelho.getCodigo(), atividadeAparelho.getAtividade().getNome()));
        }
    }

    public AparelhoResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<AparelhoAjusteResponseTO> getAjustes() {
        return ajustes;
    }

    public void setAjustes(List<AparelhoAjusteResponseTO> ajustes) {
        this.ajustes = ajustes;
    }

    public List<AtividadeAparelhoResponseTO> getAtividades() {
        return atividades;
    }

    public void setAtividades(List<AtividadeAparelhoResponseTO> atividades) {
        this.atividades = atividades;
    }
}
