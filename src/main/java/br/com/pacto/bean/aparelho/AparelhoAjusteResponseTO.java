package br.com.pacto.bean.aparelho;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 29/07/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações dos ajustes disponíveis para o aparelho")
public class AparelhoAjusteResponseTO {

    @ApiModelProperty(value = "Código único identificador do ajuste", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Nome do ajuste disponível para o aparelho", example = "Altura do banco")
    private String nome;

    public AparelhoAjusteResponseTO(){

    }

    public AparelhoAjusteResponseTO(Integer id, String nome){
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
