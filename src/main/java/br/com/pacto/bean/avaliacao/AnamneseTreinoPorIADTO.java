package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados da anamnese para geração de programa de treino por Inteligência Artificial")
public class AnamneseTreinoPorIADTO {

    @ApiModelProperty(value = "Idade do cliente em anos", example = "28")
    private Integer age;

    @ApiModelProperty(value = "Altura do cliente em centímetros", example = "175")
    private Integer height;

    @ApiModelProperty(value = "Tipo corporal do cliente. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- ectomorph (Ectomorfo)\n" +
            "- mesomorph (Mesomorfo)\n" +
            "- endomorph (Endomorfo)\n", example = "mesomorph")
    private String body_type;

    @ApiModelProperty(value = "Objetivo do treino. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- weight_loss (Perda de peso)\n" +
            "- muscle_gain (Ganho de massa muscular)\n" +
            "- strength (Força)\n" +
            "- endurance (Resistência)\n" +
            "- general_fitness (Condicionamento geral)\n", example = "muscle_gain")
    private String goal;

    @ApiModelProperty(value = "Quantidade de dias por semana disponíveis para treino", example = "4")
    private Integer training_days;

    @ApiModelProperty(value = "Tempo disponível para cada sessão de treino em minutos", example = "60")
    private Integer training_time;

    @ApiModelProperty(value = "Nível de experiência em treinos. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- beginner (Iniciante)\n" +
            "- intermediate (Intermediário)\n" +
            "- advanced (Avançado)\n", example = "intermediate")
    private String experience_level;

    @ApiModelProperty(value = "Condição física atual. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- sedentary (Sedentário)\n" +
            "- lightly_active (Levemente ativo)\n" +
            "- moderately_active (Moderadamente ativo)\n" +
            "- very_active (Muito ativo)\n", example = "moderately_active")
    private String current_condition;

    @ApiModelProperty(value = "ID do cliente no sistema", example = "456")
    private Integer client_id;

    @ApiModelProperty(value = "Peso do cliente em quilogramas", example = "75")
    private Integer weight;

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getBody_type() {
        return body_type;
    }

    public void setBody_type(String body_type) {
        this.body_type = body_type;
    }

    public String getGoal() {
        return goal;
    }

    public void setGoal(String goal) {
        this.goal = goal;
    }

    public Integer getTraining_days() {
        return training_days;
    }

    public void setTraining_days(Integer training_days) {
        this.training_days = training_days;
    }

    public Integer getTraining_time() {
        return training_time;
    }

    public void setTraining_time(Integer training_time) {
        this.training_time = training_time;
    }

    public String getExperience_level() {
        return experience_level;
    }

    public void setExperience_level(String experience_level) {
        this.experience_level = experience_level;
    }

    public String getCurrent_condition() {
        return current_condition;
    }

    public void setCurrent_condition(String current_condition) {
        this.current_condition = current_condition;
    }

    public Integer getClient_id() {
        return client_id;
    }

    public void setClient_id(Integer client_id) {
        this.client_id = client_id;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }
}
