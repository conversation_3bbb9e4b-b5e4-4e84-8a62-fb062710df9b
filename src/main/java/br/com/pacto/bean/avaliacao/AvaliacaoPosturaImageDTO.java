package br.com.pacto.bean.avaliacao;

import br.com.pacto.objeto.Aplicacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Representa uma imagem usada na avaliação postural do aluno.")
public class AvaliacaoPosturaImageDTO {

    @ApiModelProperty(value = "Identificador da imagem.", example = "123")
    private Integer id;

    @ApiModelProperty(value = "URL da imagem salva no sistema.", example = "https://exemplo.com.br/fotos/avaliacao-postural/frente123.jpg")
    private String url;

    private final String URL_PATH = Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/";

    public AvaliacaoPosturaImageDTO(String url) {
        url = url != null && !url.equalsIgnoreCase("") ? URL_PATH + url : null;
        this.url = url;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
