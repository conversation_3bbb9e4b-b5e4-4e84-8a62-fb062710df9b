package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Medições de perímetros corporais do aluno para avaliação física.")
public class AvaliacaoPerimetriaDTO {

    @ApiModelProperty(value = "Circunferência do pescoço em cm.", example = "37.5")
    private Double pescoco;

    @ApiModelProperty(value = "Largura do ombro em cm.", example = "45.0")
    private Double ombro;

    @ApiModelProperty(value = "Circunferência do tórax/busto relaxado em cm.", example = "90.0")
    private Double toraxBustoRelaxado;

    @ApiModelProperty(value = "Circunferência do braço relaxado esquerdo em cm.", example = "30.0")
    private Double bracoRelaxadoEsq;

    @ApiModelProperty(value = "Circunferência do braço relaxado direito em cm.", example = "31.0")
    private Double bracoRelaxadoDir;

    @ApiModelProperty(value = "Circunferência do braço contraído esquerdo em cm.", example = "33.0")
    private Double bracoContraidoEsq;

    @ApiModelProperty(value = "Circunferência do braço contraído direito em cm.", example = "34.0")
    private Double bracoContraidoDir;

    @ApiModelProperty(value = "Circunferência do antebraço esquerdo em cm.", example = "26.0")
    private Double antebracoEsq;

    @ApiModelProperty(value = "Circunferência do antebraço direito em cm.", example = "26.5")
    private Double antebracoDir;

    @ApiModelProperty(value = "Circunferência da cintura em cm.", example = "85.0")
    private Double cintura;

    @ApiModelProperty(value = "Circunferência abdominal em cm.", example = "88.0")
    private Double circunferenciaAbdominal;

    @ApiModelProperty(value = "Circunferência do quadril em cm.", example = "95.0")
    private Double quadril;

    @ApiModelProperty(value = "Circunferência proximal da coxa esquerda em cm.", example = "55.0")
    private Double coxaProximalEsq;

    @ApiModelProperty(value = "Circunferência proximal da coxa direita em cm.", example = "56.0")
    private Double coxaProximalDir;

    @ApiModelProperty(value = "Circunferência média da coxa esquerda em cm.", example = "50.0")
    private Double coxaMediaEsq;

    @ApiModelProperty(value = "Circunferência média da coxa direita em cm.", example = "51.0")
    private Double coxaMediaDir;

    @ApiModelProperty(value = "Circunferência distal da coxa esquerda em cm.", example = "45.0")
    private Double coxaDistalEsq;

    @ApiModelProperty(value = "Circunferência distal da coxa direita em cm.", example = "46.0")
    private Double coxaDistalDir;

    @ApiModelProperty(value = "Circunferência da panturrilha esquerda em cm.", example = "38.0")
    private Double panturrilhaEsq;

    @ApiModelProperty(value = "Circunferência da panturrilha direita em cm.", example = "39.0")
    private Double panturrilhaDir;

    @ApiModelProperty(value = "Diâmetro do punho em cm.", example = "16.0")
    private Double diametroPunho;

    @ApiModelProperty(value = "Diâmetro do joelho em cm.", example = "38.0")
    private Double diametroJoelho;

    @ApiModelProperty(value = "Diâmetro do cotovelo em cm.", example = "28.0")
    private Double diametroCotovelo;

    @ApiModelProperty(value = "Diâmetro do tornozelo em cm.", example = "22.0")
    private Double diametroTornozelo;


    public AvaliacaoPerimetriaDTO() {
    }

    public AvaliacaoPerimetriaDTO(AvaliacaoFisica avaliacaoFisica, PesoOsseo pesoOsseo) {
        this.pescoco = avaliacaoFisica.getPescoco();
        this.ombro = avaliacaoFisica.getOmbro();
        this.toraxBustoRelaxado = avaliacaoFisica.getToraxBusto();
        this.bracoRelaxadoEsq = avaliacaoFisica.getBracoRelaxadoEsq();
        this.bracoRelaxadoDir = avaliacaoFisica.getBracoRelaxadoDir();
        this.bracoContraidoEsq = avaliacaoFisica.getBracoContraidoEsq();
        this.bracoContraidoDir = avaliacaoFisica.getBracoContraidoDir();
        this.antebracoEsq = avaliacaoFisica.getAntebracoEsq();
        this.antebracoDir = avaliacaoFisica.getAntebracoDir();
        this.cintura = avaliacaoFisica.getCintura();
        this.circunferenciaAbdominal = avaliacaoFisica.getCircunferenciaAbdominal();
        this.quadril = avaliacaoFisica.getQuadril();
        this.coxaProximalEsq = avaliacaoFisica.getCoxaProximalEsq();
        this.coxaProximalDir = avaliacaoFisica.getCoxaProximalDir();
        this.coxaMediaEsq = avaliacaoFisica.getCoxaMediaEsq();
        this.coxaMediaDir = avaliacaoFisica.getCoxaMediaDir();
        this.coxaDistalEsq = avaliacaoFisica.getCoxaDistalEsq();
        this.coxaDistalDir = avaliacaoFisica.getCoxaDistalDir();
        this.panturrilhaEsq = avaliacaoFisica.getPanturrilhaEsq();
        this.panturrilhaDir = avaliacaoFisica.getPanturrilhaDir();
        this.diametroPunho = pesoOsseo.getDiametroPunho();
        this.diametroJoelho = pesoOsseo.getDiametroFemur();
        this.diametroCotovelo = pesoOsseo.getDiametroCotovelo();
        this.diametroTornozelo = pesoOsseo.getDiametroTornozelo();
    }

    public Double getPescoco() {
        return pescoco;
    }

    public void setPescoco(Double pescoco) {
        this.pescoco = pescoco;
    }

    public Double getOmbro() {
        return ombro;
    }

    public void setOmbro(Double ombro) {
        this.ombro = ombro;
    }

    public Double getToraxBustoRelaxado() {
        return toraxBustoRelaxado;
    }

    public void setToraxBustoRelaxado(Double toraxBustoRelaxado) {
        this.toraxBustoRelaxado = toraxBustoRelaxado;
    }

    public Double getBracoRelaxadoEsq() {
        return bracoRelaxadoEsq;
    }

    public void setBracoRelaxadoEsq(Double bracoRelaxadoEsq) {
        this.bracoRelaxadoEsq = bracoRelaxadoEsq;
    }

    public Double getBracoRelaxadoDir() {
        return bracoRelaxadoDir;
    }

    public void setBracoRelaxadoDir(Double bracoRelaxadoDir) {
        this.bracoRelaxadoDir = bracoRelaxadoDir;
    }

    public Double getBracoContraidoEsq() {
        return bracoContraidoEsq;
    }

    public void setBracoContraidoEsq(Double bracoContraidoEsq) {
        this.bracoContraidoEsq = bracoContraidoEsq;
    }

    public Double getBracoContraidoDir() {
        return bracoContraidoDir;
    }

    public void setBracoContraidoDir(Double bracoContraidoDir) {
        this.bracoContraidoDir = bracoContraidoDir;
    }

    public Double getAntebracoEsq() {
        return antebracoEsq;
    }

    public void setAntebracoEsq(Double antebracoEsq) {
        this.antebracoEsq = antebracoEsq;
    }

    public Double getAntebracoDir() {
        return antebracoDir;
    }

    public void setAntebracoDir(Double antebracoDir) {
        this.antebracoDir = antebracoDir;
    }

    public Double getCintura() {
        return cintura;
    }

    public void setCintura(Double cintura) {
        this.cintura = cintura;
    }

    public Double getCircunferenciaAbdominal() {
        return circunferenciaAbdominal;
    }

    public void setCircunferenciaAbdominal(Double circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }

    public Double getQuadril() {
        return quadril;
    }

    public void setQuadril(Double quadril) {
        this.quadril = quadril;
    }

    public Double getCoxaProximalEsq() {
        return coxaProximalEsq;
    }

    public void setCoxaProximalEsq(Double coxaProximalEsq) {
        this.coxaProximalEsq = coxaProximalEsq;
    }

    public Double getCoxaProximalDir() {
        return coxaProximalDir;
    }

    public void setCoxaProximalDir(Double coxaProximalDir) {
        this.coxaProximalDir = coxaProximalDir;
    }

    public Double getCoxaMediaEsq() {
        return coxaMediaEsq;
    }

    public void setCoxaMediaEsq(Double coxaMediaEsq) {
        this.coxaMediaEsq = coxaMediaEsq;
    }

    public Double getCoxaMediaDir() {
        return coxaMediaDir;
    }

    public void setCoxaMediaDir(Double coxaMediaDir) {
        this.coxaMediaDir = coxaMediaDir;
    }

    public Double getCoxaDistalEsq() {
        return coxaDistalEsq;
    }

    public void setCoxaDistalEsq(Double coxaDistalEsq) {
        this.coxaDistalEsq = coxaDistalEsq;
    }

    public Double getCoxaDistalDir() {
        return coxaDistalDir;
    }

    public void setCoxaDistalDir(Double coxaDistalDir) {
        this.coxaDistalDir = coxaDistalDir;
    }

    public Double getPanturrilhaEsq() {
        return panturrilhaEsq;
    }

    public void setPanturrilhaEsq(Double panturrilhaEsq) {
        this.panturrilhaEsq = panturrilhaEsq;
    }

    public Double getPanturrilhaDir() {
        return panturrilhaDir;
    }

    public void setPanturrilhaDir(Double panturrilhaDir) {
        this.panturrilhaDir = panturrilhaDir;
    }

    public Double getDiametroPunho() {
        return diametroPunho;
    }

    public void setDiametroPunho(Double diametroPunho) {
        this.diametroPunho = diametroPunho;
    }

    public Double getDiametroJoelho() {
        return diametroJoelho;
    }

    public void setDiametroJoelho(Double diametroJoelho) {
        this.diametroJoelho = diametroJoelho;
    }

    public Double getDiametroCotovelo() {
        return diametroCotovelo;
    }

    public void setDiametroCotovelo(Double diametroCotovelo) {
        this.diametroCotovelo = diametroCotovelo;
    }

    public Double getDiametroTornozelo() {
        return diametroTornozelo;
    }

    public void setDiametroTornozelo(Double diametroTornozelo) {
        this.diametroTornozelo = diametroTornozelo;
    }
}
