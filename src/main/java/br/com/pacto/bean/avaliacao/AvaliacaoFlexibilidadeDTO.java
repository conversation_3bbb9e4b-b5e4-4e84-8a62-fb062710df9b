package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoFlexibilidadeDTO {
    @ApiModelProperty(value = "Alcance máximo de flexibilidade em centímetros.", example = "35")
    private Integer alcanceMaximo;

    @ApiModelProperty(value = "Mobilidade do tornozelo esquerdo classificada.\n\n" +
            "Valores possíveis:\n" +
            "- 0 FRACA\n" +
            "- 1 REGULAR\n" +
            "- 2 MEDIA\n" +
            "- 3 BOA\n" +
            "- 4 EXCELENTE\n" +
            "- 5 NORMAL\n" +
            "- 6 POSITIVA\n" +
            "- 7 VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- 8 VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- 9 VALGO_DINAMICO_BILATERAL\n" +
            "- 10 ABAIXO_DA_MEDIA\n" +
            "- 11 HIPERMOBILIDADE", example = "4")
    private Integer mobilidadeTornozeloEsquerdo;

    @ApiModelProperty(value = "Mobilidade do tornozelo esquerdo classificada.\n\n" +
            "Valores possíveis:\n" +
            "- 0 FRACA\n" +
            "- 1 REGULAR\n" +
            "- 2 MEDIA\n" +
            "- 3 BOA\n" +
            "- 4 EXCELENTE\n" +
            "- 5 NORMAL\n" +
            "- 6 POSITIVA\n" +
            "- 7 VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- 8 VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- 9 VALGO_DINAMICO_BILATERAL\n" +
            "- 10 ABAIXO_DA_MEDIA\n" +
            "- 11 HIPERMOBILIDADE", example = "4")
    private Integer mobilidadeTornozeloDireito;

    @ApiModelProperty(value = "Observação geral da avaliação de flexibilidade.", example = "Flexibilidade dentro do esperado.")
    private String observacao;

    @ApiModelProperty(value = "Indicador de classificação da coluna.\n\n" +
            "Valores possíveis:\n" +
            "- FRACA\n" +
            "- REGULAR\n" +
            "- MEDIA\n" +
            "- BOA\n" +
            "- EXCELENTE\n" +
            "- NORMAL\n" +
            "- POSITIVA\n" +
            "- VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- VALGO_DINAMICO_BILATERAL\n" +
            "- ABAIXO_DA_MEDIA\n" +
            "- HIPERMOBILIDADE", example = "NORMAL")
    private ClassificacaoEnum indicadorColuna;

    @ApiModelProperty(value = "Indicador de classificação da coluna.\n\n" +
            "Valores possíveis:\n" +
            "- FRACA\n" +
            "- REGULAR\n" +
            "- MEDIA\n" +
            "- BOA\n" +
            "- EXCELENTE\n" +
            "- NORMAL\n" +
            "- POSITIVA\n" +
            "- VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- VALGO_DINAMICO_BILATERAL\n" +
            "- ABAIXO_DA_MEDIA\n" +
            "- HIPERMOBILIDADE", example = "NORMAL")
    private ClassificacaoEnum mobilidadeOmbroEsquerdo;

    @ApiModelProperty(value = "Indicador de classificação da coluna.\n\n" +
            "Valores possíveis:\n" +
            "- FRACA\n" +
            "- REGULAR\n" +
            "- MEDIA\n" +
            "- BOA\n" +
            "- EXCELENTE\n" +
            "- NORMAL\n" +
            "- POSITIVA\n" +
            "- VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- VALGO_DINAMICO_BILATERAL\n" +
            "- ABAIXO_DA_MEDIA\n" +
            "- HIPERMOBILIDADE", example = "NORMAL")
    private ClassificacaoEnum mobilidadeOmbroDireito;

    @ApiModelProperty(value = "Indicador de classificação da coluna.\n\n" +
            "Valores possíveis:\n" +
            "- FRACA\n" +
            "- REGULAR\n" +
            "- MEDIA\n" +
            "- BOA\n" +
            "- EXCELENTE\n" +
            "- NORMAL\n" +
            "- POSITIVA\n" +
            "- VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- VALGO_DINAMICO_BILATERAL\n" +
            "- ABAIXO_DA_MEDIA\n" +
            "- HIPERMOBILIDADE", example = "NORMAL")
    private ClassificacaoEnum mobilidadeJoelhoEsquerdo;

    @ApiModelProperty(value = "Indicador de classificação da coluna.\n\n" +
            "Valores possíveis:\n" +
            "- FRACA\n" +
            "- REGULAR\n" +
            "- MEDIA\n" +
            "- BOA\n" +
            "- EXCELENTE\n" +
            "- NORMAL\n" +
            "- POSITIVA\n" +
            "- VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- VALGO_DINAMICO_BILATERAL\n" +
            "- ABAIXO_DA_MEDIA\n" +
            "- HIPERMOBILIDADE", example = "NORMAL")
    private ClassificacaoEnum mobilidadeJoelhoDireito;

    @ApiModelProperty(value = "Indicador de classificação da coluna.\n\n" +
            "Valores possíveis:\n" +
            "- FRACA\n" +
            "- REGULAR\n" +
            "- MEDIA\n" +
            "- BOA\n" +
            "- EXCELENTE\n" +
            "- NORMAL\n" +
            "- POSITIVA\n" +
            "- VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- VALGO_DINAMICO_BILATERAL\n" +
            "- ABAIXO_DA_MEDIA\n" +
            "- HIPERMOBILIDADE", example = "NORMAL")
    private ClassificacaoEnum mobilidadeQuadrilEsquerdo;

    @ApiModelProperty(value = "Indicador de classificação da coluna.\n\n" +
            "Valores possíveis:\n" +
            "- FRACA\n" +
            "- REGULAR\n" +
            "- MEDIA\n" +
            "- BOA\n" +
            "- EXCELENTE\n" +
            "- NORMAL\n" +
            "- POSITIVA\n" +
            "- VALGO_DINAMICO_MEMBRO_DOMINANTE\n" +
            "- VALGO_DINAMICO_MEMBRO_NAO_DOMINANTE\n" +
            "- VALGO_DINAMICO_BILATERAL\n" +
            "- ABAIXO_DA_MEDIA\n" +
            "- HIPERMOBILIDADE", example = "NORMAL")
    private ClassificacaoEnum mobilidadeQuadrilDireito;

    @ApiModelProperty(value = "Observação sobre a mobilidade do ombro.", example = "Mobilidade reduzida devido a lesão.")
    private String observacaoOmbro;

    @ApiModelProperty(value = "Observação sobre a mobilidade do quadril.", example = "Bom nível de mobilidade.")
    private String observacaoQuadril;

    @ApiModelProperty(value = "Observação sobre a mobilidade do tornozelo.", example = "Mobilidade limitada no tornozelo direito.")
    private String observacaoTornozelo;

    @ApiModelProperty(value = "Observação sobre a mobilidade do joelho.", example = "Joelho esquerdo com restrição.")
    private String observacaoJoelho;

    public AvaliacaoFlexibilidadeDTO(Flexibilidade flexibilidade) {
        this.alcanceMaximo = flexibilidade.getAlcance();
        this.observacao = flexibilidade.getObservacao();
        this.indicadorColuna = flexibilidade.getClassificacao();
        this.mobilidadeOmbroEsquerdo = flexibilidade.getMobilidadeOmbroEsquerdo();
        this.mobilidadeOmbroDireito = flexibilidade.getMobilidadeOmbroDireito();
        this.mobilidadeQuadrilEsquerdo = flexibilidade.getMobilidadeQuadrilEsquerdo();
        this.mobilidadeQuadrilDireito = flexibilidade.getMobilidadeQuadrilDireito();
        this.mobilidadeJoelhoEsquerdo = flexibilidade.getMobilidadeJoelhoEsquerdo();
        this.mobilidadeJoelhoDireito = flexibilidade.getMobilidadeJoelhoDireito();
        this.mobilidadeTornozeloEsquerdo = flexibilidade.getMobilidadeTornozeloEsquerdo();
        this.mobilidadeTornozeloDireito = flexibilidade.getMobilidadeTornozeloDireito();
        this.observacaoJoelho = flexibilidade.getObservacaoJoelho();
        this.observacaoTornozelo = flexibilidade.getObservacaoTornozelo();
        this.observacaoOmbro = flexibilidade.getObservacaoOmbro();
        this.observacaoQuadril = flexibilidade.getObservacaoQuadril();
    }

    public Integer getAlcanceMaximo() {
        return alcanceMaximo;
    }

    public void setAlcanceMaximo(Integer alcanceMaximo) {
        this.alcanceMaximo = alcanceMaximo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public ClassificacaoEnum getIndicadorColuna() {
        return indicadorColuna;
    }

    public void setIndicadorColuna(ClassificacaoEnum indicadorColuna) {
        this.indicadorColuna = indicadorColuna;
    }

    public ClassificacaoEnum getMobilidadeOmbroEsquerdo() {
        return mobilidadeOmbroEsquerdo;
    }

    public void setMobilidadeOmbroEsquerdo(ClassificacaoEnum mobilidadeOmbroEsquerdo) {
        this.mobilidadeOmbroEsquerdo = mobilidadeOmbroEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeOmbroDireito() {
        return mobilidadeOmbroDireito;
    }

    public void setMobilidadeOmbroDireito(ClassificacaoEnum mobilidadeOmbroDireito) {
        this.mobilidadeOmbroDireito = mobilidadeOmbroDireito;
    }

    public ClassificacaoEnum getMobilidadeJoelhoEsquerdo() {
        return mobilidadeJoelhoEsquerdo;
    }

    public void setMobilidadeJoelhoEsquerdo(ClassificacaoEnum mobilidadeJoelhoEsquerdo) {
        this.mobilidadeJoelhoEsquerdo = mobilidadeJoelhoEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeJoelhoDireito() {
        return mobilidadeJoelhoDireito;
    }

    public void setMobilidadeJoelhoDireito(ClassificacaoEnum mobilidadeJoelhoDireito) {
        this.mobilidadeJoelhoDireito = mobilidadeJoelhoDireito;
    }

    public Integer getMobilidadeTornozeloEsquerdo() {
        return mobilidadeTornozeloEsquerdo;
    }

    public void setMobilidadeTornozeloEsquerdo(Integer mobilidadeTornozeloEsquerdo) {
        this.mobilidadeTornozeloEsquerdo = mobilidadeTornozeloEsquerdo;
    }

    public Integer getMobilidadeTornozeloDireito() {
        return mobilidadeTornozeloDireito;
    }

    public void setMobilidadeTornozeloDireito(Integer mobilidadeTornozeloDireito) {
        this.mobilidadeTornozeloDireito = mobilidadeTornozeloDireito;
    }


    public ClassificacaoEnum getMobilidadeQuadrilEsquerdo() {
        return mobilidadeQuadrilEsquerdo;
    }

    public void setMobilidadeQuadrilEsquerdo(ClassificacaoEnum mobilidadeQuadrilEsquerdo) {
        this.mobilidadeQuadrilEsquerdo = mobilidadeQuadrilEsquerdo;
    }

    public ClassificacaoEnum getMobilidadeQuadrilDireito() {
        return mobilidadeQuadrilDireito;
    }

    public void setMobilidadeQuadrilDireito(ClassificacaoEnum mobilidadeQuadrilDireito) {
        this.mobilidadeQuadrilDireito = mobilidadeQuadrilDireito;
    }

    public String getObservacaoQuadril() {
        return observacaoQuadril;
    }

    public void setObservacaoQuadril(String observacaoQuadril) {
        this.observacaoQuadril = observacaoQuadril;
    }

    public String getObservacaoTornozelo() {
        return observacaoTornozelo;
    }

    public void setObservacaoTornozelo(String observacaoTornozelo) {
        this.observacaoTornozelo = observacaoTornozelo;
    }

    public String getObservacaoJoelho() {
        return observacaoJoelho;
    }

    public void setObservacaoJoelho(String observacaoJoelho) {
        this.observacaoJoelho = observacaoJoelho;
    }

    public String getObservacaoOmbro() {
        return observacaoOmbro;
    }

    public void setObservacaoOmbro(String observacaoOmbro) {
        this.observacaoOmbro = observacaoOmbro;
    }

}
