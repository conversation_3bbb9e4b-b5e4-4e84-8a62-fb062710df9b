package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.Movimento3D;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Dados para criação ou atualização de uma avaliação integrada, incluindo anamnese, testes de mobilidade e estabilidade.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoIntegradaDTOUptade {

    @ApiModelProperty(value = "Identificador único da avaliação integrada. Deve ser informado apenas para atualizações.", example = "456")
    private Integer id;

    @ApiModelProperty(value = "Data da avaliação integrada em formato timestamp.", example = "1717958400000", required = true)
    private Long dataAvaliacao;

    @ApiModelProperty(value = "Identificador da anamnese selecionada para esta avaliação integrada.", example = "12", required = true)
    private Integer anamneseSelecionadaId;

    @ApiModelProperty(value = "Lista de respostas às perguntas da anamnese selecionada.")
    private List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas;

    @ApiModelProperty(value = "Lista de movimentos de mobilidade avaliados (cadeia anterior, posterior, lateral, rotacional).")
    private List<Movimento3D> mobilidade;

    @ApiModelProperty(value = "Lista de movimentos de estabilidade avaliados (controle, fechamento, abertura).")
    private List<Movimento3D> estabilidade;
    @ApiModelProperty(value = "Soma dos pontos de mobilidade do lado direito do corpo.", example = "28")
    private Integer somaMobilidadeDir;

    @ApiModelProperty(value = "Soma dos pontos de estabilidade do lado direito do corpo.", example = "24")
    private Integer somaEstabilidadeDir;

    @ApiModelProperty(value = "Média dos pontos de mobilidade do lado direito do corpo.", example = "7.0")
    private Double mediaMobilidadeDir;

    @ApiModelProperty(value = "Média dos pontos de estabilidade do lado direito do corpo.", example = "6.0")
    private Double mediaEstabilidadeDir;

    @ApiModelProperty(value = "Soma dos pontos de mobilidade do lado esquerdo do corpo.", example = "26")
    private Integer somaMobilidadeEsq;

    @ApiModelProperty(value = "Soma dos pontos de estabilidade do lado esquerdo do corpo.", example = "22")
    private Integer somaEstabilidadeEsq;

    @ApiModelProperty(value = "Média dos pontos de mobilidade do lado esquerdo do corpo.", example = "6.5")
    private Double mediaMobilidadeEsq;

    @ApiModelProperty(value = "Média dos pontos de estabilidade do lado esquerdo do corpo.", example = "5.5")
    private Double mediaEstabilidadeEsq;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Long dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Integer getAnamneseSelecionadaId() {
        return anamneseSelecionadaId;
    }

    public void setAnamneseSelecionadaId(Integer anamneseSelecionadaId) {
        this.anamneseSelecionadaId = anamneseSelecionadaId;
    }

    public List<AnamnesePerguntaRespostaDTOUpdate> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<Movimento3D> getMobilidade() {
        return mobilidade;
    }

    public void setMobilidade(List<Movimento3D> mobilidade) {
        this.mobilidade = mobilidade;
    }

    public List<Movimento3D> getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(List<Movimento3D> estabilidade) {
        this.estabilidade = estabilidade;
    }

    public Integer getSomaMobilidadeDir() {
        return somaMobilidadeDir;
    }

    public void setSomaMobilidadeDir(Integer somaMobilidadeDir) {
        this.somaMobilidadeDir = somaMobilidadeDir;
    }

    public Integer getSomaEstabilidadeDir() {
        return somaEstabilidadeDir;
    }

    public void setSomaEstabilidadeDir(Integer somaEstabilidadeDir) {
        this.somaEstabilidadeDir = somaEstabilidadeDir;
    }

    public Double getMediaMobilidadeDir() {
        return mediaMobilidadeDir;
    }

    public void setMediaMobilidadeDir(Double mediaMobilidadeDir) {
        this.mediaMobilidadeDir = mediaMobilidadeDir;
    }

    public Double getMediaEstabilidadeDir() {
        return mediaEstabilidadeDir;
    }

    public void setMediaEstabilidadeDir(Double mediaEstabilidadeDir) {
        this.mediaEstabilidadeDir = mediaEstabilidadeDir;
    }

    public Integer getSomaMobilidadeEsq() {
        return somaMobilidadeEsq;
    }

    public void setSomaMobilidadeEsq(Integer somaMobilidadeEsq) {
        this.somaMobilidadeEsq = somaMobilidadeEsq;
    }

    public Integer getSomaEstabilidadeEsq() {
        return somaEstabilidadeEsq;
    }

    public void setSomaEstabilidadeEsq(Integer somaEstabilidadeEsq) {
        this.somaEstabilidadeEsq = somaEstabilidadeEsq;
    }

    public Double getMediaMobilidadeEsq() {
        return mediaMobilidadeEsq;
    }

    public void setMediaMobilidadeEsq(Double mediaMobilidadeEsq) {
        this.mediaMobilidadeEsq = mediaMobilidadeEsq;
    }

    public Double getMediaEstabilidadeEsq() {
        return mediaEstabilidadeEsq;
    }

    public void setMediaEstabilidadeEsq(Double mediaEstabilidadeEsq) {
        this.mediaEstabilidadeEsq = mediaEstabilidadeEsq;
    }

}
