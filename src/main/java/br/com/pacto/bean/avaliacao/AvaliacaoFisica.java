package br.com.pacto.bean.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;
import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rafael on 08/10/2016.
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa uma avaliação física completa de um cliente, incluindo dados antropométricos, composição corporal, perimetria, dobras cutâneas, protocolos de avaliação e resultados calculados como IMC, percentual de gordura e classificações.")
public class AvaliacaoFisica implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único da avaliação física no banco de dados.", example = "98765")
    private Integer codigo;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Protocolo utilizado para cálculo do VO2 máximo. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- VO_CAMINHADA_CORRIDA_12_MINUTOS (Teste de caminhada/corrida de 12 minutos)\n" +
            "- VO_CAMINHADA_2400_M (Teste de caminhada de 2400 metros)\n" +
            "- VO_AEROBICO_DE_BANCO (Teste aeróbico de banco)\n" +
            "- BIKE (Teste em bicicleta ergométrica)\n", example = "VO_CAMINHADA_CORRIDA_12_MINUTOS")
    private ProtocolosVo2MaxEnum protocoloVo = ProtocolosVo2MaxEnum.VO_CAMINHADA_CORRIDA_12_MINUTOS;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Protocolo utilizado para avaliação da composição corporal. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- POLLOCK_3_DOBRAS (Protocolo de Pollock com 3 dobras cutâneas)\n" +
            "- POLLOCK_7_DOBRAS (Protocolo de Pollock com 7 dobras cutâneas)\n" +
            "- GUEDES (Protocolo de Guedes)\n" +
            "- FAULKNER_DOBRAS (Protocolo de Faulkner com 4 dobras)\n" +
            "- BIOIMPEDANCIA (Avaliação por bioimpedância)\n" +
            "- WELTMAN_OBESO (Protocolo de Weltman para obesos)\n" +
            "- POLLOCK_ADOLESCENTE (Protocolo de Pollock para adolescentes)\n" +
            "- SLAUGHTER (Protocolo de Slaughter)\n" +
            "- YUHASZ (Protocolo de Yuhasz com 6 dobras)\n" +
            "- TG_LOHMAN (Protocolo TG Lohman com 2 dobras)\n", example = "POLLOCK_3_DOBRAS")
    private ProtocolosAvaliacaoFisicaEnum protocolo = ProtocolosAvaliacaoFisicaEnum.POLLOCK_3_DOBRAS;

    @ManyToOne
    @JsonIgnore
    @ApiModelProperty(value = "Referência ao cliente/aluno avaliado.")
    private ClienteSintetico cliente;

    @ApiModelProperty(value = "Peso corporal do cliente em quilogramas.", example = "75.5")
    private Double peso = 0.0;

    @ApiModelProperty(value = "Altura do cliente em centímetros.", example = "175.0")
    private Double altura = 0.0;
    @Lob
    @Type(type = "org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    @ApiModelProperty(value = "URL da assinatura digital do cliente na avaliação física.", example = "https://storage.example.com/assinaturas/cliente_123_avaliacao_456.png")
    private String urlAssinatura;
    @Lob
    @Type(type = "org.hibernate.type.StringClobType")
    @Column(columnDefinition = "text", length = 99999)
    @ApiModelProperty(value = "Log de dados da balança de bioimpedância utilizada na avaliação.", example = "Balança InBody 270 - Dados coletados em 18/06/2024 14:30")
    private String logBalanca;
    @Transient
    @ApiModelProperty(value = "Dados da assinatura digital em formato base64 para processamento temporário.", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    private String assinatura;
    @OneToOne(mappedBy = "avaliacao", cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
    @ApiModelProperty(value = "Referência à entidade de flexibilidade associada a esta avaliação física.")
    private Flexibilidade flexibilidadeEntity;
    @Transient
    @ApiModelProperty(value = "Referência à avaliação postural associada a esta avaliação física.")
    private AvaliacaoPostural avaliacaoPosturual;
    //Dobras cutâneas

    @ApiModelProperty(value = "Medida da dobra cutânea abdominal em milímetros.", example = "18.5")
    private Double abdominal = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea peitoral em milímetros.", example = "12.3")
    private Double peitoral = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea da coxa medial em milímetros.", example = "22.7")
    private Double coxaMedial = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea supra-ilíaca em milímetros.", example = "15.8")
    private Double supraIliaca = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea do tríceps em milímetros.", example = "14.2")
    private Double triceps = 0.0;
    @ApiModelProperty(value = "Soma total de todas as dobras cutâneas medidas em milímetros.", example = "95.6")
    private Double totalDobras = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea subescapular em milímetros.", example = "16.4")
    private Double subescapular = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea do bíceps em milímetros.", example = "8.9")
    private Double biceps = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea axilar média em milímetros.", example = "11.7")
    private Double axilarMedia = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea supra-espinhal em milímetros.", example = "13.5")
    private Double supraEspinhal = 0.0;
    @ApiModelProperty(value = "Medida da dobra cutânea da panturrilha em milímetros.", example = "10.2")
    private Double panturrilha = 0.0;
    @ApiModelProperty(value = "Frequência cardíaca máxima teórica calculada em batimentos por minuto.", example = "185.0")
    private Double fcMaxima = 0.0;
    @ApiModelProperty(value = "Distância percorrida no teste de 12 minutos em metros.", example = "2800.0")
    private Double distancia12 = 0.0;
    @ApiModelProperty(value = "Tempo registrado no teste de 2400 metros no formato mm:ss.", example = "12:45")
    private String tempo2400 = "";
    @ApiModelProperty(value = "VO2 máximo calculado pelo teste de 12 minutos em ml/kg/min.", example = "45.2")
    private Double vo2Max12 = 0.0;
    @ApiModelProperty(value = "VO2 máximo calculado pelo teste de 2400 metros em ml/kg/min.", example = "42.8")
    private Double vo2Max2400 = 0.0;
    @ApiModelProperty(value = "VO2 máximo aeróbico calculado em ml/kg/min.", example = "48.5")
    private Double voMaxAerobico = 0.0;
    @ApiModelProperty(value = "Frequência cardíaca no 4º minuto do teste de Astrand em bpm.", example = "165.0")
    private Double fcAstrand4 = 0.0;
    @ApiModelProperty(value = "Frequência cardíaca no 5º minuto do teste de Astrand em bpm.", example = "168.0")
    private Double fcAstrand5 = 0.0;
    @ApiModelProperty(value = "Frequência cardíaca no teste de Queens em bpm.", example = "155.0")
    private Double fcQueens = 0.0;
    @ApiModelProperty(value = "Carga utilizada no teste de Astrand em watts.", example = "150.0")
    private Double cargaAstrand = 0.0;
    @ApiModelProperty(value = "VO2 calculado no teste de Astrand em ml/kg/min.", example = "35.7")
    private Double vo2Astrand = 0.0;
    @ApiModelProperty(value = "VO2 máximo calculado pelo protocolo de Astrand em ml/kg/min.", example = "44.3")
    private Double vo2MaxAstrand = 0.0;
    @ApiModelProperty(value = "VO2 máximo calculado pelo protocolo de Queens em ml/kg/min.", example = "41.9")
    private Double vo2MaxQueens = 0.0;
    //bioimpedancia
    @ApiModelProperty(value = "Percentual de água corporal total medido por bioimpedância.", example = "58.7")
    private Double percentualAgua = 0.0;
    @ApiModelProperty(value = "Taxa Metabólica Basal (TMB) calculada em kcal/dia.", example = "1685.0")
    private Double tmb = 0.0;
    @ApiModelProperty(value = "Resistência corporal medida por bioimpedância em ohms.", example = "485.2")
    private Double resistencia = 0.0;
    @ApiModelProperty(value = "Reatância corporal medida por bioimpedância em ohms.", example = "52.8")
    private Double reatancia = 0.0;
    @ApiModelProperty(value = "Percentual de gordura corporal ideal calculado.", example = "12.5")
    private Double gorduraIdeal = 0.0;
    @ApiModelProperty(value = "Necessidade calórica para atividade física em kcal/dia.", example = "2250.0")
    private Double necessidadeFisica = 0.0;
    @ApiModelProperty(value = "Necessidade calórica total diária em kcal/dia.", example = "2850.0")
    private Double necessidadeCalorica = 0.0;
    @ApiModelProperty(value = "Idade metabólica calculada em anos.", example = "28.0")
    private Double idadeMetabolica = 0.0;
    @ApiModelProperty(value = "Nível de gordura visceral medido por bioimpedância.", example = "6.0")
    private Double gorduraVisceral = 0.0;

    //somatotipia
    @ApiModelProperty(value = "Componente ectomórfico da somatotipia (linearidade/magreza).", example = "3.2")
    private Double ectomorfia = 0.0;
    @ApiModelProperty(value = "Componente mesomórfico da somatotipia (desenvolvimento muscular).", example = "5.8")
    private Double mesomorfia = 0.0;
    @ApiModelProperty(value = "Componente endomórfico da somatotipia (adiposidade relativa).", example = "2.1")
    private Double endomorfia = 0.0;


    //Perimetria
    @ApiModelProperty(value = "Perímetro do antebraço esquerdo em centímetros.", example = "26.5")
    private Double antebracoEsq = 0.0;
    @ApiModelProperty(value = "Perímetro do braço esquerdo relaxado em centímetros.", example = "32.8")
    private Double bracoRelaxadoEsq = 0.0;
    @ApiModelProperty(value = "Perímetro do braço esquerdo contraído em centímetros.", example = "35.2")
    private Double bracoContraidoEsq = 0.0;
    @ApiModelProperty(value = "Perímetro da coxa média esquerda em centímetros.", example = "58.4")
    private Double coxaMediaEsq = 0.0;
    @ApiModelProperty(value = "Perímetro da panturrilha esquerda em centímetros.", example = "37.6")
    private Double panturrilhaEsq = 0.0;
    @ApiModelProperty(value = "Perímetro do punho em centímetros.", example = "16.8")
    private Double punho = 0.0;
    @ApiModelProperty(value = "Perímetro do quadril em centímetros.", example = "98.5")
    private Double quadril = 0.0;
    @ApiModelProperty(value = "Perímetro dos ombros em centímetros.", example = "112.3")
    private Double ombro = 0.0;
    @ApiModelProperty(value = "Perímetro do pescoço em centímetros.", example = "38.2")
    private Double pescoco = 0.0;

    @ApiModelProperty(value = "Código do movimento de produto relacionado à avaliação.", example = "1001")
    private Integer movProduto;
    @ApiModelProperty(value = "Código da venda relacionada à avaliação.", example = "2001")
    private Integer venda;

    @ApiModelProperty(value = "Perímetro da coxa distal direita em centímetros.", example = "52.3")
    private Double coxaDistalDir = 0.0;
    @ApiModelProperty(value = "Perímetro da coxa distal esquerda em centímetros.", example = "52.1")
    private Double coxaDistalEsq = 0.0;

    @ApiModelProperty(value = "Perímetro da coxa proximal direita em centímetros.", example = "60.8")
    private Double coxaProximalDir = 0.0;
    @ApiModelProperty(value = "Perímetro da coxa proximal esquerda em centímetros.", example = "60.5")
    private Double coxaProximalEsq = 0.0;

    @ApiModelProperty(value = "Perímetro do antebraço direito em centímetros.", example = "26.8")
    private Double antebracoDir = 0.0;
    @ApiModelProperty(value = "Perímetro do braço direito relaxado em centímetros.", example = "33.1")
    private Double bracoRelaxadoDir = 0.0;
    @ApiModelProperty(value = "Perímetro do braço direito contraído em centímetros.", example = "35.5")
    private Double bracoContraidoDir = 0.0;
    @ApiModelProperty(value = "Perímetro da coxa média direita em centímetros.", example = "58.7")
    private Double coxaMediaDir = 0.0;
    @ApiModelProperty(value = "Perímetro da panturrilha direita em centímetros.", example = "37.9")
    private Double panturrilhaDir = 0.0;
    @ApiModelProperty(value = "Perímetro do tórax/busto em centímetros.", example = "95.4")
    private Double toraxBusto = 0.0;
    @ApiModelProperty(value = "Perímetro da cintura em centímetros.", example = "82.6")
    private Double cintura = 0.0;
    @ApiModelProperty(value = "Perímetro do glúteo em centímetros.", example = "102.3")
    private Double gluteo = 0.0;
    @ApiModelProperty(value = "Medida de flexibilidade em centímetros (teste sentar e alcançar).", example = "28.5")
    private Double flexibilidade = 0.0;
    @ApiModelProperty(value = "Circunferência abdominal em centímetros.", example = "85.2")
    private Double circunferenciaAbdominal = 0.0;
    @ApiModelProperty(value = "Soma total de todas as medidas de perimetria em centímetros.", example = "1245.8")
    private Double totalPerimetria = 0.0;
    //Resultado
    @Column(columnDefinition = "text", length = 9999)
    @ApiModelProperty(value = "Recomendações e observações do profissional sobre a avaliação física.", example = "Manter atividade física regular, focar em exercícios aeróbicos para redução do percentual de gordura. Incluir treino de força 3x por semana.")
    private String recomendacoes;
    @ApiModelProperty(value = "Percentual de gordura corporal calculado.", example = "15.8")
    private Double percentualGordura = 0.0;

    @ApiModelProperty(value = "Meta de percentual de gordura estabelecida para o cliente.", example = "12.0")
    private Double metaPercentualGordura = 0.0;

    @ApiModelProperty(value = "Meta de percentual de gordura da avaliação anterior.", example = "14.0")
    private Double metaPercentualGorduraAnterior = 0.0;

    @ApiModelProperty(value = "Percentual de massa magra corporal.", example = "84.2")
    private Double percentualMassaMagra = 0.0;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Categoria de classificação do percentual de gordura. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NENHUM (Nenhuma classificação)\n" +
            "- EXCELENTE (Excelente)\n" +
            "- BOM (Bom)\n" +
            "- ACIMA_MEDIA (Acima da média)\n" +
            "- MEDIA (Média)\n" +
            "- ABAIXO_MEDIA (Abaixo da média)\n" +
            "- RUIM (Ruim)\n" +
            "- MUITO_RUIM (Muito ruim)\n" +
            "- OBESIDADE (Obesidade)\n" +
            "- ALTO (Alto)\n", example = "BOM")
    private CategoriaPercentualGordura categoriaPercentualGordura = CategoriaPercentualGordura.NENHUM;

    @ApiModelProperty(value = "Massa magra em quilogramas.", example = "63.6")
    private Double massaMagra = 0.0;

    @ApiModelProperty(value = "Massa gorda em quilogramas.", example = "11.9")
    private Double massaGorda = 0.0;

    @ApiModelProperty(value = "Peso ósseo estimado em quilogramas.", example = "12.5")
    private Double pesoOsseo = 0.0;

    @ApiModelProperty(value = "Peso muscular estimado em quilogramas.", example = "51.1")
    private Double pesoMuscular = 0.0;

    @ApiModelProperty(value = "Peso residual estimado em quilogramas.", example = "18.9")
    private Double residual = 0.0;

    @ApiModelProperty(value = "Índice de Massa Corporal (IMC) calculado.", example = "24.7")
    private Double imc = 0.0;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Categoria de classificação do IMC (Índice de Massa Corporal). \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NENHUM (Nenhuma classificação)\n" +
            "- BAIXO (Baixo peso)\n" +
            "- NORMAL (Peso normal)\n" +
            "- SOBREPESO (Sobrepeso)\n" +
            "- OBESIDADE_CLASSE_I (Obesidade classe I)\n" +
            "- OBESIDADE_CLASSE_II (Obesidade classe II)\n" +
            "- OBESIDADE_CLASSE_III (Obesidade classe III)\n", example = "NORMAL")
    private CategoriaAvaliacaoIMC categoriaAvaliacaoIMC = CategoriaAvaliacaoIMC.NENHUM;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data e hora em que a avaliação foi realizada.", example = "2024-06-18T14:30:00Z")
    private Date dataAvaliacao;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data e hora prevista para a próxima avaliação.", example = "2024-09-18T14:30:00Z")
    private Date dataProxima;
    @JsonIgnore
    @ApiModelProperty(value = "Código do colaborador responsável pelo lançamento da avaliação.", example = "123")
    private Integer responsavelLancamento_codigo;
    @OneToOne(cascade = CascadeType.REMOVE, orphanRemoval = true)
    @JsonIgnore
    @ApiModelProperty(value = "Referência ao agendamento da reavaliação física.")
    private Agendamento agendamentoReavaliacao;
    @ApiModelProperty(value = "Indica se houve aumento no percentual de gordura em relação à avaliação anterior.", example = "false")
    private Boolean aumentoPercentualGordura;
    @ApiModelProperty(value = "Identificador único da avaliação física na API externa.", example = "AVL_2024_001234")
    private String idAvaliacaoFisicaApi;
    @Transient
    @ApiModelProperty(value = "Lista das dobras cutâneas utilizadas no protocolo de avaliação selecionado.", example = "[\"abdominal\", \"triceps\", \"suprailiaca\"]")
    private List<String> dobrasUsadas;
    @Transient
    @ApiModelProperty(value = "Indica se esta avaliação está selecionada para processamento ou comparação.", example = "false")
    private Boolean selecionado = false;
    @Transient
    @ApiModelProperty(value = "Mapa de valores auxiliares para cálculos e processamentos internos.")
    private Map<String, Integer> valores = new HashMap<String, Integer>();


    @Transient
    @ApiModelProperty(value = "Diâmetro ósseo do punho em centímetros.", example = "5.8")
    private Double diametroPunho;
    @Transient
    @ApiModelProperty(value = "Diâmetro ósseo do joelho em centímetros.", example = "9.2")
    private Double diametroJoelho;
    @Transient
    @ApiModelProperty(value = "Diâmetro ósseo do cotovelo em centímetros.", example = "6.5")
    private Double diametroCotovelo;
    @Transient
    @ApiModelProperty(value = "Diâmetro ósseo do tornozelo em centímetros.", example = "7.1")
    private Double diametroTornozelo;

    public AvaliacaoFisica() {
    }

    public String getCategoriaApresentar() {
        return categoriaAvaliacaoIMC == null ? "" : categoriaAvaliacaoIMC.getDescricao();
    }

    public String getCategoriaPercGordApresentar() {
        return categoriaPercentualGordura == null ? "" : categoriaPercentualGordura.getDescricao();
    }

    public String getPercentualGorduraApresentar() {
        return percentualGordura == null ? "0" : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(percentualGordura));
    }

    public String getPercentualMassaMagraApresentar() {
        return percentualMassaMagra == null ? "0" : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(percentualMassaMagra));
    }

    public List<String> getDobrasUsadas() {
        if (dobrasUsadas == null) {
            if (cliente != null && cliente.isMenorIdade() && protocolo != null && protocolo.equals(ProtocolosAvaliacaoFisicaEnum.POLLOCK_3_DOBRAS)) {
                dobrasUsadas = Arrays.asList("coxa", "triceps");
                return dobrasUsadas;
            }
            dobrasUsadas = (cliente == null || cliente.getSexo() == null
                    || cliente.getSexo().equals("M")
                    || getProtocolo().getCamposMulher() == null
                    || getProtocolo().getCamposMulher().length == 0) ? Arrays.asList(getProtocolo().getCamposHomem())
                    : Arrays.asList(getProtocolo().getCamposMulher());
        }
        return dobrasUsadas;
    }

    public boolean getUsouAbdominal() {
        return !UteisValidacao.emptyNumber(getAbdominal());
    }

    public boolean getUsouSuprailiaca() {
        return !UteisValidacao.emptyNumber(getSupraIliaca());
    }

    public boolean getUsouTorax() {
        return !UteisValidacao.emptyNumber(getToraxBusto());
    }

    public boolean getUsouTriceps() {
        return !UteisValidacao.emptyNumber(getTriceps());
    }

    public boolean getUsouCoxa() {
        return !UteisValidacao.emptyNumber(getCoxaMedial());
    }

    public boolean getUsouBiceps() {
        return !UteisValidacao.emptyNumber(getBiceps());
    }

    public boolean getUsouPeso() {
        return !UteisValidacao.emptyNumber(getPeso());
    }

    public boolean getUsouSubescapular() {
        return !UteisValidacao.emptyNumber(getSubescapular());
    }

    public boolean getUsouAxilarMedia() {
        return !UteisValidacao.emptyNumber(getAxilarMedia());
    }

    public boolean getUsouAltura() {
        return !UteisValidacao.emptyNumber(getAltura());
    }

    public boolean getUsouPanturrilha() {
        return !UteisValidacao.emptyNumber(getPanturrilha());
    }

    public boolean getUsouSupraespinhal() {
        return !UteisValidacao.emptyNumber(getSupraEspinhal());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Double inicializarValor(Double valor) {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public Double getAbdominal() {
        return inicializarValor(abdominal);
    }

    public void setAbdominal(Double abdominal) {
        this.abdominal = abdominal;
    }

    public Double getPeitoral() {
        return inicializarValor(peitoral);
    }

    public void setPeitoral(Double peitoral) {
        this.peitoral = peitoral;
    }

    public Double getCoxaMedial() {
        return inicializarValor(coxaMedial);
    }

    public void setCoxaMedial(Double coxaMedial) {
        this.coxaMedial = coxaMedial;
    }

    public Double getAntebracoEsq() {
        return inicializarValor(antebracoEsq);
    }

    public void setAntebracoEsq(Double antebracoEsq) {
        this.antebracoEsq = antebracoEsq;
    }

    public Double getBracoRelaxadoEsq() {
        return inicializarValor(bracoRelaxadoEsq);
    }

    public void setBracoRelaxadoEsq(Double bracoRelaxadoEsq) {
        this.bracoRelaxadoEsq = bracoRelaxadoEsq;
    }

    public Double getBracoContraidoEsq() {
        return inicializarValor(bracoContraidoEsq);
    }

    public void setBracoContraidoEsq(Double bracoContraidoEsq) {
        this.bracoContraidoEsq = bracoContraidoEsq;
    }

    public Double getCoxaMediaEsq() {
        return inicializarValor(coxaMediaEsq);
    }

    public void setCoxaMediaEsq(Double coxaMediaEsq) {
        this.coxaMediaEsq = coxaMediaEsq;
    }

    public Double getPanturrilhaEsq() {
        return inicializarValor(panturrilhaEsq);
    }

    public void setPanturrilhaEsq(Double panturrilhaEsq) {
        this.panturrilhaEsq = panturrilhaEsq;
    }

    public Double getAntebracoDir() {
        return inicializarValor(antebracoDir);
    }

    public void setAntebracoDir(Double antebracoDir) {
        this.antebracoDir = antebracoDir;
    }

    public Double getBracoRelaxadoDir() {
        return inicializarValor(bracoRelaxadoDir);
    }

    public void setBracoRelaxadoDir(Double bracoRelaxadoDir) {
        this.bracoRelaxadoDir = bracoRelaxadoDir;
    }

    public Double getBracoContraidoDir() {
        return inicializarValor(bracoContraidoDir);
    }

    public void setBracoContraidoDir(Double bracoContraidoDir) {
        this.bracoContraidoDir = bracoContraidoDir;
    }

    public Double getCoxaMediaDir() {
        return inicializarValor(coxaMediaDir);
    }

    public void setCoxaMediaDir(Double coxaMediaDir) {
        this.coxaMediaDir = coxaMediaDir;
    }

    public Double getPanturrilhaDir() {
        return inicializarValor(panturrilhaDir);
    }

    public void setPanturrilhaDir(Double panturrilhaDir) {
        this.panturrilhaDir = panturrilhaDir;
    }

    public Double getToraxBusto() {
        return inicializarValor(toraxBusto);
    }

    public void setToraxBusto(Double toraxBusto) {
        this.toraxBusto = toraxBusto;
    }

    public Double getCintura() {
        return inicializarValor(cintura);
    }

    public void setCintura(Double cintura) {
        this.cintura = cintura;
    }

    public Double getGluteo() {
        return inicializarValor(gluteo);
    }

    public void setGluteo(Double gluteo) {
        this.gluteo = gluteo;
    }

    public Double getFlexibilidade() {
        return inicializarValor(flexibilidade);
    }

    public void setFlexibilidade(Double flexibilidade) {
        this.flexibilidade = flexibilidade;
    }

    public Double getPercentualGordura() {
        return inicializarValor(percentualGordura);
    }

    public void setPercentualGordura(Double percentualGordura) {
        this.percentualGordura = percentualGordura;
    }

    public CategoriaPercentualGordura getCategoriaPercentualGordura() {
        return categoriaPercentualGordura;
    }

    public void setCategoriaPercentualGordura(CategoriaPercentualGordura categoriaPercentualGordura) {
        this.categoriaPercentualGordura = categoriaPercentualGordura;
    }

    public Double getMassaMagra() {
        return inicializarValor(massaMagra);
    }

    public void setMassaMagra(Double massaMagra) {
        this.massaMagra = massaMagra;
    }

    public Double getMassaGorda() {
        return inicializarValor(massaGorda);
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }

    public Double getImc() {
        return inicializarValor(imc);
    }

    public String getImcApresentar() {
        return imc == null ? " - " : Uteis.formatarValorNumerico(Uteis.arredondarForcando2CasasDecimais(imc));
    }

    public String getMassaGordaApresentar() {
        return massaGorda == null ? " - " : Uteis.formatarValorNumerico(massaGorda);
    }

    public String getMassaMagraApresentar() {
        return massaMagra == null ? " - " : Uteis.formatarValorNumerico(massaMagra);
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }

    public CategoriaAvaliacaoIMC getCategoriaAvaliacaoIMC() {
        return categoriaAvaliacaoIMC;
    }

    public void setCategoriaAvaliacaoIMC(CategoriaAvaliacaoIMC categoriaAvaliacaoIMC) {
        this.categoriaAvaliacaoIMC = categoriaAvaliacaoIMC;
    }

    public Date getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Date dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Integer getResponsavelLancamento_codigo() {
        return responsavelLancamento_codigo;
    }

    public void setResponsavelLancamento_codigo(Integer responsavelLancamento) {
        this.responsavelLancamento_codigo = responsavelLancamento;
    }

    public Double getTotalDobras() {
        return inicializarValor(totalDobras);
    }

    public void setTotalDobras(Double totalDobras) {
        this.totalDobras = totalDobras;
    }

    public Double getTotalPerimetria() {
        return inicializarValor(totalPerimetria);
    }

    public void setTotalPerimetria(Double totalPerimetria) {
        this.totalPerimetria = totalPerimetria;
    }

    public String getDataAvaliacao_Apresentar() {
        return Uteis.getData(getDataAvaliacao());
    }

    public String getDataProxima_Apresentar() {
        return Uteis.getData(getDataProxima());
    }

    public Double getPeso() {
        return inicializarValor(peso);
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getAltura() {
        return inicializarValor(altura);
    }

    public void setAlturaString(String d) {
        altura = new Double(d.replace(",", "."));

    }

    public String getAlturaString() {
        try {
            return altura.toString().replace(".", ",");
        } catch (Exception e) {
            return "0,00";
        }
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Boolean getAumentoPercentualGordura() {
        return aumentoPercentualGordura;
    }

    public void setAumentoPercentualGordura(Boolean aumentoPercentualGordura) {
        this.aumentoPercentualGordura = aumentoPercentualGordura;
    }

    public Double getSupraIliaca() {
        return inicializarValor(supraIliaca);
    }

    public void setSupraIliaca(Double supraIliaca) {
        this.supraIliaca = supraIliaca;
    }

    public Double getTriceps() {
        return inicializarValor(triceps);
    }

    public void setTriceps(Double triceps) {
        this.triceps = triceps;
    }

    public Boolean getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(Boolean selecionado) {
        this.selecionado = selecionado;
    }

    public Double getSubescapular() {
        return inicializarValor(subescapular);
    }

    public void setSubescapular(Double subescapular) {
        this.subescapular = subescapular;
    }

    public Double getBiceps() {
        return inicializarValor(biceps);
    }

    public void setBiceps(Double biceps) {
        this.biceps = biceps;
    }

    public Double getAxilarMedia() {
        return inicializarValor(axilarMedia);
    }

    public void setAxilarMedia(Double axilarMedia) {
        this.axilarMedia = axilarMedia;
    }

    public Double getSupraEspinhal() {
        return inicializarValor(supraEspinhal);
    }

    public void setSupraEspinhal(Double supraEspinhal) {
        this.supraEspinhal = supraEspinhal;
    }

    public Double getPanturrilha() {
        return inicializarValor(panturrilha);
    }

    public void setPanturrilha(Double panturrilha) {
        this.panturrilha = panturrilha;
    }


    public Double getPunho() {
        return inicializarValor(punho);
    }

    public void setPunho(Double punho) {
        this.punho = punho;
    }

    public Double getQuadril() {
        return inicializarValor(quadril);
    }

    public void setQuadril(Double quadril) {
        this.quadril = quadril;
    }

    public Double getPercentualMassaMagra() {
        return inicializarValor(percentualMassaMagra);
    }

    public void setPercentualMassaMagra(Double percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public Double getPesoOsseo() {
        return inicializarValor(pesoOsseo);
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }

    public Double getPesoMuscular() {
        return inicializarValor(Uteis.forcarCasasDecimais(2, pesoMuscular == null ? 0.0 : pesoMuscular));
    }

    public void setPesoMuscular(Double pesoMuscular) {
        this.pesoMuscular = pesoMuscular;
    }

    public Double getResidual() {
        return inicializarValor(residual);
    }

    public void setResidual(Double residual) {
        this.residual = residual;
    }

    public ProtocolosAvaliacaoFisicaEnum getProtocolo() {
        if (protocolo == null) {
            protocolo = ProtocolosAvaliacaoFisicaEnum.POLLOCK_3_DOBRAS;
        }
        return protocolo;
    }

    public void setProtocolo(ProtocolosAvaliacaoFisicaEnum protocolo) {
        this.protocolo = protocolo;
    }

    public String getNameProtocolo() {
        return getProtocolo().name();
    }

    public Double getPercentualAgua() {
        return inicializarValor(percentualAgua);
    }

    public void setPercentualAgua(Double percentualAgua) {
        this.percentualAgua = percentualAgua;
    }

    public Double getTmb() {
        return inicializarValor(tmb);
    }

    public void setTmb(Double tmb) {
        this.tmb = tmb;
    }

    public Double getResistencia() {
        return inicializarValor(resistencia);
    }

    public void setResistencia(Double resistencia) {
        this.resistencia = resistencia;
    }

    public Double getReatancia() {
        return inicializarValor(reatancia);
    }

    public void setReatancia(Double reatancia) {
        this.reatancia = reatancia;
    }

    public Double getGorduraIdeal() {
        return inicializarValor(gorduraIdeal);
    }

    public void setGorduraIdeal(Double gorduraIdeal) {
        this.gorduraIdeal = gorduraIdeal;
    }

    public Double getNecessidadeFisica() {
        return inicializarValor(necessidadeFisica);
    }

    public void setNecessidadeFisica(Double necessidadeFisica) {
        this.necessidadeFisica = necessidadeFisica;
    }

    public Double getNecessidadeCalorica() {
        return inicializarValor(necessidadeCalorica);
    }

    public void setNecessidadeCalorica(Double necessidadeCalorica) {
        this.necessidadeCalorica = necessidadeCalorica;
    }

    public Double getCircunferenciaAbdominal() {
        return inicializarValor(circunferenciaAbdominal);
    }

    public void setCircunferenciaAbdominal(Double circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }


    public String getResultadoRiscoCA() {
        if (UteisValidacao.emptyNumber(circunferenciaAbdominal)) {
            return "avaliacao.circunferencia.normal";
        }
        double limiteAumentado = getCliente() == null
                || getCliente().isSexoMasculino() ? 94.0 : 80.0;
        double limiteAumentadoSubstancialmente = getCliente() == null
                || getCliente().isSexoMasculino() ? 102 : 88.0;
        return circunferenciaAbdominal >= limiteAumentadoSubstancialmente ? "avaliacao.circunferencia.aumentado.substancialmente" :
                circunferenciaAbdominal >= limiteAumentado ? "avaliacao.circunferencia.aumentado" : "avaliacao.circunferencia.normal";
    }

    public void setDobrasUsadas(List<String> dobrasUsadas) {
        this.dobrasUsadas = dobrasUsadas;
    }

    public Map<String, Integer> getValores() {
        return valores;
    }

    public void setValores(Map<String, Integer> valores) {
        this.valores = valores;
    }

    public List<String> getNiveis() {
        ArrayList<String> strings = new ArrayList<String>(valores.keySet());
        Collections.sort(strings);
        return strings;
    }

    public Double getFcMaxima() {
        return inicializarValor(fcMaxima);
    }

    public void setFcMaxima(Double fcMaxima) {
        this.fcMaxima = fcMaxima;
    }

    public Double getDistancia12() {
        return inicializarValor(distancia12);
    }

    public void setDistancia12(Double distancia12) {
        this.distancia12 = distancia12;
    }

    public Double getVo2Max12() {
        return inicializarValor(vo2Max12);
    }

    public void setVo2Max12(Double vo2Max12) {
        this.vo2Max12 = vo2Max12;
    }

    public Double getVoMaxAerobico() {
        return voMaxAerobico;
    }

    public void setVoMaxAerobico(Double voMaxAerobico) {
        this.voMaxAerobico = voMaxAerobico;
    }

    public Double getOmbro() {
        return inicializarValor(ombro);
    }

    public void setOmbro(Double ombro) {
        this.ombro = ombro;
    }

    public Double getPescoco() {
        return inicializarValor(pescoco);
    }

    public void setPescoco(Double pescoco) {
        this.pescoco = pescoco;
    }

    public Double getCoxaDistalDir() {
        return inicializarValor(coxaDistalDir);
    }

    public void setCoxaDistalDir(Double coxaDistalDir) {
        this.coxaDistalDir = coxaDistalDir;
    }

    public Double getCoxaDistalEsq() {
        return inicializarValor(coxaDistalEsq);
    }

    public void setCoxaDistalEsq(Double coxaDistalEsq) {
        this.coxaDistalEsq = coxaDistalEsq;
    }

    public Double getCoxaProximalDir() {
        return inicializarValor(coxaProximalDir);
    }

    public void setCoxaProximalDir(Double coxaProximalDir) {
        this.coxaProximalDir = coxaProximalDir;
    }

    public Double getCoxaProximalEsq() {
        return inicializarValor(coxaProximalEsq);
    }

    public void setCoxaProximalEsq(Double coxaProximalEsq) {
        this.coxaProximalEsq = coxaProximalEsq;
    }

    public String getAlturaApresentar() {
        return altura == null ? " - " : Uteis.formatarValorNumerico(altura);
    }

    public String getPesoApresentar() {
        return peso == null ? " - " : Uteis.formatarValorNumerico(peso);
    }

    public String getMetaApresentar() {
        return UteisValidacao.emptyNumber(metaPercentualGordura) ? " - " : Uteis.formatarValorNumerico(metaPercentualGordura);
    }

    public String getMetaAnteriorApresentar() {
        return UteisValidacao.emptyNumber(metaPercentualGorduraAnterior) ? " - " : Uteis.formatarValorNumerico(metaPercentualGorduraAnterior);
    }

    public String getTempo2400() {
        return tempo2400;
    }

    public void setTempo2400(String tempo2400) {
        this.tempo2400 = tempo2400;
    }

    public Double getVo2Max2400() {
        return inicializarValor(vo2Max2400);
    }

    public void setVo2Max2400(Double vo2Max2400) {
        this.vo2Max2400 = vo2Max2400;
    }

    public Date getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Date dataProxima) {
        this.dataProxima = dataProxima;
    }

    public Agendamento getAgendamentoReavaliacao() {
        return agendamentoReavaliacao;
    }

    public void setAgendamentoReavaliacao(Agendamento agendamentoReavaliacao) {
        this.agendamentoReavaliacao = agendamentoReavaliacao;
    }

    public Integer getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(Integer movProduto) {
        this.movProduto = movProduto;
    }

    public Integer getVenda() {
        return venda;
    }

    public void setVenda(Integer venda) {
        this.venda = venda;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public String getRecomendacoes() {
        return recomendacoes;
    }

    public void setRecomendacoes(String recomendacoes) {
        this.recomendacoes = recomendacoes;
    }

    public ProtocolosVo2MaxEnum getProtocoloVo() {
        if (protocoloVo == null) {
            protocoloVo = ProtocolosVo2MaxEnum.VO_CAMINHADA_CORRIDA_12_MINUTOS;
        }
        return protocoloVo;
    }


    public String getUrlProntaAssinatura() {
        return urlAssinatura == null ? "" : (Aplicacao.obterUrlFotoDaNuvem(urlAssinatura) + "?v=" + new Date().getTime());
    }

    public String getUrlAssinatura() {
        return urlAssinatura;
    }

    public void setUrlAssinatura(String urlAssinatura) {
        this.urlAssinatura = urlAssinatura;
    }

    public void setProtocoloVo(ProtocolosVo2MaxEnum protocoloVo) {
        this.protocoloVo = protocoloVo;
    }

    public Double getEctomorfia() {
        return inicializarValor(ectomorfia);
    }

    public void setEctomorfia(Double ectomorfia) {
        this.ectomorfia = ectomorfia;
    }

    public Double getMesomorfia() {
        return inicializarValor(mesomorfia);
    }

    public void setMesomorfia(Double mesomorfia) {
        this.mesomorfia = mesomorfia;
    }

    public Double getEndomorfia() { return inicializarValor(endomorfia); }

    public void setEndomorfia(Double endomorfia) {
        this.endomorfia = endomorfia;
    }

    public String getEctomorfiaApresentar() {
        return ectomorfia == null ? "0" : Uteis.formatarValorNumerico(Uteis.forcarCasasDecimais(3, ectomorfia));
    }

    public String getEndomorfiaApresentar() {
        return endomorfia == null ? "0" : Uteis.formatarValorNumerico(Uteis.forcarCasasDecimais(3, endomorfia));
    }

    public String getMesomorfiaApresentar() {
        return mesomorfia == null ? "0" : Uteis.formatarValorNumerico(Uteis.forcarCasasDecimais(3, mesomorfia));
    }

    public Double getFcAstrand4() { return inicializarValor(fcAstrand4); }

    public void setFcAstrand4(Double fcAstrand4) {
        this.fcAstrand4 = fcAstrand4;
    }

    public Double getFcAstrand5() { return inicializarValor(fcAstrand5); }

    public void setFcAstrand5(Double fcAstrand5) { this.fcAstrand5 = fcAstrand5; }

    public Double getCargaAstrand() {
        return inicializarValor(cargaAstrand);
    }

    public void setCargaAstrand(Double cargaAstrand) {
        this.cargaAstrand = cargaAstrand;
    }

    public Double getVo2Astrand() {
        return inicializarValor(vo2Astrand);
    }

    public void setVo2Astrand(Double vo2Astrand) {
        this.vo2Astrand = vo2Astrand;
    }

    public Double getVo2MaxAstrand() {
        return inicializarValor(vo2MaxAstrand);
    }

    public void setVo2MaxAstrand(Double vo2MaxAstrand) {
        this.vo2MaxAstrand = vo2MaxAstrand;
    }

    public Double getIdadeMetabolica() {
        return inicializarValor(idadeMetabolica);
    }

    public void setIdadeMetabolica(Double idadeMetabolica) {
        this.idadeMetabolica = idadeMetabolica;
    }

    public Double getGorduraVisceral() {
        return inicializarValor(gorduraVisceral);
    }

    public void setGorduraVisceral(Double gorduraVisceral) {
        this.gorduraVisceral = gorduraVisceral;
    }

    public Double getMetaPercentualGordura() {
        return inicializarValor(metaPercentualGordura);
    }

    public void setMetaPercentualGordura(Double metaPercentualGordura) {
        this.metaPercentualGordura = metaPercentualGordura;
    }

    public Double getMetaPercentualGorduraAnterior() {
        return inicializarValor(metaPercentualGorduraAnterior);
    }

    public void setMetaPercentualGorduraAnterior(Double metaPercentualGorduraAnterior) {
        this.metaPercentualGorduraAnterior = metaPercentualGorduraAnterior;
    }

    public Double getFcQueens() {
        return inicializarValor(fcQueens);
    }

    public void setFcQueens(Double fcQueens) {
        this.fcQueens = fcQueens;
    }

    public Double getVo2MaxQueens() {
        return inicializarValor(vo2MaxQueens);
    }

    public void setVo2MaxQueens(Double vo2MaxQueens) {
        this.vo2MaxQueens = vo2MaxQueens;
    }

    public Double getDiametroPunho() {
        return diametroPunho;
    }

    public void setDiametroPunho(Double diametroPunho) {
        this.diametroPunho = diametroPunho;
    }

    public Double getDiametroJoelho() {
        return diametroJoelho;
    }

    public void setDiametroJoelho(Double diametroJoelho) {
        this.diametroJoelho = diametroJoelho;
    }

    public Double getDiametroCotovelo() {
        return diametroCotovelo;
    }

    public void setDiametroCotovelo(Double diametroCotovelo) {
        this.diametroCotovelo = diametroCotovelo;
    }

    public Double getDiametroTornozelo() {
        return diametroTornozelo;
    }

    public void setDiametroTornozelo(Double diametroTornozelo) {
        this.diametroTornozelo = diametroTornozelo;
    }

    public AvaliacaoPostural getAvaliacaoPosturual() {
        return avaliacaoPosturual;
    }

    public void setAvaliacaoPosturual(AvaliacaoPostural avaliacaoPosturual) {
        this.avaliacaoPosturual = avaliacaoPosturual;
    }

    public String getLogBalanca() {
        return logBalanca;
    }

    public void setLogBalanca(String logBalanca) {
        this.logBalanca = logBalanca;
    }

    public String getDescricaoParaLog(AvaliacaoFisica a2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, a2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    public String getIdAvaliacaoFisicaApi() {
        return idAvaliacaoFisicaApi;
    }

    public void setIdAvaliacaoFisicaApi(String idAvaliacaoFisicaApi) {
        this.idAvaliacaoFisicaApi = idAvaliacaoFisicaApi;
    }
}
