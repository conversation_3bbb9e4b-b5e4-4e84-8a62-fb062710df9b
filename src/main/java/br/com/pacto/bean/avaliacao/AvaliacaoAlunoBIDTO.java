package br.com.pacto.bean.avaliacao;

import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.enumeradores.ProtocolosAvaliacaoFisicaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Contém os dados biométricos consolidados do aluno com base na avaliação física.")
public class AvaliacaoAlunoBIDTO {

    @ApiModelProperty(value = "Índice de Massa Corporal (IMC) calculado do aluno.", example = "23.5")
    private Double imc;

    @ApiModelProperty(value = "Altura do aluno em metros.", example = "1.75")
    private Double altura;

    @ApiModelProperty(value = "Peso atual do aluno em quilogramas.", example = "72.4")
    private Double peso;

    @ApiModelProperty(value = "Porcentagem de gordura corporal.", example = "18.2")
    private Double composicaoPorcentualGordura;

    @ApiModelProperty(value = "Porcentagem de massa magra.", example = "81.8")
    private Double composicaoPorcentualMassaMagra;

    @ApiModelProperty(value = "Massa magra em quilogramas.", example = "59.2")
    private Double composicaoMassaMagra;

    @ApiModelProperty(value = "Massa gorda em quilogramas.", example = "13.2")
    private Double composicaoMassaGorda;

    @ApiModelProperty(value = "Porcentagem estimada de resíduos corporais.", example = "5.0")
    private Double composicaoPorcentualResiduos;

    @ApiModelProperty(value = "Porcentagem estimada de ossos.", example = "4.0")
    private Double composicaoPorcentualOssos;

    @ApiModelProperty(value = "Porcentagem estimada de músculos.", example = "40.0")
    private Double composicaoPorcentualMusculos;

    @ApiModelProperty(value = "Massa muscular estimada em quilogramas.", example = "29.0")
    private Double musculos;

    @ApiModelProperty(value = "Quantidade de gordura em quilogramas.", example = "13.2")
    private Double gordura;

    @ApiModelProperty(value = "Quantidade de resíduos em quilogramas.", example = "3.6")
    private Double residuos;

    @ApiModelProperty(value = "Massa óssea estimada em quilogramas.", example = "2.9")
    private Double ossos;

    @ApiModelProperty(value = "Componente não identificado na composição corporal (pode incluir água, etc).", example = "0.5")
    private Double naoInformado;

    @ApiModelProperty(value = "Valor da circunferência abdominal em centímetros.", example = "85.0")
    private Double circunferenciaAbdominal;

    @ApiModelProperty(value = "Histórico de pesos anteriores do aluno.", example = "[85.3, 87.9]")
    private List<Double> historicoPeso;

    public AvaliacaoAlunoBIDTO(AvaliacaoFisica avaliacao, List<Double> historicoPeso, PesoOsseo pesoOsseo) {
        this.imc = Uteis.arredondarForcando2CasasDecimais((avaliacao.getImc() == null || avaliacao.getImc().isNaN()) ? 0.0 : avaliacao.getImc());
        this.altura = avaliacao.getAltura();
        this.peso = avaliacao.getPeso();
        this.composicaoPorcentualGordura = avaliacao.getPercentualGordura();
        this.composicaoPorcentualMassaMagra = avaliacao.getPercentualMassaMagra();
        this.composicaoPorcentualResiduos = Uteis.arredondarForcando2CasasDecimais(pesoOsseo.percentPeso(pesoOsseo.getPesoResidual()));
        this.composicaoPorcentualOssos = Uteis.arredondarForcando2CasasDecimais(pesoOsseo.percentPeso(pesoOsseo.getPesoOsseo()));
        this.composicaoPorcentualMusculos = Uteis.arredondarForcando2CasasDecimais(pesoOsseo.getPercMuscular());
        this.circunferenciaAbdominal = avaliacao.getCircunferenciaAbdominal();
        this.historicoPeso = historicoPeso;

        montarGrafico(avaliacao, pesoOsseo);

    }

    public void montarGrafico(AvaliacaoFisica avaliacao, PesoOsseo pesoOsseo) {

        if (this.peso == null && avaliacao != null && avaliacao.getPeso() != null) {
            this.peso = avaliacao.getPeso();
        }

        if (isBioimpedancia(avaliacao) && !UteisValidacao.emptyNumber(avaliacao.getPercentualMassaMagra())
                && !UteisValidacao.emptyNumber(avaliacao.getPercentualGordura())) {
            this.musculos = peso * (avaliacao.getPercentualMassaMagra() / 100.0);
            this.composicaoMassaMagra = peso * (avaliacao.getPercentualMassaMagra() / 100.0);
            this.gordura = peso * (avaliacao.getPercentualGordura() / 100.0);
            this.composicaoMassaGorda = peso * (avaliacao.getPercentualGordura() / 100.0);
            if (null != peso) {
                this.naoInformado = peso - musculos - gordura;
            } else {
                this.naoInformado = avaliacao.getPeso() - musculos - gordura;
            }
        } else if (isBioimpedancia(avaliacao) && !UteisValidacao.emptyNumber(avaliacao.getMassaMagra())
                && !UteisValidacao.emptyNumber(avaliacao.getMassaGorda())) {
            this.musculos = avaliacao.getMassaMagra();
            this.composicaoMassaMagra = avaliacao.getMassaMagra();
            this.gordura = avaliacao.getMassaGorda();
            this.composicaoMassaGorda = avaliacao.getMassaGorda();
            if (null != peso) {
                this.naoInformado = peso - musculos - gordura;
            } else {
                this.naoInformado = avaliacao.getPeso() - musculos - gordura;
            }
        } else {
            this.composicaoPorcentualGordura = avaliacao.getPercentualGordura();
            this.composicaoPorcentualMassaMagra = avaliacao.getPercentualMassaMagra();
            this.musculos = musculoGrafico(avaliacao, pesoOsseo);
            this.gordura = gorduraGrafico(avaliacao, pesoOsseo);
            this.residuos = residuoGrafico(avaliacao, pesoOsseo);
            this.ossos = ossosGrafico(avaliacao, pesoOsseo);
            this.naoInformado = naoInformadoGrafico(avaliacao, pesoOsseo);
            try {
                if (UteisValidacao.emptyNumber(this.composicaoPorcentualGordura) && !UteisValidacao.emptyNumber(this.gordura)) {
                    Double peso = this.musculos + this.gordura + this.residuos + this.ossos + this.naoInformado;
                    this.composicaoPorcentualGordura = Uteis.arredondar((this.gordura * 100.0) / peso, 2);
                }
            } catch (Exception e) {
                Uteis.logar(e, AvaliacaoAlunoBIDTO.class);
            }
        }

        if (isBioimpedancia(avaliacao) && UteisValidacao.emptyNumber(this.composicaoPorcentualMassaMagra)) {
            try {
                Double total =
                        (this.composicaoMassaMagra != null ? this.composicaoMassaMagra : 0.0) +
                                (this.composicaoMassaGorda != null ? this.composicaoMassaGorda : 0.0) +
                                (this.naoInformado != null ? this.naoInformado : 0.0);
                this.composicaoPorcentualMassaMagra =
                        total == 0 ? 0 : Uteis.arredondarForcando2CasasDecimais(((this.composicaoMassaMagra != null ? this.composicaoMassaMagra : 0.0) / total) * 100);
            } catch (Exception e) {
                this.composicaoPorcentualMassaMagra = 0.0;
                Uteis.logar(e, AvaliacaoAlunoBIDTO.class);
            }
        }

    }

    public Double gorduraGrafico(AvaliacaoFisica avaliacao, PesoOsseo pesoOsseo) {
        try {
            if (isBioimpedancia(avaliacao)) {
                if (UteisValidacao.emptyNumber(avaliacao.getMassaGorda()) && !UteisValidacao.emptyNumber(avaliacao.getPercentualGordura())) {
                    return (avaliacao.getPeso() * avaliacao.getPercentualGordura()) / 100.0;
                }
                return avaliacao.getMassaGorda();
            }
            return pesoOsseo.getPesoGorduraArredondado();
        } catch (Exception e) {
            return 0.0;
        }

    }

    public Double naoInformadoGrafico(AvaliacaoFisica avaliacao, PesoOsseo pesoOsseo) {
        try {
            Double gordura = gorduraGrafico(avaliacao, pesoOsseo);
            Double pesorestante = avaliacao.getPeso() - gordura - avaliacao.getPesoMuscular() - avaliacao.getPesoOsseo() - avaliacao.getResidual();
            return isBioimpedancia(avaliacao) && pesorestante >= 1.0 ?
                    Uteis.arredondarForcando2CasasDecimais(pesorestante)
                    : 0.0;
        } catch (Exception e) {
            return 0.0;
        }
    }

    public Double musculoGrafico(AvaliacaoFisica avaliacao, PesoOsseo pesoOsseo) {
        return isBioimpedancia(avaliacao) ? avaliacao.getPesoMuscular() : pesoOsseo.getPesoMuscularArredondado();
    }

    public Double ossosGrafico(AvaliacaoFisica avaliacao, PesoOsseo pesoOsseo) {
        return isBioimpedancia(avaliacao) ? avaliacao.getPesoOsseo() : pesoOsseo.getPesoOsseoArredondado();
    }

    public Double residuoGrafico(AvaliacaoFisica avaliacao, PesoOsseo pesoOsseo) {
        return isBioimpedancia(avaliacao) ? avaliacao.getResidual() : pesoOsseo.getPesoResidualArredondado();
    }

    public boolean isBioimpedancia(AvaliacaoFisica avaliacao) {
        return avaliacao != null && avaliacao.getProtocolo() != null
                && avaliacao.getProtocolo().equals(ProtocolosAvaliacaoFisicaEnum.BIOIMPEDANCIA);
    }

    public AvaliacaoAlunoBIDTO() {
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getComposicaoPorcentualGordura() {
        return composicaoPorcentualGordura;
    }

    public void setComposicaoPorcentualGordura(Double composicaoPorcentualGordura) {
        this.composicaoPorcentualGordura = composicaoPorcentualGordura;
    }

    public Double getComposicaoPorcentualResiduos() {
        return composicaoPorcentualResiduos;
    }

    public void setComposicaoPorcentualResiduos(Double composicaoPorcentualResiduos) {
        this.composicaoPorcentualResiduos = composicaoPorcentualResiduos;
    }

    public Double getComposicaoPorcentualOssos() {
        return composicaoPorcentualOssos;
    }

    public void setComposicaoPorcentualOssos(Double composicaoPorcentualOssos) {
        this.composicaoPorcentualOssos = composicaoPorcentualOssos;
    }

    public Double getComposicaoPorcentualMusculos() {
        return composicaoPorcentualMusculos;
    }

    public void setComposicaoPorcentualMusculos(Double composicaoPorcentualMusculos) {
        this.composicaoPorcentualMusculos = composicaoPorcentualMusculos;
    }

    public Double getCircunferenciaAbdominal() {
        return circunferenciaAbdominal;
    }

    public void setCircunferenciaAbdominal(Double circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }

    public List<Double> getHistoricoPeso() {
        return historicoPeso;
    }

    public void setHistoricoPeso(List<Double> historicoPeso) {
        this.historicoPeso = historicoPeso;
    }

    public Double getMusculos() {
        return musculos;
    }

    public void setMusculos(Double musculos) {
        this.musculos = musculos;
    }

    public Double getGordura() {
        return gordura;
    }

    public void setGordura(Double gordura) {
        this.gordura = gordura;
    }

    public Double getResiduos() {
        return residuos;
    }

    public void setResiduos(Double residuos) {
        this.residuos = residuos;
    }

    public Double getOssos() {
        return ossos;
    }

    public void setOssos(Double ossos) {
        this.ossos = ossos;
    }

    public Double getNaoInformado() {
        return naoInformado;
    }

    public void setNaoInformado(Double naoInformado) {
        this.naoInformado = naoInformado;
    }

    public Double getComposicaoPorcentualMassaMagra() {
        return composicaoPorcentualMassaMagra;
    }

    public void setComposicaoPorcentualMassaMagra(Double composicaoPorcentualMassaMagra) {
        this.composicaoPorcentualMassaMagra = composicaoPorcentualMassaMagra;
    }

    public Double getComposicaoMassaMagra() {
        return composicaoMassaMagra;
    }

    public void setComposicaoMassaMagra(Double composicaoMassaMagra) {
        this.composicaoMassaMagra = composicaoMassaMagra;
    }

    public Double getComposicaoMassaGorda() {
        return composicaoMassaGorda;
    }

    public void setComposicaoMassaGorda(Double composicaoMassaGorda) {
        this.composicaoMassaGorda = composicaoMassaGorda;
    }
}
