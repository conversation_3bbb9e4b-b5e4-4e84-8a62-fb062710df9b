package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de um movimento 3D avaliado, incluindo pontuações para ambos os lados do corpo.")
public class Movimento3DDTO {

    @ApiModelProperty(value = "Código identificador único do movimento.", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Identificador do item da avaliação física relacionado.", example = "456")
    private Integer item;

    @ApiModelProperty(value = "Código do tipo de movimento avaliado.", example = "1")
    protected Integer movimento;

    @ApiModelProperty(value = "Pontuação obtida no lado esquerdo do corpo (0-3 pontos).", example = "2")
    private Integer esquerda = 0;

    @ApiModelProperty(value = "Pontuação obtida no lado direito do corpo (0-3 pontos).", example = "3")
    private Integer direita = 0;

    public Movimento3DDTO() { }

    public Movimento3DDTO(Integer codigo, Integer item, Integer movimento, Integer esquerda, Integer direita) {
        this.codigo = codigo;
        this.item = item;
        this.movimento = movimento;
        this.esquerda = esquerda;
        this.direita = direita;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getItem() {
        return item;
    }

    public void setItem(Integer codItem) {
        this.item = codItem;
    }

    public Integer getMovimento() {
        return movimento;
    }

    public void setMovimento(Integer movimento) {
        this.movimento = movimento;
    }

    public Integer getEsquerda() {
        return esquerda;
    }

    public void setEsquerda(Integer esquerda) {
        this.esquerda = esquerda;
    }

    public Integer getDireita() {
        return direita;
    }

    public void setDireita(Integer direita) {
        this.direita = direita;
    }

}
