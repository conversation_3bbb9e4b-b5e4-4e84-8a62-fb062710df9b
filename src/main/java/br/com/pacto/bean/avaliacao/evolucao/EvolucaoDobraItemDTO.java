package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON> 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de evolução de uma dobra cutânea específica ao longo do tempo")
public class EvolucaoDobraItemDTO implements Serializable {

    @ApiModelProperty(value = "Nome da dobra cutânea medida", example = "Tricipital")
    private String nome;

    @ApiModelProperty(value = "Lista de pontos de medição da dobra cutânea ao longo do tempo")
    private List<EvolucaoDobraPontoDTO> pontos;

    public EvolucaoDobraItemDTO() {
    }

    public EvolucaoDobraItemDTO(String nome, EvolucaoDobraPontoDTO pontos) {
        this.pontos = new ArrayList<>();
        this.nome = nome;
        this.pontos.add(pontos);
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<EvolucaoDobraPontoDTO> getPontos() {
        return pontos;
    }

    public void setPontos(List<EvolucaoDobraPontoDTO> pontos) {
        this.pontos = pontos;
    }
}
