package br.com.pacto.bean.avaliacao.evolucao;

import br.com.pacto.bean.avaliacao.EvolucaoFisica;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR> <PERSON> 29/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados completos da evolução física do aluno, incluindo composição corporal, grupos musculares trabalhados e estatísticas de treino")
public class EvolucaoFisicaDTO implements Serializable {

    @ApiModelProperty(value = "Número total de avaliações físicas realizadas pelo aluno", example = "5")
    private Integer numeroAvaliacoes;

    @ApiModelProperty(value = "Número de dias considerados no período de análise", example = "90")
    private Long numeroDiasConsiderados;

    @ApiModelProperty(value = "Quantidade total de treinos realizados no período", example = "24")
    private Long treinosPeriodos;

    @ApiModelProperty(value = "Média de treinos realizados por semana no período", example = "2.8")
    private Double mediaTreinoSemana;

    @ApiModelProperty(value = "Histórico de evolução do Índice de Massa Corporal (IMC) do aluno ao longo do tempo")
    private Collection<HistoricoImcDTO> historicoImc;

    @ApiModelProperty(value = "Percentual atual de gordura corporal do aluno", example = "18.5")
    private Double percentualGordura;

    @ApiModelProperty(value = "Indica se o percentual de gordura aumentou em relação à avaliação anterior", example = "false")
    private Boolean percentualGorduraAumentado;

    @ApiModelProperty(value = "Massa gorda em quilogramas na primeira avaliação", example = "15.2")
    private Double massaGordaInicial;

    @ApiModelProperty(value = "Massa gorda em quilogramas na penúltima avaliação", example = "14.8")
    private Double massaGordaPenultima;

    @ApiModelProperty(value = "Massa gorda em quilogramas na avaliação mais recente", example = "14.1")
    private Double massaGordaFinal;

    @ApiModelProperty(value = "Percentual atual de massa magra (músculos, ossos, órgãos) do aluno", example = "81.5")
    private Double percentualMassaMagra;

    @ApiModelProperty(value = "Indica se o percentual de massa magra está aumentando", example = "true")
    private Boolean percentualMassaMagraAumentando;

    @ApiModelProperty(value = "Massa magra em quilogramas na primeira avaliação", example = "58.3")
    private Double massaMagraInicial;

    @ApiModelProperty(value = "Massa magra em quilogramas na penúltima avaliação", example = "59.1")
    private Double massaMagraPenultima;

    @ApiModelProperty(value = "Massa magra em quilogramas na avaliação mais recente", example = "60.2")
    private Double massaMagraFinal;

    @ApiModelProperty(value = "Dados detalhados dos grupos musculares trabalhados pelo aluno")
    private EvolucaoGrupoTrabalhadoDTO gruposTrabalhados;

    @ApiModelProperty(value = "Dados de evolução das medidas de perimetria (circunferências corporais)")
    private EvolucaoPerimetriaDTO perimetria;

    @ApiModelProperty(value = "Dados de proporção entre peso e gordura corporal ao longo do tempo")
    private Collection<EvolucaoProporcaoPesoGorduraPontoDTO> proporcaoPesoGordura;

    @ApiModelProperty(value = "Dados de evolução das dobras cutâneas medidas nas avaliações")
    private Collection<EvolucaoDobraItemDTO> dobras;

    @ApiModelProperty(value = "Percentual de exercícios realizados para cada grupo muscular", example = "85.7")
    private double porcentagemExerciciosGrupos;


    public EvolucaoFisicaDTO() {
    }

    public Integer getNumeroAvaliacoes() {
        return numeroAvaliacoes;
    }

    public void setNumeroAvaliacoes(Integer numeroAvaliacoes) {
        this.numeroAvaliacoes = numeroAvaliacoes;
    }

    public Long getNumeroDiasConsiderados() {
        return numeroDiasConsiderados;
    }

    public void setNumeroDiasConsiderados(Long numeroDiasConsiderados) {
        this.numeroDiasConsiderados = numeroDiasConsiderados;
    }

    public Long getTreinosPeriodos() {
        return treinosPeriodos;
    }

    public void setTreinosPeriodos(Long treinosPeriodos) {
        this.treinosPeriodos = treinosPeriodos;
    }

    public Double getMediaTreinoSemana() {
        return mediaTreinoSemana;
    }

    public void setMediaTreinoSemana(Double mediaTreinoSemana) {
        this.mediaTreinoSemana = mediaTreinoSemana;
    }

    public Collection<HistoricoImcDTO> getHistoricoImc() {
        return historicoImc;
    }

    public void setHistoricoImc(Collection<HistoricoImcDTO> historicoImc) {
        this.historicoImc = historicoImc;
    }

    public Double getPercentualGordura() {
        return percentualGordura;
    }

    public void setPercentualGordura(Double percentualGordura) {
        this.percentualGordura = percentualGordura;
    }

    public Boolean getPercentualGorduraAumentado() {
        return percentualGorduraAumentado;
    }

    public void setPercentualGorduraAumentado(Boolean percentualGorduraAumentado) {
        this.percentualGorduraAumentado = percentualGorduraAumentado;
    }

    public Double getMassaGordaInicial() {
        return massaGordaInicial;
    }

    public void setMassaGordaInicial(Double massaGordaInicial) {
        this.massaGordaInicial = massaGordaInicial;
    }

    public Double getMassaGordaFinal() {
        return massaGordaFinal;
    }

    public void setMassaGordaFinal(Double massaGordaFinal) {
        this.massaGordaFinal = massaGordaFinal;
    }

    public Double getPercentualMassaMagra() {
        return percentualMassaMagra;
    }

    public void setPercentualMassaMagra(Double percentualMassaMagra) {
        this.percentualMassaMagra = percentualMassaMagra;
    }

    public Boolean getPercentualMassaMagraAumentando() {
        return percentualMassaMagraAumentando;
    }

    public void setPercentualMassaMagraAumentando(Boolean percentualMassaMagraAumentando) {
        this.percentualMassaMagraAumentando = percentualMassaMagraAumentando;
    }

    public Double getMassaMagraInicial() {
        return massaMagraInicial;
    }

    public void setMassaMagraInicial(Double massaMagraInicial) {
        this.massaMagraInicial = massaMagraInicial;
    }

    public Double getMassaMagraFinal() {
        return massaMagraFinal;
    }

    public void setMassaMagraFinal(Double massaMagraFinal) {
        this.massaMagraFinal = massaMagraFinal;
    }

    public EvolucaoGrupoTrabalhadoDTO getGruposTrabalhados() {
        return gruposTrabalhados;
    }

    public void setGruposTrabalhados(EvolucaoGrupoTrabalhadoDTO gruposTrabalhados) {
        this.gruposTrabalhados = gruposTrabalhados;
    }

    public EvolucaoPerimetriaDTO getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(EvolucaoPerimetriaDTO perimetria) {
        this.perimetria = perimetria;
    }

    public Collection<EvolucaoProporcaoPesoGorduraPontoDTO> getProporcaoPesoGordura() {
        return proporcaoPesoGordura;
    }

    public void setProporcaoPesoGordura(Collection<EvolucaoProporcaoPesoGorduraPontoDTO> proporcaoPesoGordura) {
        this.proporcaoPesoGordura = proporcaoPesoGordura;
    }

    public Collection<EvolucaoDobraItemDTO> getDobras() {
        return dobras;
    }

    public void setDobras(Collection<EvolucaoDobraItemDTO> dobras) {
        this.dobras = dobras;
    }

    public double getPorcentagemExerciciosGrupos() {
        return porcentagemExerciciosGrupos;
    }

    public void setPorcentagemExerciciosGrupos(double porcentagemExerciciosGrupos) {
        this.porcentagemExerciciosGrupos = porcentagemExerciciosGrupos;
    }

    public Double getMassaGordaPenultima() {
        return massaGordaPenultima;
    }

    public void setMassaGordaPenultima(Double massaGordaPenultima) {
        this.massaGordaPenultima = massaGordaPenultima;
    }

    public Double getMassaMagraPenultima() {
        return massaMagraPenultima;
    }

    public void setMassaMagraPenultima(Double massaMagraPenultima) {
        this.massaMagraPenultima = massaMagraPenultima;
    }
}
