package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para criação ou atualização de uma avaliação postural, incluindo imagens fotográficas e análises posturais detalhadas.")
public class AvaliacaoPosturaDTOUpdate implements Serializable {

    @ApiModelProperty(value = "Identificador da imagem postural frontal já existente no sistema.", example = "img_frontal_123456")
    private String frenteImageId;

    @ApiModelProperty(value = "Identificador da imagem postural lateral direita já existente no sistema.", example = "img_direita_123456")
    private String direitaImageId;

    @ApiModelProperty(value = "Identificador da imagem postural lateral esquerda já existente no sistema.", example = "img_esquerda_123456")
    private String esquerdaImageId;

    @ApiModelProperty(value = "Identificador da imagem postural posterior (costas) já existente no sistema.", example = "img_costas_123456")
    private String costasImageId;

    @ApiModelProperty(value = "Nova imagem postural frontal codificada em Base64 para upload. Se fornecida, substitui a imagem anterior.", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...")
    private String frenteImageUpload;

    @ApiModelProperty(value = "Nova imagem postural lateral direita codificada em Base64 para upload. Se fornecida, substitui a imagem anterior.", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...")
    private String direitaImageUpload;

    @ApiModelProperty(value = "Nova imagem postural lateral esquerda codificada em Base64 para upload. Se fornecida, substitui a imagem anterior.", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...")
    private String esquerdaImageUpload;

    @ApiModelProperty(value = "Nova imagem postural posterior (costas) codificada em Base64 para upload. Se fornecida, substitui a imagem anterior.", example = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...")
    private String costasImageUpload;

    @ApiModelProperty(value = "Análise postural da visão lateral, incluindo avaliação de cabeça, ombros, coluna e membros inferiores.")
    private AnamnesePosturaLateralDTO visaoLateral;

    @ApiModelProperty(value = "Análise postural da visão posterior, incluindo avaliação de alinhamento da coluna vertebral e simetria corporal.")
    private AnamnesePosturalPosteriorDTO visaoPosterior;

    @ApiModelProperty(value = "Análise postural da visão anterior, incluindo avaliação de alinhamento frontal e posicionamento dos segmentos corporais.")
    private AnamnesePosturalAnteriorDTO visaoAnterior;

    @ApiModelProperty(value = "Análise de assimetrias posturais identificadas, incluindo desalinhamentos de ombros e quadril.")
    private AnamnesePosturalAssimetriasDTO assimetrias;

    @ApiModelProperty(value = "Observações gerais do avaliador sobre a postura do aluno, recomendações e considerações especiais.", example = "Aluno apresenta leve anteriorização da cabeça e elevação do ombro direito. Recomenda-se fortalecimento da musculatura cervical posterior e alongamento do trapézio superior.")
    private String observacao;

    public String getFrenteImageId() {
        return frenteImageId;
    }

    public void setFrenteImageId(String frenteImageId) {
        this.frenteImageId = frenteImageId;
    }

    public String getDireitaImageId() {
        return direitaImageId;
    }

    public void setDireitaImageId(String direitaImageId) {
        this.direitaImageId = direitaImageId;
    }

    public String getEsquerdaImageId() {
        return esquerdaImageId;
    }

    public void setEsquerdaImageId(String esquerdaImageId) {
        this.esquerdaImageId = esquerdaImageId;
    }

    public String getCostasImageId() {
        return costasImageId;
    }

    public void setCostasImageId(String costasImageId) {
        this.costasImageId = costasImageId;
    }

    public String getFrenteImageUpload() {
        return frenteImageUpload;
    }

    public void setFrenteImageUpload(String frenteImageUpload) {
        this.frenteImageUpload = frenteImageUpload;
    }

    public String getDireitaImageUpload() {
        return direitaImageUpload;
    }

    public void setDireitaImageUpload(String direitaImageUpload) {
        this.direitaImageUpload = direitaImageUpload;
    }

    public String getEsquerdaImageUpload() {
        return esquerdaImageUpload;
    }

    public void setEsquerdaImageUpload(String esquerdaImageUpload) {
        this.esquerdaImageUpload = esquerdaImageUpload;
    }

    public String getCostasImageUpload() {
        return costasImageUpload;
    }

    public void setCostasImageUpload(String costasImageUpload) {
        this.costasImageUpload = costasImageUpload;
    }

    public AnamnesePosturaLateralDTO getVisaoLateral() {
        return visaoLateral;
    }

    public void setVisaoLateral(AnamnesePosturaLateralDTO visaoLateral) {
        this.visaoLateral = visaoLateral;
    }

    public AnamnesePosturalPosteriorDTO getVisaoPosterior() {
        return visaoPosterior;
    }

    public void setVisaoPosterior(AnamnesePosturalPosteriorDTO visaoPosterior) {
        this.visaoPosterior = visaoPosterior;
    }

    public AnamnesePosturalAnteriorDTO getVisaoAnterior() {
        return visaoAnterior;
    }

    public void setVisaoAnterior(AnamnesePosturalAnteriorDTO visaoAnterior) {
        this.visaoAnterior = visaoAnterior;
    }

    public AnamnesePosturalAssimetriasDTO getAssimetrias() {
        return assimetrias;
    }

    public void setAssimetrias(AnamnesePosturalAssimetriasDTO assimetrias) {
        this.assimetrias = assimetrias;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
