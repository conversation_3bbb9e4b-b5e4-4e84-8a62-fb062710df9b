package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Resposta a uma pergunta específica da anamnese ou questionário PAR-Q.")
public class AnamnesePerguntaRespostaDTOUpdate implements Serializable {

    @ApiModelProperty(value = "Identificador único da pergunta da anamnese.", example = "25")
    private Integer anamnesePerguntaId;

    @ApiModelProperty(value = "Resposta fornecida pelo aluno à pergunta.", example = "Sim")
    private String resposta;

    @ApiModelProperty(value = "Observações adicionais sobre a resposta (opcional).", example = "Pratica corrida 3x por semana")
    private String observacao;

    public Integer getAnamnesePerguntaId() {
        return anamnesePerguntaId;
    }

    public void setAnamnesePerguntaId(Integer anamnesePerguntaId) {
        this.anamnesePerguntaId = anamnesePerguntaId;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

}
