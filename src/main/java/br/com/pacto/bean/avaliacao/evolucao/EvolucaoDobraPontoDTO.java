package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de um ponto de medição de dobra cutânea")
public class EvolucaoDobraPontoDTO implements Serializable {

    @ApiModelProperty(value = "Data da medição da dobra cutânea em timestamp", example = "1640995200000")
    private Long data;

    @ApiModelProperty(value = "Valor da dobra cutânea medida em milímetros", example = "12.5")
    private Double valor;

    public EvolucaoDobraPontoDTO() {
    }

    public EvolucaoDobraPontoDTO(Long data, Double valor) {
        this.data = data;
        this.valor = valor;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
