package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 18/02/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados históricos do Índice de Massa Corporal (IMC) do aluno")
public class HistoricoImcDTO implements Serializable {

    @ApiModelProperty(value = "Data da medição do IMC em timestamp", example = "1640995200000")
    private Long data;

    @ApiModelProperty(value = "Valor do Índice de Massa Corporal calculado (peso/altura²)", example = "24.5")
    private Double imc;

    public HistoricoImcDTO(Long data, Double imc) {
        this.data = data;
        this.imc = imc;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }
}
