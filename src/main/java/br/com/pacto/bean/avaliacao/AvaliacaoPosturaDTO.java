package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(description = "Representa os dados da avaliação postural do aluno, com imagens e observações de cada ângulo.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoPosturaDTO {

    @ApiModelProperty(value = "Imagem da vista frontal do aluno.")
    private AvaliacaoPosturaImageDTO frenteImageUri;

    @ApiModelProperty(value = "Imagem da vista lateral direita do aluno.")
    private AvaliacaoPosturaImageDTO direitaImageUri;

    @ApiModelProperty(value = "Imagem da vista lateral esquerda do aluno.")
    private AvaliacaoPosturaImageDTO esquerdaImageUri;

    @ApiModelProperty(value = "Imagem da vista posterior (costas) do aluno.")
    private AvaliacaoPosturaImageDTO costasImageUri;

    @ApiModelProperty(value = "Informações da visão lateral da avaliação postural.")
    private AnamnesePosturaLateralDTO visaoLateral;

    @ApiModelProperty(value = "Informações da visão posterior da avaliação postural.")
    private AnamnesePosturalPosteriorDTO visaoPosterior;

    @ApiModelProperty(value = "Informações da visão anterior da avaliação postural.")
    private AnamnesePosturalAnteriorDTO visaoAnterior;

    @ApiModelProperty(value = "Informações de assimetrias observadas na postura do aluno.")
    private AnamnesePosturalAssimetriasDTO assimetrias;

    @ApiModelProperty(value = "Observações gerais da avaliação postural.", example = "Leve inclinação à direita observada na região torácica.")
    private String observacao;

    public AvaliacaoPosturaDTO(AvaliacaoPostural postural, List<ItemAvaliacaoPostural> itens) throws Exception {
        this.frenteImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgPosterior());
        this.direitaImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgDireita());
        this.esquerdaImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgEsquerda());
        this.costasImageUri = new AvaliacaoPosturaImageDTO(postural.getKeyImgAnterior());
        this.visaoLateral = new AnamnesePosturaLateralDTO(itens);
        this.visaoPosterior = new AnamnesePosturalPosteriorDTO(itens);
        this.visaoAnterior = new AnamnesePosturalAnteriorDTO(itens);
        this.assimetrias = new AnamnesePosturalAssimetriasDTO(postural.getOmbros(), postural.getQuadril());
        this.observacao = postural.getObservacao();
    }

    public AvaliacaoPosturaImageDTO getFrenteImageUri() {
        return frenteImageUri;
    }

    public void setFrenteImageUri(AvaliacaoPosturaImageDTO frenteImageUri) {
        this.frenteImageUri = frenteImageUri;
    }

    public AvaliacaoPosturaImageDTO getDireitaImageUri() {
        return direitaImageUri;
    }

    public void setDireitaImageUri(AvaliacaoPosturaImageDTO direitaImageUri) {
        this.direitaImageUri = direitaImageUri;
    }

    public AvaliacaoPosturaImageDTO getEsquerdaImageUri() {
        return esquerdaImageUri;
    }

    public void setEsquerdaImageUri(AvaliacaoPosturaImageDTO esquerdaImageUri) {
        this.esquerdaImageUri = esquerdaImageUri;
    }

    public AvaliacaoPosturaImageDTO getCostasImageUri() {
        return costasImageUri;
    }

    public void setCostasImageUri(AvaliacaoPosturaImageDTO costasImageUri) {
        this.costasImageUri = costasImageUri;
    }

    public AnamnesePosturaLateralDTO getVisaoLateral() {
        return visaoLateral;
    }

    public void setVisaoLateral(AnamnesePosturaLateralDTO visaoLateral) {
        this.visaoLateral = visaoLateral;
    }

    public AnamnesePosturalPosteriorDTO getVisaoPosterior() {
        return visaoPosterior;
    }

    public void setVisaoPosterior(AnamnesePosturalPosteriorDTO visaoPosterior) {
        this.visaoPosterior = visaoPosterior;
    }

    public AnamnesePosturalAnteriorDTO getVisaoAnterior() {
        return visaoAnterior;
    }

    public void setVisaoAnterior(AnamnesePosturalAnteriorDTO visaoAnterior) {
        this.visaoAnterior = visaoAnterior;
    }

    public AnamnesePosturalAssimetriasDTO getAssimetrias() {
        return assimetrias;
    }

    public void setAssimetrias(AnamnesePosturalAssimetriasDTO assimetrias) {
        this.assimetrias = assimetrias;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
