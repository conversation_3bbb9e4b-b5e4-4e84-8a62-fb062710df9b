package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados dos testes de flexibilidade e mobilidade articular para avaliação física.")
public class AvaliacaoFlexibilidadeDTOUpdate implements Serializable {

    @ApiModelProperty(value = "Alcance máximo no teste de flexibilidade (cm).", example = "25")
    private Integer alcanceMaximo;

    @ApiModelProperty(value = "Observações gerais sobre os testes de flexibilidade.", example = "Boa flexibilidade geral, limitação leve no quadril")
    private String observacao;

    @ApiModelProperty(value = "Avaliação da mobilidade do ombro esquerdo.", example = "Normal")
    private String mobilidadeOmbroEsquerdo;

    @ApiModelProperty(value = "Avaliação da mobilidade do ombro direito.", example = "Limitada")
    private String mobilidadeOmbroDireito;

    @ApiModelProperty(value = "Avaliação da mobilidade do quadril esquerdo.", example = "Normal")
    private String mobilidadeQuadrilEsquerdo;

    @ApiModelProperty(value = "Avaliação da mobilidade do quadril direito.", example = "Normal")
    private String mobilidadeQuadrilDireito;

    @ApiModelProperty(value = "Avaliação da mobilidade do joelho esquerdo.", example = "Normal")
    private String mobilidadeJoelhoEsquerdo;

    @ApiModelProperty(value = "Avaliação da mobilidade do joelho direito.", example = "Normal")
    private String mobilidadeJoelhoDireito;

    @ApiModelProperty(value = "Grau de mobilidade do tornozelo esquerdo.", example = "90")
    private Integer mobilidadeTornozeloEsquerdo;

    @ApiModelProperty(value = "Grau de mobilidade do tornozelo direito.", example = "85")
    private Integer mobilidadeTornozeloDireito;

    @ApiModelProperty(value = "Observações específicas sobre a mobilidade dos ombros.", example = "Tensão muscular no ombro direito")
    private String observacaoOmbro;

    @ApiModelProperty(value = "Observações específicas sobre a mobilidade do quadril.", example = "Flexibilidade adequada")
    private String observacaoQuadril;

    @ApiModelProperty(value = "Observações específicas sobre a mobilidade dos joelhos.", example = "Sem limitações")
    private String observacaoJoelho;

    @ApiModelProperty(value = "Observações específicas sobre a mobilidade dos tornozelos.", example = "Leve limitação no tornozelo direito")
    private String observacaoTornozelo;

    public Integer getAlcanceMaximo() {
        return alcanceMaximo;
    }

    public void setAlcanceMaximo(Integer alcanceMaximo) {
        this.alcanceMaximo = alcanceMaximo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getMobilidadeOmbroEsquerdo() {
        return mobilidadeOmbroEsquerdo;
    }

    public void setMobilidadeOmbroEsquerdo(String mobilidadeOmbroEsquerdo) {
        this.mobilidadeOmbroEsquerdo = mobilidadeOmbroEsquerdo;
    }

    public String getMobilidadeOmbroDireito() {
        return mobilidadeOmbroDireito;
    }

    public void setMobilidadeOmbroDireito(String mobilidadeOmbroDireito) {
        this.mobilidadeOmbroDireito = mobilidadeOmbroDireito;
    }

    public String getMobilidadeJoelhoEsquerdo() {
        return mobilidadeJoelhoEsquerdo;
    }

    public void setMobilidadeJoelhoEsquerdo(String mobilidadeJoelhoEsquerdo) {
        this.mobilidadeJoelhoEsquerdo = mobilidadeJoelhoEsquerdo;
    }

    public String getMobilidadeJoelhoDireito() {
        return mobilidadeJoelhoDireito;
    }

    public void setMobilidadeJoelhoDireito(String mobilidadeJoelhoDireito) {
        this.mobilidadeJoelhoDireito = mobilidadeJoelhoDireito;
    }

    public String getMobilidadeQuadrilDireito() {
        return mobilidadeQuadrilDireito;
    }

    public void setMobilidadeQuadrilDireito(String mobilidadeQuadrilDireito) {
        this.mobilidadeQuadrilDireito = mobilidadeQuadrilDireito;
    }

    public String getMobilidadeQuadrilEsquerdo() {
        return mobilidadeQuadrilEsquerdo;
    }
    public void setMobilidadeQuadrilEsquerdo(String mobilidadeQuadrilEsquerdo) {
        this.mobilidadeQuadrilEsquerdo = mobilidadeQuadrilEsquerdo;
    }
    public String getObservacaoOmbro() {
        return observacaoOmbro;
    }

    public void setObservacaoOmbro(String observacaoOmbro) {
        this.observacaoOmbro = observacaoOmbro;
    }

    public String getObservacaoJoelho() {
        return observacaoJoelho;
    }

    public void setObservacaoJoelho(String observacaoJoelho) {
        this.observacaoJoelho = observacaoJoelho;
    }

    public String getObservacaoQuadril() {
        return observacaoQuadril;
    }

    public void setObservacaoQuadril(String observacaoQuadril) {
        this.observacaoQuadril = observacaoQuadril;
    }

    public String getObservacaoTornozelo() {
        return observacaoTornozelo;
    }

    public void setObservacaoTornozelo(String observacaoTornozelo) {
        this.observacaoTornozelo = observacaoTornozelo;
    }


    public Integer getMobilidadeTornozeloEsquerdo() {
        return mobilidadeTornozeloEsquerdo;
    }

    public void setMobilidadeTornozeloEsquerdo(Integer mobilidadeTornozeloEsquerdo) {
        this.mobilidadeTornozeloEsquerdo = mobilidadeTornozeloEsquerdo;
    }

    public Integer getMobilidadeTornozeloDireito() {
        return mobilidadeTornozeloDireito;
    }

    public void setMobilidadeTornozeloDireito(Integer mobilidadeTornozeloDireito) {
        this.mobilidadeTornozeloDireito = mobilidadeTornozeloDireito;
    }

}

