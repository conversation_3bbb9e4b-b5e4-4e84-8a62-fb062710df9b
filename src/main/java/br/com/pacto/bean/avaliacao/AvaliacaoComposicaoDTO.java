package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Composição corporal do aluno, detalhando pesos de diferentes componentes do corpo.")
public class AvaliacaoComposicaoDTO {

    @ApiModelProperty(value = "Peso de gordura corporal em kg.", example = "15.5")
    private Double pesoGordura;

    @ApiModelProperty(value = "Peso residual (outros componentes do corpo) em kg.", example = "12.3")
    private Double pesoResidual;

    @ApiModelProperty(value = "Peso muscular em kg.", example = "30.2")
    private Double pesoMuscular;

    @ApiModelProperty(value = "Peso ósseo em kg.", example = "4.5")
    private Double pesoOsseo;


    public AvaliacaoComposicaoDTO() {
    }

    public AvaliacaoComposicaoDTO(PesoOsseo pesoOsseo) {
        this.pesoGordura = pesoOsseo.getPesoGordura();
        this.pesoResidual = pesoOsseo.getPesoResidual();
        this.pesoMuscular = pesoOsseo.getPesoMuscular();
        this.pesoOsseo = pesoOsseo.getPesoOsseo();
    }

    public Double getPesoGordura() {
        return pesoGordura;
    }

    public void setPesoGordura(Double pesoGordura) {
        this.pesoGordura = pesoGordura;
    }

    public Double getPesoResidual() {
        return pesoResidual;
    }

    public void setPesoResidual(Double pesoResidual) {
        this.pesoResidual = pesoResidual;
    }

    public Double getPesoMuscular() {
        return pesoMuscular;
    }

    public void setPesoMuscular(Double pesoMuscular) {
        this.pesoMuscular = pesoMuscular;
    }

    public Double getPesoOsseo() {
        return pesoOsseo;
    }

    public void setPesoOsseo(Double pesoOsseo) {
        this.pesoOsseo = pesoOsseo;
    }
}
