package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Si<PERSON> 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para criação ou atualização de uma avaliação física completa.")
public class AvaliacaoFisicaDTOUpdate implements Serializable {
    @ApiModelProperty(value = "Identificador único da avaliação física (usado apenas para alterações).", example = "789")
    private Integer id;

    @ApiModelProperty(value = "Data da avaliação física em timestamp (milissegundos).", example = "1640995200000")
    private Long dataAvaliacao;

    @ApiModelProperty(value = "Data prevista para a próxima avaliação em timestamp (milissegundos).", example = "1648771200000")
    private Long dataProxima;

    @ApiModelProperty(value = "Identificador da anamnese selecionada para esta avaliação.", example = "15")
    private Integer anamneseSelecionadaId;

    @ApiModelProperty(value = "Lista de objetivos do aluno para a avaliação física.", example = "[\"Emagrecimento\", \"Aumento de força\", \"Melhora do condicionamento\"]")
    private List<String> objetivos;

    @ApiModelProperty(value = "Lista de respostas às perguntas da anamnese selecionada.")
    private List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas;

    @ApiModelProperty(value = "Lista de respostas ao questionário PAR-Q (Physical Activity Readiness Questionnaire).")
    private List<AnamnesePerguntaRespostaDTOUpdate> parQRespostas;

    @ApiModelProperty(value = "Dados de peso, altura e sinais vitais do aluno.")
    private AvaliacaoPesoAlturaDTO pesoAltura;

    @ApiModelProperty(value = "Medidas das dobras cutâneas para avaliação da composição corporal.")
    private AvaliacaoDobrasDTO dobras;

    @ApiModelProperty(value = "Medidas de perímetros corporais (circunferências).")
    private AvaliacaoPerimetriaDTO perimetria;

    @ApiModelProperty(value = "Dados dos testes de flexibilidade e mobilidade articular.")
    private AvaliacaoFlexibilidadeDTOUpdate flexibilidade;

    @ApiModelProperty(value = "Dados da avaliação postural com imagens e análises.")
    private AvaliacaoPosturaDTOUpdate postura;

    @ApiModelProperty(value = "Resultados dos testes de resistência muscular localizada (RML).")
    private AvaliacaoRMLDTOUpdate rml;

    @ApiModelProperty(value = "Dados dos testes de capacidade cardiovascular (VO2).")
    private AvaliacaoVo2DTO vo2;

    @ApiModelProperty(value = "Dados da avaliação de somatotipia (classificação do tipo físico).")
    private AvaliacaoSomatotipiaDTO somatotipia;

    @ApiModelProperty(value = "Metas estabelecidas e recomendações do avaliador.")
    private AvaliacaoMetaRecomendacoesDTO metaRecomendacoes;

    @ApiModelProperty(value = "Indica se a avaliação possui dados importados de equipamento Biosanny.", example = "false")
    private boolean temImportacaoBiosanny;

    @ApiModelProperty(value = "Origem da avaliação (manual, importação, integração).", example = "MANUAL")
    private String origemAvaliacao;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Long dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Long getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Long dataProxima) { this.dataProxima = dataProxima;
    }

    public Integer getAnamneseSelecionadaId() {
        return anamneseSelecionadaId;
    }

    public void setAnamneseSelecionadaId(Integer anamneseSelecionadaId) {
        this.anamneseSelecionadaId = anamneseSelecionadaId;
    }

    public List<String> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<String> objetivos) {
        this.objetivos = objetivos;
    }

    public List<AnamnesePerguntaRespostaDTOUpdate> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTOUpdate> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<AnamnesePerguntaRespostaDTOUpdate> getParQRespostas() {
        return parQRespostas;
    }

    public void setParQRespostas(List<AnamnesePerguntaRespostaDTOUpdate> parQRespostas) {
        this.parQRespostas = parQRespostas;
    }

    public AvaliacaoPesoAlturaDTO getPesoAltura() {
        return pesoAltura;
    }

    public void setPesoAltura(AvaliacaoPesoAlturaDTO pesoAltura) {
        this.pesoAltura = pesoAltura;
    }

    public AvaliacaoDobrasDTO getDobras() {
        return dobras;
    }

    public void setDobras(AvaliacaoDobrasDTO dobras) {
        this.dobras = dobras;
    }

    public AvaliacaoPerimetriaDTO getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(AvaliacaoPerimetriaDTO perimetria) {
        this.perimetria = perimetria;
    }

    public AvaliacaoFlexibilidadeDTOUpdate getFlexibilidade() {
        return flexibilidade;
    }

    public void setFlexibilidade(AvaliacaoFlexibilidadeDTOUpdate flexibilidade) {
        this.flexibilidade = flexibilidade;
    }

    public AvaliacaoPosturaDTOUpdate getPostura() {
        return postura;
    }

    public void setPostura(AvaliacaoPosturaDTOUpdate postura) {
        this.postura = postura;
    }

    public AvaliacaoRMLDTOUpdate getRml() {
        return rml;
    }

    public void setRml(AvaliacaoRMLDTOUpdate rml) {
        this.rml = rml;
    }

    public AvaliacaoVo2DTO getVo2() {
        return vo2;
    }

    public void setVo2(AvaliacaoVo2DTO vo2) {
        this.vo2 = vo2;
    }

    public AvaliacaoSomatotipiaDTO getSomatotipia() {
        return somatotipia;
    }

    public void setSomatotipia(AvaliacaoSomatotipiaDTO somatotipia) {
        this.somatotipia = somatotipia;
    }

    public AvaliacaoMetaRecomendacoesDTO getMetaRecomendacoes() {
        return metaRecomendacoes;
    }

    public void setMetaRecomendacoes(AvaliacaoMetaRecomendacoesDTO metaRecomendacoes) {
        this.metaRecomendacoes = metaRecomendacoes;
    }

    public boolean getTemImportacaoBiosanny() {
        return temImportacaoBiosanny;
    }

    public void setTemImportacaoBiosanny(boolean temImportacaoBiosanny) {
        this.temImportacaoBiosanny = temImportacaoBiosanny;
    }

    public String getOrigemAvaliacao() {
        return origemAvaliacao;
    }

    public void setOrigemAvaliacao(String origemAvaliacao) {
        this.origemAvaliacao = origemAvaliacao;
    }
}
