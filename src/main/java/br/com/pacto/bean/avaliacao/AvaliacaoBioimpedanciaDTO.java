package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.ALWAYS)
@ApiModel(description = "Dados extraídos de uma avaliação por bioimpedância, contendo medidas antropométricas, composição corporal e parâmetros elétricos corporais.")
public class AvaliacaoBioimpedanciaDTO {
    @ApiModelProperty(value = "Data da avaliação por bioimpedância no formato dd/MM/yyyy.", example = "18/06/2024")
    private String data;
    @ApiModelProperty(value = "Altura do cliente em centímetros.", example = "175.0")
    private Double altura;
    @ApiModelProperty(value = "Peso corporal do cliente em quilogramas.", example = "75.5")
    private Double peso;
    @ApiModelProperty(value = "Resistência corporal medida por bioimpedância em ohms.", example = "485.2")
    private Double resistencia;
    @ApiModelProperty(value = "Reatância corporal medida por bioimpedância em ohms.", example = "52.8")
    private Double reatancia;
    @ApiModelProperty(value = "Perímetro abdominal em centímetros.", example = "85.2")
    private Double perimetroAbdominal;
    @ApiModelProperty(value = "Percentual de água corporal total.", example = "58.7")
    private Double aguaCorporalPercentual;
    @ApiModelProperty(value = "Massa livre de gordura (massa magra) em quilogramas.", example = "63.6")
    private Double massaLivreKg;
    @ApiModelProperty(value = "Percentual de massa livre de gordura (massa magra).", example = "84.2")
    private Double massaLivrePercentual;
    @ApiModelProperty(value = "Massa de gordura corporal em quilogramas.", example = "11.9")
    private Double gorduraCorporalKg;
    @ApiModelProperty(value = "Percentual de gordura corporal.", example = "15.8")
    private Double gorduraCorporalPercentual;
    @ApiModelProperty(value = "Massa muscular esquelética em quilogramas.", example = "51.1")
    private Double massaMuscularEsqueleticaKg;
    @ApiModelProperty(value = "Índice de Massa Corporal (IMC) calculado.", example = "24.7")
    private Double imc;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getResistencia() {
        return resistencia;
    }

    public void setResistencia(Double resistencia) {
        this.resistencia = resistencia;
    }

    public Double getReatancia() {
        return reatancia;
    }

    public void setReatancia(Double reatancia) {
        this.reatancia = reatancia;
    }

    public Double getPerimetroAbdominal() {
        return perimetroAbdominal;
    }

    public void setPerimetroAbdominal(Double perimetroAbdominal) {
        this.perimetroAbdominal = perimetroAbdominal;
    }

    public Double getAguaCorporalPercentual() {
        return aguaCorporalPercentual;
    }

    public void setAguaCorporalPercentual(Double aguaCorporalPercentual) {
        this.aguaCorporalPercentual = aguaCorporalPercentual;
    }

    public Double getMassaLivreKg() {
        return massaLivreKg;
    }

    public void setMassaLivreKg(Double massaLivreKg) {
        this.massaLivreKg = massaLivreKg;
    }

    public Double getMassaLivrePercentual() {
        return massaLivrePercentual;
    }

    public void setMassaLivrePercentual(Double massaLivrePercentual) {
        this.massaLivrePercentual = massaLivrePercentual;
    }

    public Double getGorduraCorporalKg() {
        return gorduraCorporalKg;
    }

    public void setGorduraCorporalKg(Double gorduraCorporalKg) {
        this.gorduraCorporalKg = gorduraCorporalKg;
    }

    public Double getGorduraCorporalPercentual() {
        return gorduraCorporalPercentual;
    }

    public void setGorduraCorporalPercentual(Double gorduraCorporalPercentual) {
        this.gorduraCorporalPercentual = gorduraCorporalPercentual;
    }

    public Double getMassaMuscularEsqueleticaKg() {
        return massaMuscularEsqueleticaKg;
    }

    public void setMassaMuscularEsqueleticaKg(Double massaMuscularEsqueleticaKg) {
        this.massaMuscularEsqueleticaKg = massaMuscularEsqueleticaKg;
    }

    public Double getImc() {
        return imc;
    }

    public void setImc(Double imc) {
        this.imc = imc;
    }
}
