package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Metas estabelecidas e recomendações do avaliador para o aluno.")
public class AvaliacaoMetaRecomendacoesDTO {

    @ApiModelProperty(value = "Percentual de gordura corporal da avaliação anterior (%).", example = "18.5")
    private Double percentualGorduraAnterior;

    @ApiModelProperty(value = "Meta de percentual de gordura corporal para a próxima avaliação (%).", example = "15.0")
    private Double percentualGorduraProxima;

    @ApiModelProperty(value = "Observações e recomendações do avaliador para o aluno.", example = "Manter frequência de treinos, aumentar atividade cardiovascular, atenção à dieta")
    private String observacoesAvaliador;

    public AvaliacaoMetaRecomendacoesDTO() {
    }

    public AvaliacaoMetaRecomendacoesDTO(AvaliacaoFisica avaliacaoFisica) {
        this.percentualGorduraAnterior = avaliacaoFisica.getMetaPercentualGorduraAnterior();
        this.percentualGorduraProxima = avaliacaoFisica.getMetaPercentualGordura();
        this.observacoesAvaliador = avaliacaoFisica.getRecomendacoes();
    }

    public Double getPercentualGorduraAnterior() {
        return percentualGorduraAnterior;
    }

    public void setPercentualGorduraAnterior(Double percentualGorduraAnterior) {
        this.percentualGorduraAnterior = percentualGorduraAnterior;
    }

    public Double getPercentualGorduraProxima() {
        return percentualGorduraProxima;
    }

    public void setPercentualGorduraProxima(Double percentualGorduraProxima) {
        this.percentualGorduraProxima = percentualGorduraProxima;
    }

    public String getObservacoesAvaliador() {
        return observacoesAvaliador;
    }

    public void setObservacoesAvaliador(String observacoesAvaliador) {
        this.observacoesAvaliador = observacoesAvaliador;
    }
}
