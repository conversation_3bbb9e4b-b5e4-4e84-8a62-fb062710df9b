package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para importação de arquivo de bioimpedância.")
public class ImportarAvaliacaoBioimpedanciaDTO {

    @ApiModelProperty(value = "Conteúdo do arquivo de bioimpedância codificado em Base64 para processamento.", example = "UEsDBBQAAAAIAE...")
    private String arquivoUpload;

    public String getArquivoUpload() {
        return arquivoUpload;
    }

    public void setArquivoUpload(String arquivoUpload) {
        this.arquivoUpload = arquivoUpload;
    }
}
