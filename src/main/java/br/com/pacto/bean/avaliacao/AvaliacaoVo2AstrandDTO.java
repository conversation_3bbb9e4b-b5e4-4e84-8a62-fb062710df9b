package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados do teste de VO2 pelo protocolo Astrand para avaliação cardiovascular.")
public class AvaliacaoVo2AstrandDTO {

    @ApiModelProperty(value = "Frequência cardíaca no 4º minuto do teste Astrand (bpm).", example = "150.0")
    private Double astrandFrequencia4;

    @ApiModelProperty(value = "Frequência cardíaca no 5º minuto do teste Astrand (bpm).", example = "148.0")
    private Double astrandFrequencia5;

    @ApiModelProperty(value = "Carga utilizada no teste Astrand (watts).", example = "100.0")
    private Double astrandCarga;

    public Double getAstrandFrequencia4() {
        return astrandFrequencia4;
    }

    public void setAstrandFrequencia4(Double astrandFrequencia4) {
        this.astrandFrequencia4 = astrandFrequencia4;
    }

    public Double getAstrandFrequencia5() { return astrandFrequencia5; }

    public void setAstrandFrequencia5(Double astrandFrequencia5) { this.astrandFrequencia5 = astrandFrequencia5; }

    public Double getAstrandCarga() {
        return astrandCarga;
    }

    public void setAstrandCarga(Double astrandCarga) {
        this.astrandCarga = astrandCarga;
    }
}
