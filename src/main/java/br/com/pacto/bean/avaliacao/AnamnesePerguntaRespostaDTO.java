package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.PerguntaTO;
import br.com.pacto.bean.anamnese.RespostaCliente;
import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Representa a resposta de uma pergunta da anamnese do aluno.")
public class AnamnesePerguntaRespostaDTO {

    @ApiModelProperty(value = "Identificador da resposta.", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Objeto com os dados da pergunta da anamnese.")
    private PerguntaTO anamnesePergunta;

    @ApiModelProperty(value = "Identificador da pergunta da anamnese.", example = "456")
    private Integer perguntaAnamneseId;

    @ApiModelProperty(value = "Resposta fornecida pelo aluno. Pode ser de qualquer tipo (texto, número, booleano etc).")
    private Object resposta;

    @ApiModelProperty(value = "Observações adicionais sobre a resposta.", example = "Paciente relatou dores no joelho ao correr.")
    private String observacao;

    public AnamnesePerguntaRespostaDTO() {
    }

    public AnamnesePerguntaRespostaDTO(RespostaCliente resposta) {
        this.id = resposta.getCodigo();
        this.anamnesePergunta = new PerguntaTO(resposta.getPerguntaAnamnese().getPergunta());
        this.perguntaAnamneseId = resposta.getPerguntaAnamnese().getCodigo();
        switch (resposta.getPerguntaAnamnese().getPergunta().getTipoPergunta()){
            case SIM_NAO:
                this.resposta = resposta.getResposta();
                break;
            case SIMPLES_ESCOLHA:
                this.resposta = StringUtils.isBlank(resposta.getResposta()) ? null : resposta.getResposta().matches("[0-9]+") ? Integer.valueOf(resposta.getResposta()) : null;
                break;
            case MULTIPLA_ESCOLHA:
                if(!UteisValidacao.emptyString(resposta.getResposta())){
                    String[] split = resposta.getResposta().split("\\_");
                    List<Integer> respostas = new ArrayList<Integer>();
                    for(String r : split){
                        if (isNumber(r)){
                            respostas.add(Integer.valueOf(r));
                        }
                    }
                    this.resposta = respostas;
                }
                break;
            case TEXTUAL:
                this.resposta = resposta.getResposta();
                break;
        }
        this.observacao = resposta.getObs();
    }

    private static final Pattern NUMBER_PATTERN = Pattern.compile(
            "[\\x00-\\x20]*[+-]?(NaN|Infinity|((((\\p{Digit}+)(\\.)?((\\p{Digit}+)?)" +
                    "([eE][+-]?(\\p{Digit}+))?)|(\\.((\\p{Digit}+))([eE][+-]?(\\p{Digit}+))?)|" +
                    "(((0[xX](\\p{XDigit}+)(\\.)?)|(0[xX](\\p{XDigit}+)?(\\.)(\\p{XDigit}+)))" +
                    "[pP][+-]?(\\p{Digit}+)))[fFdD]?))[\\x00-\\x20]*");

    public boolean isNumber(String s){
        return NUMBER_PATTERN.matcher(s).matches();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public PerguntaTO getAnamnesePergunta() {
        return anamnesePergunta;
    }

    public void setAnamnesePergunta(PerguntaTO anamnesePergunta) {
        this.anamnesePergunta = anamnesePergunta;
    }

    public Object getResposta() {
        return resposta;
    }

    public void setResposta(Object resposta) {
        this.resposta = resposta;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Integer getPerguntaAnamneseId() {
        return perguntaAnamneseId;
    }

    public void setPerguntaAnamneseId(Integer perguntaAnamneseId) {
        this.perguntaAnamneseId = perguntaAnamneseId;
    }
}
