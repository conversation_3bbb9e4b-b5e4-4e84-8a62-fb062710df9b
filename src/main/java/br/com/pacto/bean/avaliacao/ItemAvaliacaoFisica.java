/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.Anamnese;
import br.com.pacto.bean.anamnese.Movimento3D;
import br.com.pacto.bean.anamnese.ResultadoMovimentoEnum;
import br.com.pacto.bean.anamnese.ResultadoVidaEnum;
import br.com.pacto.bean.cliente.ClienteSintetico;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.util.impl.UtilReflection;
import org.json.JSONObject;
import edu.emory.mathcs.backport.java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa um item específico de uma avaliação física, contendo dados de mobilidade, estabilidade, qualidade de movimento e vida, além de referências às entidades relacionadas como cliente, anamnese e movimentos 3D.")
public class ItemAvaliacaoFisica {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único do item de avaliação física no banco de dados.", example = "12345")
    private Integer codigo;

    @ApiModelProperty(value = "Resultado ou valor obtido neste item específico da avaliação física. Pode conter valores numéricos, textuais ou booleanos dependendo do tipo de item.", example = "75.5")
    private String result;

    @ApiModelProperty(value = "Código do usuário responsável pelo lançamento/criação deste item de avaliação.", example = "789")
    private Integer responsavelLancamento_codigo;

    @ManyToOne
    @ApiModelProperty(value = "Referência ao cliente/aluno ao qual este item de avaliação pertence.")
    private ClienteSintetico cliente;

    @ManyToOne
    @ApiModelProperty(value = "Referência à avaliação física principal da qual este item faz parte.")
    private AvaliacaoFisica avaliacaoFisica;

    @ManyToOne
    @ApiModelProperty(value = "Referência à ficha de anamnese utilizada neste item de avaliação.")
    private Anamnese anamnese;

    @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "item")
    @ApiModelProperty(value = "Lista de movimentos 3D associados a este item de avaliação, incluindo testes de mobilidade e estabilidade.")
    private List<Movimento3D> movimentos3D;

    @OneToOne
    @ApiModelProperty(value = "Referência aos dados de ventilometria (testes respiratórios) associados a este item.")
    private Ventilometria ventilometria;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data e hora em que este item de avaliação foi criado/lançado no sistema.", example = "2024-06-18T14:30:00Z")
    private Date dataLancamento;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data e hora da resposta ou execução deste item de avaliação.", example = "2024-06-18T15:45:00Z")
    private Date dataResposta = Calendario.hoje();

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Tipo específico do item de avaliação física. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- <strong>PRESSAO_ARTERIAL</strong> (Pressão Arterial)\n" +
            "- <strong>RESISTENCIA_MUSCULAR_ABDOMEN</strong> (Resistência Muscular - Abdômen)\n" +
            "- <strong>RESISTENCIA_MUSCULAR_BRACO</strong> (Resistência Muscular - Braço)\n" +
            "- <strong>ALTURA</strong> (Altura do Aluno)\n" +
            "- <strong>PESO</strong> (Peso do Aluno)\n" +
            "- <strong>OBJETIVOS</strong> (Objetivos do Aluno)\n" +
            "- <strong>PARQ</strong> (Questionário PAR-Q)\n" +
            "- <strong>FREQUENCIA_CARDIACA</strong> (Frequência Cardíaca)\n" +
            "- <strong>PESO_OSSEO</strong> (Peso Ósseo)\n" +
            "- <strong>ANAMNESE</strong> (Anamnese)\n" +
            "- <strong>VENTILOMETRIA</strong> (Ventilometria)\n" +
            "- <strong>FLEXIBILIDADE</strong> (Flexibilidade)\n" +
            "- <strong>AVALIACAO_INTEGRADA</strong> (Avaliação Integrada)\n" +
            "- <strong>PRESSAO_ARTERIAL_SISTOLICA</strong> (Pressão Arterial Sistólica)\n" +
            "- <strong>PRESSAO_ARTERIAL_DIASTOLICA</strong> (Pressão Arterial Diastólica)\n" +
            "- <strong>RESISTENCIA_MUSCULAR_ABDOMEN_HOMEM</strong> (Resistência Muscular Abdômen - Homem)\n" +
            "- <strong>RESISTENCIA_MUSCULAR_ABDOMEN_MULHER</strong> (Resistência Muscular Abdômen - Mulher)\n" +
            "- <strong>RESISTENCIA_MUSCULAR_BRACO_HOMEM</strong> (Resistência Muscular Braço - Homem)\n" +
            "- <strong>RESISTENCIA_MUSCULAR_BRACO_MULHER</strong> (Resistência Muscular Braço - Mulher)", example = "AVALIACAO_INTEGRADA")
    private ItemAvaliacaoFisicaEnum item;
    @ApiModelProperty(value = "Soma total dos pontos de mobilidade obtidos no lado direito do corpo.", example = "28")
    private Integer somaMobilidadeDir = 0;

    @ApiModelProperty(value = "Soma total dos pontos de estabilidade obtidos no lado direito do corpo.", example = "24")
    private Integer somaEstabilidadeDir = 0;

    @ApiModelProperty(value = "Média dos pontos de mobilidade do lado direito do corpo.", example = "7.0")
    private Double mediaMobilidadeDir = 0.0;

    @ApiModelProperty(value = "Média dos pontos de estabilidade do lado direito do corpo.", example = "6.0")
    private Double mediaEstabilidadeDir = 0.0;

    @ApiModelProperty(value = "Soma total dos pontos de mobilidade obtidos no lado esquerdo do corpo.", example = "26")
    private Integer somaMobilidadeEsq = 0;

    @ApiModelProperty(value = "Soma total dos pontos de estabilidade obtidos no lado esquerdo do corpo.", example = "22")
    private Integer somaEstabilidadeEsq = 0;

    @ApiModelProperty(value = "Média dos pontos de mobilidade do lado esquerdo do corpo.", example = "6.5")
    private Double mediaMobilidadeEsq = 0.0;

    @ApiModelProperty(value = "Média dos pontos de estabilidade do lado esquerdo do corpo.", example = "5.5")
    private Double mediaEstabilidadeEsq = 0.0;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Classificação da qualidade de vida baseada na soma dos pontos da anamnese. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- <strong>FRACA</strong> (0-5 pontos)\n" +
            "- <strong>MEDIA</strong> (6-8 pontos)\n" +
            "- <strong>BOA</strong> (9-11 pontos)\n" +
            "- <strong>MUITO_BOA</strong> (12-13 pontos)\n" +
            "- <strong>EXCELENTE</strong> (14-15 pontos)", example = "MUITO_BOA")
    private ResultadoVidaEnum qualidadeVida;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Classificação da qualidade de movimento baseada na soma dos pontos de mobilidade e estabilidade. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- <strong>FRACA</strong> (0.0-6.0 pontos)\n" +
            "- <strong>MEDIA</strong> (6.1-10.0 pontos)\n" +
            "- <strong>BOA</strong> (10.1-14.0 pontos)\n" +
            "- <strong>MUITO_BOA</strong> (14.1-16.0 pontos)\n" +
            "- <strong>EXCELENTE</strong> (16.1-18.0 pontos)", example = "BOA")
    private ResultadoMovimentoEnum qualidadeMovimento;

    @ApiModelProperty(value = "Soma total dos pontos de qualidade de movimento calculados.", example = "14.5")
    private Double somaQualidadeMovimento;

    @ApiModelProperty(value = "Soma total dos pontos de qualidade de vida calculados.", example = "12")
    private Integer somaQualidadeVida;
    @Transient
    @ApiModelProperty(value = "Lista de movimentos 3D específicos de mobilidade (cadeia anterior, posterior, lateral, rotacional) separados para facilitar o processamento.")
    private List<Movimento3D> mobilidade;

    @Transient
    @ApiModelProperty(value = "Lista de movimentos 3D específicos de estabilidade (controle, fechamento, abertura) separados para facilitar o processamento.")
    private List<Movimento3D> estabilidade;

    @Transient
    @ApiModelProperty(value = "Indica se este item está selecionado na interface do usuário (usado para operações em lote).", example = "false")
    private Boolean selecionado = false;

    @Transient
    @ApiModelProperty(value = "Indica se este item foi editado/modificado na sessão atual (controle de estado da interface).", example = "false")
    private Boolean editado = false;

    public ItemAvaliacaoFisica() {
    }

    public ItemAvaliacaoFisica(ItemAvaliacaoFisicaEnum i, String result, Date data, ClienteSintetico cliente, Integer responsavelLancamento, AvaliacaoFisica avaliacaoFisica) {
        this.item = i;
        this.result = result;
        this.responsavelLancamento_codigo = responsavelLancamento;
        this.cliente = cliente;
        this.dataLancamento = data;
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getResult() {
        if(result == null){
            result = "0";
        }
        return result;
    }

    public void setResult(String pressao) {
        this.result = pressao;
    }


    public Boolean getResultBoolean() {
        try {
            return Boolean.valueOf(result);
        }catch (Exception e){
            return false;
        }
    }

    public void setResultBoolean(Boolean pressao) {
        this.result = pressao.toString();
    }

    public Integer getResponsavelLancamento_codigo() {
        return responsavelLancamento_codigo;
    }

    public void setResponsavelLancamento_codigo(Integer responsavelLancamento) {
        this.responsavelLancamento_codigo = responsavelLancamento;
    }

    public String getDataLancamentoApresentar(){
        return dataLancamento == null ? "" : Uteis.getDataAplicandoFormatacao(dataLancamento,"dd/MM/yyyy HH:mm");
    }

    public ClienteSintetico getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSintetico cliente) {
        this.cliente = cliente;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public ItemAvaliacaoFisicaEnum getItem() {
        return item;
    }

    public void setItem(ItemAvaliacaoFisicaEnum item) {
        this.item = item;
    }

    public List<String> getObjs(){
        if(result == null || result.isEmpty()){
            return new ArrayList<String>();
        }
        return Arrays.asList(result.split("\\|\\#\\|"));
    }

    public Anamnese getAnamnese() {
        return anamnese;
    }

    public void setAnamnese(Anamnese anamnese) {
        this.anamnese = anamnese;
    }

    public List<Movimento3D> getMovimentos3D() {
        if (movimentos3D == null) {
            movimentos3D = new ArrayList<>();
        }
        return movimentos3D;
    }

    public Ventilometria getVentilometria() {
        return ventilometria;
    }

    public void setVentilometria(Ventilometria ventilometria) {
        this.ventilometria = ventilometria;
    }

    public Integer getSomaMobilidadeDir() {
        return somaMobilidadeDir;
    }

    public void setSomaMobilidadeDir(Integer somaMobilidadeDir) {
        this.somaMobilidadeDir = somaMobilidadeDir;
    }

    public Integer getSomaEstabilidadeDir() {
        return somaEstabilidadeDir;
    }

    public void setSomaEstabilidadeDir(Integer somaEstabilidadeDir) {
        this.somaEstabilidadeDir = somaEstabilidadeDir;
    }

    public Double getMediaMobilidadeDir() {
        return mediaMobilidadeDir;
    }

    public void setMediaMobilidadeDir(Double mediaMobilidadeDir) {
        this.mediaMobilidadeDir = mediaMobilidadeDir;
    }

    public Double getMediaEstabilidadeDir() {
        return mediaEstabilidadeDir;
    }

    public void setMediaEstabilidadeDir(Double mediaEstabilidadeDir) {
        this.mediaEstabilidadeDir = mediaEstabilidadeDir;
    }

    public Integer getSomaMobilidadeEsq() {
        return somaMobilidadeEsq;
    }

    public void setSomaMobilidadeEsq(Integer somaMobilidadeEsq) {
        this.somaMobilidadeEsq = somaMobilidadeEsq;
    }

    public Integer getSomaEstabilidadeEsq() {
        return somaEstabilidadeEsq;
    }

    public void setSomaEstabilidadeEsq(Integer somaEstabilidadeEsq) {
        this.somaEstabilidadeEsq = somaEstabilidadeEsq;
    }

    public Double getMediaMobilidadeEsq() {
        return mediaMobilidadeEsq;
    }

    public void setMediaMobilidadeEsq(Double mediaMobilidadeEsq) {
        this.mediaMobilidadeEsq = mediaMobilidadeEsq;
    }

    public Double getMediaEstabilidadeEsq() {
        return mediaEstabilidadeEsq;
    }

    public void setMediaEstabilidadeEsq(Double mediaEstabilidadeEsq) {
        this.mediaEstabilidadeEsq = mediaEstabilidadeEsq;
    }

    public ResultadoVidaEnum getQualidadeVida() {
        return qualidadeVida;
    }

    public void setQualidadeVida(ResultadoVidaEnum qualidadeVida) {
        this.qualidadeVida = qualidadeVida;
    }

    public ResultadoMovimentoEnum getQualidadeMovimento() {
        return qualidadeMovimento;
    }

    public void setQualidadeMovimento(ResultadoMovimentoEnum qualidadeMovimento) {
        this.qualidadeMovimento = qualidadeMovimento;
    }

    public Double getSomaQualidadeMovimento() {
        return somaQualidadeMovimento;
    }

    public void setSomaQualidadeMovimento(Double somaQualidadeMovimento) {
        this.somaQualidadeMovimento = somaQualidadeMovimento;
    }

    public Integer getSomaQualidadeVida() {
        return somaQualidadeVida;
    }

    public void setSomaQualidadeVida(Integer somaQualidadeVida) {
        this.somaQualidadeVida = somaQualidadeVida;
    }

    public Date getDataResposta() {
        return dataResposta;
    }

    public void setDataResposta(Date dataResposta) {
        this.dataResposta = dataResposta;
    }

    public List<Movimento3D> getMobilidade() {
        return mobilidade;
    }

    public void setMobilidade(List<Movimento3D> mobilidade) {
        this.mobilidade = mobilidade;
    }

    public List<Movimento3D> getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(List<Movimento3D> estabilidade) {
        this.estabilidade = estabilidade;
    }

    public String getLinkPrint() throws Exception{
        JSONObject j = new JSONObject();
        j.put("i", getCodigo());
        return "..integrada?j=".concat(Uteis.encriptar(j.toString(), "cript0p4r4msint"));
    }

    public AvaliacaoFisica getAvaliacaoFisica() {
        return avaliacaoFisica;
    }

    public void setAvaliacaoFisica(AvaliacaoFisica avaliacaoFisica) {
        this.avaliacaoFisica = avaliacaoFisica;
    }

    public Boolean getSelecionado() {
        return selecionado;}

    public void setSelecionado(Boolean selecionado) {this.selecionado = selecionado;}

    public Boolean getEditado() { return editado; }

    public void setEditado(Boolean editado) { this.editado = editado; }

    public String getDescricaoParaLog(ItemAvaliacaoFisica iAf2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, iAf2));
            return log.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }
}
