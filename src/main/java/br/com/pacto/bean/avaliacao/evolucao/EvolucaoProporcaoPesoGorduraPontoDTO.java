package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de um ponto na evolução da proporção entre peso corporal e massa gorda")
public class EvolucaoProporcaoPesoGorduraPontoDTO implements Serializable {

    @ApiModelProperty(value = "Data da medição em timestamp", example = "1640995200000")
    private Long data;

    @ApiModelProperty(value = "Peso corporal total em quilogramas", example = "75.5")
    private Double peso;

    @ApiModelProperty(value = "Massa gorda em quilogramas", example = "14.2")
    private Double massaGorda;

    public EvolucaoProporcaoPesoGorduraPontoDTO(Long data, Double peso, Double massaGorda) {
        this.data = data;
        this.peso = peso;
        this.massaGorda = massaGorda;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getMassaGorda() {
        return massaGorda;
    }

    public void setMassaGorda(Double massaGorda) {
        this.massaGorda = massaGorda;
    }
}
