package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados da avaliação de resistência muscular localizada (RML) do aluno.")
public class AvaliacaoRMLDTO {

    @ApiModelProperty(value = "Quantidade de flexões de braço realizadas pelo aluno.", example = "22")
    private Integer flexoesBracos;

    @ApiModelProperty(value = "Quantidade de abdominais realizados pelo aluno.", example = "30")
    private Integer abdominais;

    @ApiModelProperty(
            value = "Classificação da coluna para flexões de braço.\n\n" +
                    "<strong>Valores possíveis:</strong>\n" +
                    "- EXCELENTE\n" +
                    "- ACIMA_MEDIA\n" +
                    "- MEDIA\n" +
                    "- ABAIXO_MEDIA\n" +
                    "- FRACO",
            example = "MEDIA"
    )
    private CategoriaColuna rmlFlexoesColuna;

    @ApiModelProperty(
            value = "Classificação da linha para flexões de braço com base no sexo e faixa etária."
    )
    private RMLLinha rmlFlexoesLinha;

    @ApiModelProperty(
            value = "Classificação da coluna para abdominais.\n\n" +
                    "<strong>Valores possíveis:</strong>\n" +
                    "- EXCELENTE\n" +
                    "- ACIMA_MEDIA\n" +
                    "- MEDIA\n" +
                    "- ABAIXO_MEDIA\n" +
                    "- FRACO",
            example = "ACIMA_MEDIA"
    )
    private CategoriaColuna rmlAbominaisColuna;

    @ApiModelProperty(
            value = "Classificação da linha para abdominais com base no sexo e faixa etária.\n\n" +
                    "<strong>Valores possíveis:</strong>\n" +
                    "- CAT_15_19 (15 - 19 anos)\n" +
                    "- CAT_20_29 (20 - 29 anos)\n" +
                    "- CAT_30_39 (30 - 39 anos)\n" +
                    "- CAT_40_49 (40 - 49 anos)\n" +
                    "- CAT_50_59 (50 - 59 anos)\n" +
                    "- CAT_60_69 (60 anos ou mais)",
            example = "CAT_20_29"
    )
    private RMLLinha rmlAbominaisLinha;


    public AvaliacaoRMLDTO(Integer flexoes,
                           Integer abdominais,
                           RMLLinha rmlLinha,
                           CategoriaColuna rmlFlexoesColuna,
                           CategoriaColuna rmlAbominaisColuna) {
        this.flexoesBracos = flexoes;
        this.abdominais = abdominais;
        this.rmlFlexoesColuna = rmlFlexoesColuna;
        this.rmlFlexoesLinha = rmlLinha;
        this.rmlAbominaisColuna = rmlAbominaisColuna;
        this.rmlAbominaisLinha = rmlLinha;
    }

    public Integer getFlexoesBracos() {
        return flexoesBracos;
    }

    public void setFlexoesBracos(Integer flexoesBracos) {
        this.flexoesBracos = flexoesBracos;
    }

    public Integer getAbdominais() {
        return abdominais;
    }

    public void setAbdominais(Integer abdominais) {
        this.abdominais = abdominais;
    }

    public CategoriaColuna getRmlFlexoesColuna() {
        return rmlFlexoesColuna;
    }

    public void setRmlFlexoesColuna(CategoriaColuna rmlFlexoesColuna) {
        this.rmlFlexoesColuna = rmlFlexoesColuna;
    }

    public RMLLinha getRmlFlexoesLinha() {
        return rmlFlexoesLinha;
    }

    public void setRmlFlexoesLinha(RMLLinha rmlFlexoesLinha) {
        this.rmlFlexoesLinha = rmlFlexoesLinha;
    }

    public CategoriaColuna getRmlAbominaisColuna() {
        return rmlAbominaisColuna;
    }

    public void setRmlAbominaisColuna(CategoriaColuna rmlAbominaisColuna) {
        this.rmlAbominaisColuna = rmlAbominaisColuna;
    }

    public RMLLinha getRmlAbominaisLinha() {
        return rmlAbominaisLinha;
    }

    public void setRmlAbominaisLinha(RMLLinha rmlAbominaisLinha) {
        this.rmlAbominaisLinha = rmlAbominaisLinha;
    }
}
