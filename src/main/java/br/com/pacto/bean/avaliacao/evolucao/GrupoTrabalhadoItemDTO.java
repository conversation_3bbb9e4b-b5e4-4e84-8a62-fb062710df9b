package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.text.DecimalFormat;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de um grupo muscular trabalhado, incluindo quantidade de exercícios e percentual de trabalho")
public class GrupoTrabalhadoItemDTO implements Serializable {

    @ApiModelProperty(value = "Nome do grupo muscular trabalhado", example = "Peitoral")
    private String nome;

    @ApiModelProperty(value = "Quantidade de exercícios/séries realizadas para este grupo muscular", example = "12.0")
    private Double exercicios;

    @ApiModelProperty(value = "Percentual que este grupo muscular representa do total de exercícios realizados", example = "25.0")
    private Double porcentagem;

    public GrupoTrabalhadoItemDTO(String nome, Double exercicios, Integer total) {
        this.nome = nome;
        this.exercicios = exercicios;
        DecimalFormat formato = new DecimalFormat("#");
        this.porcentagem = Double.valueOf(formato.format((exercicios / total) * 100));
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Double getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public Double getExercicios() {
        return exercicios;
    }

    public void setExercicios(Double exercicios) {
        this.exercicios = exercicios;
    }
}
