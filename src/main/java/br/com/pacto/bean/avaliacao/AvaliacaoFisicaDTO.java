package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.controller.json.aluno.ClienteSinteticoDTO;
import br.com.pacto.controller.json.avaliacao.AvaliadorDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;


@ApiModel(description = "Contém os dados de uma avaliação física completa do aluno.")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AvaliacaoFisicaDTO {

    @ApiModelProperty(value = "Identificador da avaliação física.", example = "123")
    private Integer id;

    @ApiModelProperty(value = "Data da avaliação física em formato timestamp.", example = "1717958400000")
    private Long dataAvaliacao;

    @ApiModelProperty(value = "Data da próxima avaliação em formato timestamp.", example = "1725638400000")
    private Long dataProxima;

    @ApiModelProperty(value = "Anamnese selecionada para esta avaliação.")
    private AnamneseTO anamneseSelecionada;

    @ApiModelProperty(value = "Lista de objetivos informados pelo aluno.", example = "[\"Emagrecimento\", \"Aumento de força\"]")
    private List<String> objetivos;

    @ApiModelProperty(value = "Informações resumidas do aluno.")
    private AvaliacaoAlunoBIDTO alunoBI;

    @ApiModelProperty(value = "Lista de telefones de contato do aluno.", example = "[\"(99)99999-1234\"]")
    private List<String> telefones;

    @ApiModelProperty(value = "Lista de e-mails do aluno.", example = "[\"<EMAIL>\"]")
    private List<String> emails;

    @ApiModelProperty(value = "Lista de perguntas e respostas da anamnese.")
    private List<AnamnesePerguntaRespostaDTO> anamneseRespostas;

    @ApiModelProperty(value = "Lista de respostas ao questionário PAR-Q.")
    private List<AnamnesePerguntaRespostaDTO> parQRespostas;

    @ApiModelProperty(value = "Informações sobre peso e altura do aluno.")
    private AvaliacaoPesoAlturaDTO pesoAltura;

    @ApiModelProperty(value = "Informações das dobras cutâneas.")
    private AvaliacaoDobrasDTO dobras;

    @ApiModelProperty(value = "Informações de perímetros corporais.")
    private AvaliacaoPerimetriaDTO perimetria;

    @ApiModelProperty(value = "Composição corporal do aluno.")
    private AvaliacaoComposicaoDTO composicao;

    @ApiModelProperty(value = "Resultados dos testes de flexibilidade.")
    private AvaliacaoFlexibilidadeDTO flexibilidade;

    @ApiModelProperty(value = "Resultados da avaliação postural.")
    private AvaliacaoPosturaDTO postura;

    @ApiModelProperty(value = "Resultados do teste de resistência muscular localizada (RML).")
    private AvaliacaoRMLDTO rml;

    @ApiModelProperty(value = "Resultados estimados de consumo de oxigênio (VO2).")
    private AvaliacaoVo2DTO vo2;

    @ApiModelProperty(value = "Resultados da avaliação de somatotipia.")
    private AvaliacaoSomatotipiaDTO somatotipia;

    @ApiModelProperty(value = "Metas e recomendações definidas com base na avaliação.")
    private AvaliacaoMetaRecomendacoesDTO metaRecomendacoes;

    @ApiModelProperty(value = "Indica se o aluno foi aprovado no PAR-Q.", example = "true")
    private Boolean resultadoParq;

    @ApiModelProperty(value = "Dados do avaliador responsável pela avaliação.")
    private AvaliadorDTO avaliador;

    @ApiModelProperty(value = "Nota descritiva da composição corporal.", example = "Boa")
    private String composicaoNota;

    @ApiModelProperty(value = "Nota descritiva do índice de massa corporal (IMC).", example = "Adequado")
    private String imcNota;

    @ApiModelProperty(value = "Nota descritiva do condicionamento cardiovascular.", example = "Excelente")
    private String cardioNota;

    @ApiModelProperty(value = "Informações básicas do cliente vinculado à avaliação.")
    private ClienteSinteticoDTO cliente;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataAvaliacao() {
        return dataAvaliacao;
    }

    public void setDataAvaliacao(Long dataAvaliacao) {
        this.dataAvaliacao = dataAvaliacao;
    }

    public Long getDataProxima() {
        return dataProxima;
    }

    public void setDataProxima(Long dataProxima) {
        this.dataProxima = dataProxima;
    }

    public AnamneseTO getAnamneseSelecionada() {
        return anamneseSelecionada;
    }

    public void setAnamneseSelecionada(AnamneseTO anamneseSelecionada) {
        this.anamneseSelecionada = anamneseSelecionada;
    }

    public List<String> getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(List<String> objetivos) {
        this.objetivos = objetivos;
    }

    public AvaliacaoAlunoBIDTO getAlunoBI() {
        return alunoBI;
    }

    public void setAlunoBI(AvaliacaoAlunoBIDTO alunoBI) {
        this.alunoBI = alunoBI;
    }

    public List<AnamnesePerguntaRespostaDTO> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTO> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<AnamnesePerguntaRespostaDTO> getParQRespostas() {
        return parQRespostas;
    }

    public void setParQRespostas(List<AnamnesePerguntaRespostaDTO> parQRespostas) {
        this.parQRespostas = parQRespostas;
    }

    public AvaliacaoPesoAlturaDTO getPesoAltura() {
        return pesoAltura;
    }

    public void setPesoAltura(AvaliacaoPesoAlturaDTO pesoAltura) {
        this.pesoAltura = pesoAltura;
    }

    public AvaliacaoDobrasDTO getDobras() {
        return dobras;
    }

    public void setDobras(AvaliacaoDobrasDTO dobras) {
        this.dobras = dobras;
    }

    public AvaliacaoPerimetriaDTO getPerimetria() {
        return perimetria;
    }

    public void setPerimetria(AvaliacaoPerimetriaDTO perimetria) {
        this.perimetria = perimetria;
    }

    public AvaliacaoComposicaoDTO getComposicao() {
        return composicao;
    }

    public void setComposicao(AvaliacaoComposicaoDTO composicao) {
        this.composicao = composicao;
    }

    public AvaliacaoFlexibilidadeDTO getFlexibilidade() {
        return flexibilidade;
    }

    public void setFlexibilidade(AvaliacaoFlexibilidadeDTO flexibilidade) {
        this.flexibilidade = flexibilidade;
    }

    public AvaliacaoPosturaDTO getPostura() {
        return postura;
    }

    public void setPostura(AvaliacaoPosturaDTO postura) {
        this.postura = postura;
    }

    public AvaliacaoRMLDTO getRml() {
        return rml;
    }

    public void setRml(AvaliacaoRMLDTO rml) {
        this.rml = rml;
    }

    public AvaliacaoVo2DTO getVo2() {
        return vo2;
    }

    public void setVo2(AvaliacaoVo2DTO vo2) {
        this.vo2 = vo2;
    }

    public AvaliacaoSomatotipiaDTO getSomatotipia() {
        return somatotipia;
    }

    public void setSomatotipia(AvaliacaoSomatotipiaDTO somatotipia) {
        this.somatotipia = somatotipia;
    }

    public AvaliacaoMetaRecomendacoesDTO getMetaRecomendacoes() {
        return metaRecomendacoes;
    }

    public void setMetaRecomendacoes(AvaliacaoMetaRecomendacoesDTO metaRecomendacoes) {
        this.metaRecomendacoes = metaRecomendacoes;
    }

    public Boolean getResultadoParq() {
        return resultadoParq;
    }

    public void setResultadoParq(Boolean resultadoParq) {
        this.resultadoParq = resultadoParq;
    }

    public List<String> getTelefones() {
        return telefones;
    }

    public void setTelefones(List<String> telefones) {
        this.telefones = telefones;
    }

    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(List<String> emails) {
        this.emails = emails;
    }

    public AvaliadorDTO getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(AvaliadorDTO avaliador) {
        this.avaliador = avaliador;
    }

    public String getComposicaoNota() {return composicaoNota;}

    public void setComposicaoNota(String composicaoNota) {this.composicaoNota = composicaoNota;}

    public String getImcNota() {return imcNota;}

    public void setImcNota(String imcNota) {this.imcNota = imcNota;}

    public String getCardioNota() {
        return cardioNota;
    }

    public void setCardioNota(String cardioNota) {
        this.cardioNota = cardioNota;
    }

    public ClienteSinteticoDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSinteticoDTO cliente) {
        this.cliente = cliente;
    }
}
