package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 22/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados dos testes de Resistência Muscular Localizada (RML) para avaliação física.")
public class AvaliacaoRMLDTOUpdate implements Serializable {

    @ApiModelProperty(value = "Quantidade de flexões de braço realizadas pelo aluno no teste.", example = "22")
    private Integer flexoesBracos;

    @ApiModelProperty(value = "Quantidade de abdominais realizados pelo aluno no teste.", example = "35")
    private Integer abdominais;

    public Integer getFlexoesBracos() {
        return flexoesBracos;
    }

    public void setFlexoesBracos(Integer flexoesBracos) {
        this.flexoesBracos = flexoesBracos;
    }

    public Integer getAbdominais() {
        return abdominais;
    }

    public void setAbdominais(Integer abdominais) {
        this.abdominais = abdominais;
    }
}
