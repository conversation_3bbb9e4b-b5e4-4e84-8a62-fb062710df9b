package br.com.pacto.bean.avaliacao;

import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações sobre a avaliação postural com visão posterior do aluno.")
public class AnamnesePosturalPosteriorDTO {

    @ApiModelProperty(value = "Indica se há depressão escapular.", example = "false")
    private Boolean depressaoEscapular;

    @ApiModelProperty(value = "Indica se há encurtamento do trapézio.", example = "true")
    private Boolean encurtamentoTrapezio;

    @ApiModelProperty(value = "Indica se há escoliose na região cervical.", example = "false")
    private Boolean escolioseCervical;

    @ApiModelProperty(value = "Indica se há escoliose na região lombar.", example = "true")
    private Boolean escolioseLombar;

    @ApiModelProperty(value = "Indica se há escoliose na região torácica.", example = "true")
    private Boolean escolioseToracica;

    @ApiModelProperty(value = "Indica se há protração escapular.", example = "false")
    private Boolean protacaoEscapular;

    @ApiModelProperty(value = "Indica se há pé valgo (desvio do calcanhar para fora).", example = "true")
    private Boolean peValgo;

    @ApiModelProperty(value = "Indica se há pé varo (desvio do calcanhar para dentro).", example = "false")
    private Boolean peVaro;

    @ApiModelProperty(value = "Indica se há retração escapular.", example = "false")
    private Boolean retracaoEscapular;

    public AnamnesePosturalPosteriorDTO() {
    }

    public AnamnesePosturalPosteriorDTO(List<ItemAvaliacaoPostural> itemsAvaliacaoPostural) throws Exception{
        List<String> attributes = UtilReflection.getListAttributes(AnamnesePosturalPosteriorDTO.class);
        for(ItemAvaliacaoPostural i : itemsAvaliacaoPostural){
            if(attributes.contains(i.getItem().getField())){
                UtilReflection.setValor(this, i.getSelecionado(), i.getItem().getField());
            }
        }
    }

    public Boolean getDepressaoEscapular() {
        return depressaoEscapular;
    }

    public void setDepressaoEscapular(Boolean depressaoEscapular) {
        this.depressaoEscapular = depressaoEscapular;
    }

    public Boolean getEncurtamentoTrapezio() {
        return encurtamentoTrapezio;
    }

    public void setEncurtamentoTrapezio(Boolean encurtamentoTrapezio) {
        this.encurtamentoTrapezio = encurtamentoTrapezio;
    }

    public Boolean getEscolioseCervical() {
        return escolioseCervical;
    }

    public void setEscolioseCervical(Boolean escolioseCervical) {
        this.escolioseCervical = escolioseCervical;
    }

    public Boolean getEscolioseLombar() {
        return escolioseLombar;
    }

    public void setEscolioseLombar(Boolean escolioseLombar) {
        this.escolioseLombar = escolioseLombar;
    }

    public Boolean getEscolioseToracica() {
        return escolioseToracica;
    }

    public void setEscolioseToracica(Boolean escolioseToracica) {
        this.escolioseToracica = escolioseToracica;
    }

    public Boolean getProtacaoEscapular() {
        return protacaoEscapular;
    }

    public void setProtacaoEscapular(Boolean protacaoEscapular) {
        this.protacaoEscapular = protacaoEscapular;
    }

    public Boolean getPeValgo() {
        return peValgo;
    }

    public void setPeValgo(Boolean peValgo) {
        this.peValgo = peValgo;
    }

    public Boolean getPeVaro() {
        return peVaro;
    }

    public void setPeVaro(Boolean peVaro) {
        this.peVaro = peVaro;
    }

    public Boolean getRetracaoEscapular() {
        return retracaoEscapular;
    }

    public void setRetracaoEscapular(Boolean retracaoEscapular) {
        this.retracaoEscapular = retracaoEscapular;
    }
}
