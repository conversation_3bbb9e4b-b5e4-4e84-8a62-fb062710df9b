package br.com.pacto.bean.avaliacao;

import br.com.pacto.bean.anamnese.AnamneseTO;
import br.com.pacto.bean.anamnese.ResultadoMovimentoEnum;
import br.com.pacto.bean.anamnese.ResultadoVidaEnum;
import br.com.pacto.controller.json.avaliacao.AvaliadorDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados completos de uma avaliação integrada, incluindo resultados de mobilidade, estabilidade, qualidade de movimento e vida.")
public class AvaliacaoIntegradaDTO {

    @ApiModelProperty(value = "Identificador único da avaliação integrada.", example = "456")
    private Integer id;

    @ApiModelProperty(value = "Data de lançamento da avaliação integrada em formato timestamp (milissegundos).", example = "1717958400000")
    private Long dataLancamento;

    @ApiModelProperty(value = "Dados do profissional que realizou a avaliação.")
    private AvaliadorDTO avaliador;

    @ApiModelProperty(value = "Soma total dos pontos de qualidade de movimento calculados.", example = "14.5")
    private Double somaQualidadeMovimento;

    @ApiModelProperty(value = "Soma total dos pontos de qualidade de vida calculados.", example = "12")
    private Integer somaQualidadeVida;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Classificação da qualidade de vida baseada na soma dos pontos. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- FRACA (0-5 pontos)\n" +
            "- MEDIA (6-8 pontos)\n" +
            "- BOA (9-11 pontos)\n" +
            "- MUITO_BOA (12-13 pontos)\n" +
            "- EXCELENTE (14-15 pontos)", example = "MUITO_BOA")
    private ResultadoVidaEnum qualidadeVida;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Classificação da qualidade de movimento baseada na soma dos pontos. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- FRACA (0.0-6.0 pontos)\n" +
            "- MEDIA (6.1-10.0 pontos)\n" +
            "- BOA (10.1-14.0 pontos)\n" +
            "- MUITO_BOA (14.1-16.0 pontos)\n" +
            "- EXCELENTE (16.1-18.0 pontos)", example = "BOA")
    private ResultadoMovimentoEnum qualidadeMovimento;

    @ApiModelProperty(value = "Dados da ficha de anamnese utilizada na avaliação.")
    private AnamneseTO anamneseSelecionada;

    @ApiModelProperty(value = "Lista de respostas às perguntas da anamnese fornecidas pelo aluno.")
    private List<AnamnesePerguntaRespostaDTO> anamneseRespostas;

    @ApiModelProperty(value = "Lista de movimentos de mobilidade avaliados (cadeia anterior, posterior, lateral, rotacional).")
    private List<Movimento3DDTO> mobilidade;

    @ApiModelProperty(value = "Lista de movimentos de estabilidade avaliados (controle, fechamento, abertura).")
    private List<Movimento3DDTO> estabilidade;

    @ApiModelProperty(value = "Soma dos pontos de mobilidade do lado direito do corpo.", example = "28")
    private Integer somaMobilidadeDir;

    @ApiModelProperty(value = "Soma dos pontos de estabilidade do lado direito do corpo.", example = "24")
    private Integer somaEstabilidadeDir;

    @ApiModelProperty(value = "Média dos pontos de mobilidade do lado direito do corpo.", example = "7.0")
    private Double mediaMobilidadeDir;

    @ApiModelProperty(value = "Média dos pontos de estabilidade do lado direito do corpo.", example = "6.0")
    private Double mediaEstabilidadeDir;

    @ApiModelProperty(value = "Soma dos pontos de mobilidade do lado esquerdo do corpo.", example = "26")
    private Integer somaMobilidadeEsq;

    @ApiModelProperty(value = "Soma dos pontos de estabilidade do lado esquerdo do corpo.", example = "22")
    private Integer somaEstabilidadeEsq;

    @ApiModelProperty(value = "Média dos pontos de mobilidade do lado esquerdo do corpo.", example = "6.5")
    private Double mediaMobilidadeEsq;

    @ApiModelProperty(value = "Média dos pontos de estabilidade do lado esquerdo do corpo.", example = "5.5")
    private Double mediaEstabilidadeEsq;

    public AvaliacaoIntegradaDTO() { }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Long dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public AvaliadorDTO getAvaliador() {
        return avaliador;
    }

    public void setAvaliador(AvaliadorDTO avaliador) {
        this.avaliador = avaliador;
    }

    public Double getSomaQualidadeMovimento() {
        return somaQualidadeMovimento;
    }

    public void setSomaQualidadeMovimento(Double somaQualidadeMovimento) {
        this.somaQualidadeMovimento = somaQualidadeMovimento;
    }

    public Integer getSomaQualidadeVida() {
        return somaQualidadeVida;
    }

    public void setSomaQualidadeVida(Integer somaQualidadeVida) {
        this.somaQualidadeVida = somaQualidadeVida;
    }

    public ResultadoVidaEnum getQualidadeVida() {
        return qualidadeVida;
    }

    public void setQualidadeVida(ResultadoVidaEnum qualidadeVida) {
        this.qualidadeVida = qualidadeVida;
    }

    public ResultadoMovimentoEnum getQualidadeMovimento() {
        return qualidadeMovimento;
    }

    public void setQualidadeMovimento(ResultadoMovimentoEnum qualidadeMovimento) {
        this.qualidadeMovimento = qualidadeMovimento;
    }

    public AnamneseTO getAnamneseSelecionada() {
        return anamneseSelecionada;
    }

    public void setAnamneseSelecionada(AnamneseTO anamneseSelecionada) {
        this.anamneseSelecionada = anamneseSelecionada;
    }

    public List<AnamnesePerguntaRespostaDTO> getAnamneseRespostas() {
        return anamneseRespostas;
    }

    public void setAnamneseRespostas(List<AnamnesePerguntaRespostaDTO> anamneseRespostas) {
        this.anamneseRespostas = anamneseRespostas;
    }

    public List<Movimento3DDTO> getMobilidade() {
        return mobilidade;
    }

    public void setMobilidade(List<Movimento3DDTO> mobilidade) {
        this.mobilidade = mobilidade;
    }

    public List<Movimento3DDTO> getEstabilidade() {
        return estabilidade;
    }

    public void setEstabilidade(List<Movimento3DDTO> estabilidade) {
        this.estabilidade = estabilidade;
    }

    public Integer getSomaMobilidadeDir() {
        return somaMobilidadeDir;
    }

    public void setSomaMobilidadeDir(Integer somaMobilidadeDir) {
        this.somaMobilidadeDir = somaMobilidadeDir;
    }

    public Integer getSomaEstabilidadeDir() {
        return somaEstabilidadeDir;
    }

    public void setSomaEstabilidadeDir(Integer somaEstabilidadeDir) {
        this.somaEstabilidadeDir = somaEstabilidadeDir;
    }

    public Double getMediaMobilidadeDir() {
        return mediaMobilidadeDir;
    }

    public void setMediaMobilidadeDir(Double mediaMobilidadeDir) {
        this.mediaMobilidadeDir = mediaMobilidadeDir;
    }

    public Double getMediaEstabilidadeDir() {
        return mediaEstabilidadeDir;
    }

    public void setMediaEstabilidadeDir(Double mediaEstabilidadeDir) {
        this.mediaEstabilidadeDir = mediaEstabilidadeDir;
    }

    public Integer getSomaMobilidadeEsq() {
        return somaMobilidadeEsq;
    }

    public void setSomaMobilidadeEsq(Integer somaMobilidadeEsq) {
        this.somaMobilidadeEsq = somaMobilidadeEsq;
    }

    public Integer getSomaEstabilidadeEsq() {
        return somaEstabilidadeEsq;
    }

    public void setSomaEstabilidadeEsq(Integer somaEstabilidadeEsq) {
        this.somaEstabilidadeEsq = somaEstabilidadeEsq;
    }

    public Double getMediaMobilidadeEsq() {
        return mediaMobilidadeEsq;
    }

    public void setMediaMobilidadeEsq(Double mediaMobilidadeEsq) {
        this.mediaMobilidadeEsq = mediaMobilidadeEsq;
    }

    public Double getMediaEstabilidadeEsq() {
        return mediaEstabilidadeEsq;
    }

    public void setMediaEstabilidadeEsq(Double mediaEstabilidadeEsq) {
        this.mediaEstabilidadeEsq = mediaEstabilidadeEsq;
    }

}
