package br.com.pacto.bean.avaliacao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de peso, altura e sinais vitais coletados na avaliação física.")
public class AvaliacaoPesoAlturaDTO {

    @ApiModelProperty(value = "Peso do aluno em quilogramas (kg).", example = "72.5")
    private Double peso;

    @ApiModelProperty(value = "Altura do aluno em metros (m).", example = "1.75")
    private Double altura;

    @ApiModelProperty(value = "Pressão arterial do aluno.", example = "120/80")
    private String pressaoArterial;

    @ApiModelProperty(value = "Pressão diastólica (mínima) do aluno em mmHg.", example = "80")
    private String pressaoDiastolica;

    @ApiModelProperty(value = "Pressão sistólica (máxima) do aluno em mmHg.", example = "120")
    private String pressaoSistolica;

    @ApiModelProperty(value = "Frequência cardíaca de repouso do aluno em bpm.", example = "68")
    private Integer frequencia;

    @ApiModelProperty(value = "Frequência cardíaca máxima estimada do aluno em bpm.", example = "190.0")
    private Double frequenciaMax;
    public AvaliacaoPesoAlturaDTO() {
    }

    public AvaliacaoPesoAlturaDTO(AvaliacaoFisica avaliacaoFisica,
                                  String pressaoArterial,
                                  String pressaoSistolica,
                                  String pressaoDiastolica,
                                  Integer fc) {
        this.peso = avaliacaoFisica.getPeso();
        this.altura = avaliacaoFisica.getAltura();
        this.pressaoArterial = pressaoArterial;
        this.pressaoSistolica = pressaoSistolica;
        this.pressaoDiastolica = pressaoDiastolica;
        this.frequencia = fc;
        this.frequenciaMax = avaliacaoFisica.getFcMaxima();
    }

    public Double getPeso() {
        return peso;
    }

    public void setPeso(Double peso) {
        this.peso = peso;
    }

    public Double getAltura() {
        return altura;
    }

    public void setAltura(Double altura) {
        this.altura = altura;
    }

    public String getPressaoArterial() {
        return pressaoArterial;
    }

    public void setPressaoArterial(String pressaoArterial) {
        this.pressaoArterial = pressaoArterial;
    }

    public String getPressaoDiastolica() {
        return pressaoDiastolica;
    }

    public void setPressaoDiastolica(String pressaoDiastolica) {
        this.pressaoDiastolica = pressaoDiastolica;
    }

    public String getPressaoSistolica() {
        return pressaoSistolica;
    }

    public void setPressaoSistolica(String pressaoSistolica) {
        this.pressaoSistolica = pressaoSistolica;
    }

    public Integer getFrequencia() {
        if (frequencia == null) return 0;
        return frequencia;
    }

    public void setFrequencia(Integer frequencia) {
        this.frequencia = frequencia;
    }

    public Double getFrequenciaMax() {
        return frequenciaMax;
    }

    public void setFrequenciaMax(Double frequenciaMax) {
        this.frequenciaMax = frequenciaMax;
    }
}
