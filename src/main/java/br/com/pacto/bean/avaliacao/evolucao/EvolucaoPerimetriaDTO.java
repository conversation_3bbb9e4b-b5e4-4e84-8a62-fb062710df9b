package br.com.pacto.bean.avaliacao.evolucao;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR> Siqueira 30/01/2019
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados completos de evolução das medidas de perimetria (circunferências corporais) do aluno")
public class EvolucaoPerimetriaDTO implements Serializable {

    @ApiModelProperty(value = "Evolução da medida do pescoço")
    private EvolucaoPerimetriaItemDTO pescoco;

    @ApiModelProperty(value = "Evolução da medida do ombro")
    private EvolucaoPerimetriaItemDTO ombro;

    @ApiModelProperty(value = "Evolução da medida do braço esquerdo")
    private EvolucaoPerimetriaItemDTO braco_esquerdo;

    @ApiModelProperty(value = "Evolução da medida do braço direito")
    private EvolucaoPerimetriaItemDTO braco_direito;

    @ApiModelProperty(value = "Evolução da medida do antebraço direito")
    private EvolucaoPerimetriaItemDTO antebraco_direito;

    @ApiModelProperty(value = "Evolução da medida do antebraço esquerdo")
    private EvolucaoPerimetriaItemDTO antebraco_esquerdo;

    @ApiModelProperty(value = "Evolução da medida do tórax/busto")
    private EvolucaoPerimetriaItemDTO toraxBusto;

    @ApiModelProperty(value = "Evolução da medida da cintura")
    private EvolucaoPerimetriaItemDTO cintura;

    @ApiModelProperty(value = "Evolução da medida da circunferência abdominal")
    private EvolucaoPerimetriaItemDTO circunferenciaAbdominal;

    @ApiModelProperty(value = "Evolução da medida do quadril")
    private EvolucaoPerimetriaItemDTO quadril;

    @ApiModelProperty(value = "Evolução da medida do glúteo")
    private EvolucaoPerimetriaItemDTO gluteo;

    @ApiModelProperty(value = "Evolução da medida da coxa proximal direita")
    private EvolucaoPerimetriaItemDTO coxa_proximal_direita;

    @ApiModelProperty(value = "Evolução da medida da coxa medial direita")
    private EvolucaoPerimetriaItemDTO coxa_medial_direita;

    @ApiModelProperty(value = "Evolução da medida da coxa distal direita")
    private EvolucaoPerimetriaItemDTO coxa_distal_direita;

    @ApiModelProperty(value = "Evolução da medida da coxa proximal esquerda")
    private EvolucaoPerimetriaItemDTO coxa_proximal_esquerda;

    @ApiModelProperty(value = "Evolução da medida da coxa medial esquerda")
    private EvolucaoPerimetriaItemDTO coxa_medial_esquerda;

    @ApiModelProperty(value = "Evolução da medida da coxa distal esquerda")
    private EvolucaoPerimetriaItemDTO coxa_distal_esquerda;

    @ApiModelProperty(value = "Evolução da medida da panturrilha direita")
    private EvolucaoPerimetriaItemDTO panturrilha_direita;

    @ApiModelProperty(value = "Evolução da medida da panturrilha esquerda")
    private EvolucaoPerimetriaItemDTO panturrilha_esquerda;

    public EvolucaoPerimetriaItemDTO getPescoco() {
        return pescoco;
    }

    public void setPescoco(EvolucaoPerimetriaItemDTO pescoco) {
        this.pescoco = pescoco;
    }

    public EvolucaoPerimetriaItemDTO getOmbro() {
        return ombro;
    }

    public void setOmbro(EvolucaoPerimetriaItemDTO ombro) {
        this.ombro = ombro;
    }

    public EvolucaoPerimetriaItemDTO getBraco_esquerdo() {
        return braco_esquerdo;
    }

    public void setBraco_esquerdo(EvolucaoPerimetriaItemDTO braco_esquerdo) {
        this.braco_esquerdo = braco_esquerdo;
    }

    public EvolucaoPerimetriaItemDTO getBraco_direito() {
        return braco_direito;
    }

    public void setBraco_direito(EvolucaoPerimetriaItemDTO braco_direito) {
        this.braco_direito = braco_direito;
    }

    public EvolucaoPerimetriaItemDTO getAntebraco_direito() {
        return antebraco_direito;
    }

    public void setAntebraco_direito(EvolucaoPerimetriaItemDTO antebraco_direito) {
        this.antebraco_direito = antebraco_direito;
    }

    public EvolucaoPerimetriaItemDTO getAntebraco_esquerdo() {
        return antebraco_esquerdo;
    }

    public void setAntebraco_esquerdo(EvolucaoPerimetriaItemDTO antebraco_esquerdo) {
        this.antebraco_esquerdo = antebraco_esquerdo;
    }

    public EvolucaoPerimetriaItemDTO getToraxBusto() {
        return toraxBusto;
    }

    public void setToraxBusto(EvolucaoPerimetriaItemDTO toraxBusto) {
        this.toraxBusto = toraxBusto;
    }

    public EvolucaoPerimetriaItemDTO getCintura() {
        return cintura;
    }

    public void setCintura(EvolucaoPerimetriaItemDTO cintura) {
        this.cintura = cintura;
    }

    public EvolucaoPerimetriaItemDTO getCircunferenciaAbdominal() {
        return circunferenciaAbdominal;
    }

    public void setCircunferenciaAbdominal(EvolucaoPerimetriaItemDTO circunferenciaAbdominal) {
        this.circunferenciaAbdominal = circunferenciaAbdominal;
    }

    public EvolucaoPerimetriaItemDTO getQuadril() {
        return quadril;
    }

    public void setQuadril(EvolucaoPerimetriaItemDTO quadril) {
        this.quadril = quadril;
    }

    public EvolucaoPerimetriaItemDTO getGluteo() {
        return gluteo;
    }

    public void setGluteo(EvolucaoPerimetriaItemDTO gluteo) {
        this.gluteo = gluteo;
    }

    public EvolucaoPerimetriaItemDTO getCoxa_proximal_direita() {
        return coxa_proximal_direita;
    }

    public void setCoxa_proximal_direita(EvolucaoPerimetriaItemDTO coxa_proximal_direita) {
        this.coxa_proximal_direita = coxa_proximal_direita;
    }

    public EvolucaoPerimetriaItemDTO getCoxa_medial_direita() {
        return coxa_medial_direita;
    }

    public void setCoxa_medial_direita(EvolucaoPerimetriaItemDTO coxa_medial_direita) {
        this.coxa_medial_direita = coxa_medial_direita;
    }

    public EvolucaoPerimetriaItemDTO getCoxa_distal_direita() {
        return coxa_distal_direita;
    }

    public void setCoxa_distal_direita(EvolucaoPerimetriaItemDTO coxa_distal_direita) {
        this.coxa_distal_direita = coxa_distal_direita;
    }

    public EvolucaoPerimetriaItemDTO getCoxa_proximal_esquerda() {
        return coxa_proximal_esquerda;
    }

    public void setCoxa_proximal_esquerda(EvolucaoPerimetriaItemDTO coxa_proximal_esquerda) {
        this.coxa_proximal_esquerda = coxa_proximal_esquerda;
    }

    public EvolucaoPerimetriaItemDTO getCoxa_medial_esquerda() {
        return coxa_medial_esquerda;
    }

    public void setCoxa_medial_esquerda(EvolucaoPerimetriaItemDTO coxa_medial_esquerda) {
        this.coxa_medial_esquerda = coxa_medial_esquerda;
    }

    public EvolucaoPerimetriaItemDTO getCoxa_distal_esquerda() {
        return coxa_distal_esquerda;
    }

    public void setCoxa_distal_esquerda(EvolucaoPerimetriaItemDTO coxa_distal_esquerda) {
        this.coxa_distal_esquerda = coxa_distal_esquerda;
    }

    public EvolucaoPerimetriaItemDTO getPanturrilha_direita() {
        return panturrilha_direita;
    }

    public void setPanturrilha_direita(EvolucaoPerimetriaItemDTO panturrilha_direita) {
        this.panturrilha_direita = panturrilha_direita;
    }

    public EvolucaoPerimetriaItemDTO getPanturrilha_esquerda() {
        return panturrilha_esquerda;
    }

    public void setPanturrilha_esquerda(EvolucaoPerimetriaItemDTO panturrilha_esquerda) {
        this.panturrilha_esquerda = panturrilha_esquerda;
    }
}
