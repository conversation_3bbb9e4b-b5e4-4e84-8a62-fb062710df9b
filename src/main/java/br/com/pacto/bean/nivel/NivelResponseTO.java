package br.com.pacto.bean.nivel;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 16/08/2018.
 */
@ApiModel(description = "Objeto de resposta contendo dados de nível de aluno")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NivelResponseTO {

    @ApiModelProperty(value = "Código identificador único do nível", example = "2")
    private Integer id;

    @ApiModelProperty(value = "Nome descritivo do nível", example = "Intermediário")
    private String nome;

    @ApiModelProperty(value = "Ordem de classificação do nível (usado para ordenação)", example = "2")
    private Integer ordem;

    @ApiModelProperty(value = "Indica se o nível está ativo no sistema", example = "true")
    private Boolean ativo;

    public NivelResponseTO(Nivel nivel) {
        this.id = nivel.getCodigo();
        this.nome = nivel.getNome();
        this.ordem = nivel.getOrdem();
        this.ativo = nivel.getAtivo();
    }

    public NivelResponseTO(Integer id, String nome) {
        this.id = id;
        this.nome = nome;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
}
