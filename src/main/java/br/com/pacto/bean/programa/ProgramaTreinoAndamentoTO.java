package br.com.pacto.bean.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar o andamento de um programa de treino, contendo informações sobre o progresso do aluno.")
public class ProgramaTreinoAndamentoTO {

    @ApiModelProperty(value = "Nome do aluno que está executando o programa", example = "Carlos Silva")
    private String aluno;

    @ApiModelProperty(value = "Nome do programa de treino", example = "Programa Hipertrofia")
    private String nome;

    @ApiModelProperty(value = "Execuções realizadas no formato 'realizadas/previstas'", example = "8/12")
    private String execucoes;

    @ApiModelProperty(value = "Porcentagem de conclusão do programa", example = "66.67")
    private double porcentagem;

    public ProgramaTreinoAndamentoTO(String nomePrograma, String nomeCliente, Integer nrTreinosRealizados, Integer nrTreinosPrevistos, double porcentagem) {
        this.nome = nomePrograma;
        this.aluno = nomeCliente;
        this.execucoes = nrTreinosRealizados + "/" + nrTreinosPrevistos;
        this.porcentagem = porcentagem;
    }

    public String getAluno() {
        return aluno;
    }

    public void setAluno(String aluno) {
        this.aluno = aluno;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getExecucoes() {
        return execucoes;
    }

    public void setExecucoes(String execucoes) {
        this.execucoes = execucoes;
    }

    public double getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(double porcentagem) {
        this.porcentagem = porcentagem;
    }
}
