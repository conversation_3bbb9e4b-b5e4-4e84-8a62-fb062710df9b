package br.com.pacto.bean.programa;

import br.com.pacto.bean.bi.DashboardBI;
import br.com.pacto.bean.professor.ProfessorSintetico;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.gestao.BITreinoTreinamentoDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by ulisses on 13/08/2018.
 */
@ApiModel(description = "DTO para representar dados de Business Intelligence de um professor, incluindo informações básicas e estatísticas de treinamento")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfessorBIResponseTO {

    @ApiModelProperty(value = "Dados básicos do professor. Referência à classe ProfessorResponseTO com informações completas do professor")
    private ProfessorResponseTO professor;

    @ApiModelProperty(value = "Dados de Business Intelligence relacionados aos treinamentos do professor. Referência à classe BITreinoTreinamentoDTO com estatísticas e métricas")
    private BITreinoTreinamentoDTO biTreinoTreinamentoDTO;

    public ProfessorBIResponseTO() {
    }

    public ProfessorResponseTO getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorResponseTO professor) {
        this.professor = professor;
    }

    public BITreinoTreinamentoDTO getBiTreinoTreinamentoDTO() {
        return biTreinoTreinamentoDTO;
    }

    public void setBiTreinoTreinamentoDTO(BITreinoTreinamentoDTO biTreinoTreinamentoDTO) {
        this.biTreinoTreinamentoDTO = biTreinoTreinamentoDTO;
    }
}
