package br.com.pacto.bean.programa;

import br.com.pacto.bean.usuario.StatusObjetivoAlunoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para criação ou alteração de um objetivo intermediário do aluno")
public class ObjetivoIntermediarioAlunoDTO {

    @ApiModelProperty(value = "Código identificador do objetivo predefinido que será associado ao objetivo intermediário", example = "3")
    private Integer objetivoPredefinido;

    @ApiModelProperty(value = "Código identificador do objetivo principal do aluno ao qual este objetivo intermediário está vinculado", example = "123")
    private Integer objetivoAluno;

    @ApiModelProperty(value = "Data de início do objetivo intermediário no formato dd/MM/yyyy", example = "15/01/2024")
    private String dataInicio;

    @ApiModelProperty(value = "Data final prevista para alcançar o objetivo intermediário no formato dd/MM/yyyy", example = "15/02/2024")
    private String dataFinal;

    @ApiModelProperty(value = "Descrição específica do objetivo intermediário", example = "Aumentar carga no supino em 10kg")
    private String descricao;

    @ApiModelProperty(value = "Descrição do objetivo final relacionado", example = "Alcançar 100kg no supino")
    private String objetivoFinal;

    @ApiModelProperty(value = "Categoria do objetivo intermediário", example = "Força")
    private String categoria;

    @ApiModelProperty(value = "Status atual de alcance do objetivo intermediário. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NAO_ALCANCOU (0 - Não Alcançou)\n" +
            "- ALCANCOU_PARCIALMENTE (1 - Alcançou Parcialmente)\n" +
            "- ALCANCOU (2 - Alcançou)\n", example = "NAO_ALCANCOU")
    private StatusObjetivoAlunoEnum status;

    public Integer getObjetivoPredefinido() {
        return objetivoPredefinido;
    }

    public void setObjetivoPredefinido(Integer objetivoPredefinido) {
        this.objetivoPredefinido = objetivoPredefinido;
    }

    public Integer getObjetivoAluno() {
        return objetivoAluno;
    }

    public void setObjetivoAluno(Integer objetivoAluno) {
        this.objetivoAluno = objetivoAluno;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getObjetivoFinal() {
        return objetivoFinal;
    }

    public void setObjetivoFinal(String objetivoFinal) {
        this.objetivoFinal = objetivoFinal;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public StatusObjetivoAlunoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusObjetivoAlunoEnum status) {
        this.status = status;
    }
}
