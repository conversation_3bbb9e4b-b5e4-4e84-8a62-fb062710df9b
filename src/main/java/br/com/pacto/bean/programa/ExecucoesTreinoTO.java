package br.com.pacto.bean.programa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar as execuções de treino de um aluno, contendo informações sobre o aluno, professor e quantidade de execuções.")
public class ExecucoesTreinoTO {

    @ApiModelProperty(value = "Matrícula do aluno", example = "12345")
    private String matricula;

    @ApiModelProperty(value = "Nome do aluno", example = "Ana Costa")
    private String aluno;

    @ApiModelProperty(value = "Nome do professor responsável", example = "<PERSON>")
    private String professor;

    @ApiModelProperty(value = "Quantidade de execuções realizadas", example = "15")
    private String execucoes;

    public ExecucoesTreinoTO() { }

    public ExecucoesTreinoTO(String matricula, String aluno, String professor, String execucoes) {
        this.matricula = matricula;
        this.aluno = aluno;
        this.professor = professor;
        this.execucoes = execucoes;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getAluno() {
        return aluno;
    }

    public void setAluno(String aluno) {
        this.aluno = aluno;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getExecucoes() {
        return execucoes;
    }

    public void setExecucoes(String execucoes) {
        this.execucoes = execucoes;
    }

}
