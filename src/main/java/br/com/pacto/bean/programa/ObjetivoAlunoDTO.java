package br.com.pacto.bean.programa;

import br.com.pacto.bean.usuario.StatusObjetivoAlunoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados para criação ou alteração de um objetivo do aluno")
public class ObjetivoAlunoDTO {

    @ApiModelProperty(value = "Código identificador do objetivo predefinido que será associado ao aluno", example = "1")
    private Integer objetivoPredefinido;

    @ApiModelProperty(value = "Indica se este é o objetivo principal do aluno", example = "true")
    private Boolean primario;

    @ApiModelProperty(value = "Data de início do objetivo no formato dd/MM/yyyy", example = "01/01/2024")
    private String dataInicio;

    @ApiModelProperty(value = "Data final prevista para alcançar o objetivo no formato dd/MM/yyyy", example = "31/03/2024")
    private String dataFinal;

    @ApiModelProperty(value = "Descrição personalizada do objetivo do aluno", example = "Perder 5kg em 3 meses através de treino funcional e dieta balanceada")
    private String descricao;

    @ApiModelProperty(value = "Status atual de alcance do objetivo. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NAO_ALCANCOU (0 - Não Alcançou)\n" +
            "- ALCANCOU_PARCIALMENTE (1 - Alcançou Parcialmente)\n" +
            "- ALCANCOU (2 - Alcançou)\n", example = "NAO_ALCANCOU")
    private StatusObjetivoAlunoEnum status;

    public Integer getObjetivoPredefinido() {
        return objetivoPredefinido;
    }

    public void setObjetivoPredefinido(Integer objetivoPredefinido) {
        this.objetivoPredefinido = objetivoPredefinido;
    }

    public Boolean getPrimario() {
        return primario;
    }

    public void setPrimario(Boolean primario) {
        this.primario = primario;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public StatusObjetivoAlunoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusObjetivoAlunoEnum status) {
        this.status = status;
    }
}
