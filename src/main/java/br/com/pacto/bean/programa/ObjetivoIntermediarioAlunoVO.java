package br.com.pacto.bean.programa;

import br.com.pacto.bean.usuario.StatusObjetivoAlunoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Dados de um objetivo intermediário do aluno")
public class ObjetivoIntermediarioAlunoVO {

    @ApiModelProperty(value = "Código identificador único do objetivo intermediário", example = "456")
    private Integer codigo;

    @ApiModelProperty(value = "Nome do objetivo predefinido associado", example = "Ganho de Massa Muscular")
    private String objetivoPredefinido;

    @ApiModelProperty(value = "Data de início do objetivo intermediário no formato dd/MM/yyyy", example = "15/01/2024")
    private String dataInicio;

    @ApiModelProperty(value = "Data final prevista para o objetivo intermediário no formato dd/MM/yyyy", example = "15/02/2024")
    private String dataFinal;

    @ApiModelProperty(value = "Descrição específica do objetivo intermediário", example = "Aumentar carga no supino em 10kg")
    private String descricao;

    @ApiModelProperty(value = "Status atual de alcance do objetivo intermediário. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- NAO_ALCANCOU (0 - Não Alcançou)\n" +
            "- ALCANCOU_PARCIALMENTE (1 - Alcançou Parcialmente)\n" +
            "- ALCANCOU (2 - Alcançou)\n", example = "ALCANCOU_PARCIALMENTE")
    private StatusObjetivoAlunoEnum status;

    @ApiModelProperty(value = "Categoria do objetivo intermediário", example = "Força")
    private String categoria;

    @ApiModelProperty(value = "Descrição do objetivo final relacionado", example = "Alcançar 100kg no supino")
    private String objetivoFinal;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getObjetivoPredefinido() {
        return objetivoPredefinido;
    }

    public void setObjetivoPredefinido(String objetivoPredefinido) {
        this.objetivoPredefinido = objetivoPredefinido;
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(String dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public StatusObjetivoAlunoEnum getStatus() {
        return status;
    }

    public void setStatus(StatusObjetivoAlunoEnum status) {
        this.status = status;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getObjetivoFinal() {
        return objetivoFinal;
    }

    public void setObjetivoFinal(String objetivoFinal) {
        this.objetivoFinal = objetivoFinal;
    }
}
