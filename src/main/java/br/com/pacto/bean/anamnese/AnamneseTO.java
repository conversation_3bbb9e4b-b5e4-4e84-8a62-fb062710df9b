package br.com.pacto.bean.anamnese;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 04/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações de uma ficha de anamnese")
public class AnamneseTO {
    @ApiModelProperty(value = "Código único identificador da ficha de anamnese. (Não é necessário informando quando for cadastrar ou atualizar uma ficha)", example = "3")
    private Integer id;
    @ApiModelProperty(value = "Nome da ficha de anamnese", example = "Anamnese Sênior")
    private String nome;
    @ApiModelProperty(value = "Indica se a ficha está ativa no sistema", example = "true", allowableValues = "true,false")
    private boolean ativa;
    @ApiModelProperty(value = "Lista de perguntas referentes a ficha de anamnese")
    private List<PerguntaTO> perguntas = new ArrayList<PerguntaTO>();
    @ApiModelProperty(value = "Código do tipo da ficha de anamnese. <br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - PADRAO</li>" +
            "<li>1 - AVALIACAO_INTEGRADA</li>" +
            "</ul>", example = "0")
    private TipoAnamneseEnum tipo;
    @ApiModelProperty(value = "Data de criação da ficha de anamnese", example = "20250606")
    private Date datalancamento;

    public AnamneseTO() {

    }

    public AnamneseTO(Anamnese anamnese) {
        this.id = anamnese.getCodigo();
        this.nome = anamnese.getDescricao();
        this.ativa = anamnese.getAtiva();
        this.perguntas = new ArrayList<PerguntaTO>();
        for (PerguntaAnamnese p : anamnese.getPerguntas()) {
            perguntas.add(new PerguntaTO(p.getPergunta()));
        }
        this.tipo = anamnese.getTipo();
    }

    public AnamneseTO(Integer id, String nome) {
        this.id = id;
        this.nome = nome;

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public boolean isAtiva() {
        return ativa;
    }

    public void setAtiva(boolean ativa) {
        this.ativa = ativa;
    }

    public List<PerguntaTO> getPerguntas() {
        return perguntas;
    }

    public void setPerguntas(List<PerguntaTO> perguntas) {
        this.perguntas = perguntas;
    }

    public TipoAnamneseEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAnamneseEnum tipo) {
        this.tipo = tipo;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }
}
