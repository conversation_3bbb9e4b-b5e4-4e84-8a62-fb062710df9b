package br.com.pacto.bean.anamnese;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import br.com.pacto.util.impl.UtilReflection;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static java.util.Objects.isNull;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa uma ficha de anamnese utilizada nas avaliações físicas, contendo perguntas sobre histórico de saúde, qualidade de vida e atividade física do cliente.")
public class Anamnese {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único da anamnese no banco de dados.", example = "456")
    private Integer codigo;

    @ApiModelProperty(value = "Indica se a anamnese está ativa e disponível para uso.", example = "true")
    private Boolean ativa = Boolean.TRUE;

    @ApiModelProperty(value = "Indica se esta anamnese é do tipo PAR-Q (Physical Activity Readiness Questionnaire).", example = "false")
    private Boolean parq = Boolean.FALSE;

    @Column(columnDefinition = "boolean default false")
    @ApiModelProperty(value = "Indica se utiliza o padrão PAR-Q do Rio de Janeiro.", example = "false")
    private Boolean parqpadraorj;

    @ApiModelProperty(value = "Descrição ou nome da ficha de anamnese.", example = "Anamnese Padrão - Avaliação Integrada")
    private String descricao;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Tipo da anamnese. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- <strong>PADRAO</strong> (Anamnese Padrão)\n" +
            "- <strong>AVALIACAO_INTEGRADA</strong> (Anamnese para Avaliação Integrada)", example = "AVALIACAO_INTEGRADA")
    private TipoAnamneseEnum tipo = TipoAnamneseEnum.PADRAO;

    @ApiModelProperty(value = "Código do usuário responsável pela criação da anamnese.", example = "123")
    private Integer responsavelLancamento;

    @Temporal(TemporalType.TIMESTAMP)
    @ApiModelProperty(value = "Data e hora de criação da anamnese.", example = "2024-06-18T10:00:00Z")
    private Date dataLancamento;

    @Transient
    @ApiModelProperty(value = "Data e hora em que a anamnese foi respondida pelo cliente (campo transiente).", example = "2024-06-18T14:30:00Z")
    private Date respondidaEm;

    @Transient
    @ApiModelProperty(value = "Lista de perguntas que compõem esta anamnese (campo transiente carregado conforme necessário).")
    private List<PerguntaAnamnese> perguntas;

    public Anamnese() {
    }

    public Anamnese(Anamnese a) {
        this.ativa = a.ativa;
        this.codigo = a.codigo;
        this.parq = a.parq;
        this.descricao = a.descricao;
        this.tipo = a.tipo;
        this.responsavelLancamento = a.responsavelLancamento;
        this.dataLancamento = a.dataLancamento;
        this.respondidaEm = a.respondidaEm;
        this.perguntas = a.perguntas;
        this.parqpadraorj = a.parqpadraorj;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getAtiva() {
        return ativa;
    }

    public void setAtiva(Boolean ativa) {
        this.ativa = ativa;
    }

    public String getDescricao() {
        return descricao;
    }

    public Boolean getParq() {
        return parq;
    }

    public void setParq(Boolean parq) {
        this.parq = parq;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(Integer responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public List<PerguntaAnamnese> getPerguntas() {
        return perguntas;
    }

    public void setPerguntas(List<PerguntaAnamnese> perguntas) {
        this.perguntas = perguntas;
    }

    public TipoAnamneseEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoAnamneseEnum tipo) {
        this.tipo = tipo;
    }

    public Date getRespondidaEm() {
        return respondidaEm;
    }

    public void setRespondidaEm(Date respondidaEm) {
        this.respondidaEm = respondidaEm;
    }

    public String getDescricaoParaLog(Anamnese a2) {
        try {
            StringBuilder log = new StringBuilder();
            log.append(UtilReflection.difference(this, a2));

            if (isNull(a2)) {
                log.append(ComparePerguntas(this.getPerguntas(), null));
                return log.toString();
            }
            log.append(ComparePerguntas(this.getPerguntas(), a2.getPerguntas()));
            return log.toString();

        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO gerar log";
        }
    }

    private String ComparePerguntas(List<PerguntaAnamnese> listaPerguntas1, List<PerguntaAnamnese> listaPerguntas2) {

        StringBuilder log = new StringBuilder();

        int tamanhoLista1 = listaPerguntas1 != null ? listaPerguntas1.size() : 0;
        int tamanhoLista2 = listaPerguntas2 != null ? listaPerguntas2.size() : 0;
        int tamanhoMaximo = Math.max(tamanhoLista1, tamanhoLista2);

        for (int contador = 0; contador < tamanhoMaximo; contador++) {
            Pergunta pergunta1 = contador < tamanhoLista1 ? listaPerguntas1.get(contador).getPergunta() : null;
            Pergunta pergunta2 = contador < tamanhoLista2 ? listaPerguntas2.get(contador).getPergunta() : null;

            List<String> respostas1 = contador < tamanhoLista1 ? listaPerguntas1.get(contador).getRespostas() : null;
            List<String> respostas2 = contador < tamanhoLista2 ? listaPerguntas2.get(contador).getRespostas() : null;

            log.append(UtilReflection.difference(pergunta1, pergunta2));
            log.append(CompareRespostas(respostas1, respostas2));
            log.append(
                    compareOpcoes(
                            pergunta1 == null ? null : pergunta1.getOpcoes(),
                            pergunta2 == null ? null : pergunta2.getOpcoes()
                    )
            );
        }
        return log.toString();
    }
    private String CompareRespostas(List<String> r1, List<String> r2) {

        StringBuilder log = new StringBuilder();

        int tamanhoLista1 = r1 != null ? r1.size() : 0;
        int tamanhoLista2 = r2 != null ? r2.size() : 0;

        int tamanhoMaximo = Math.max(tamanhoLista1, tamanhoLista2);

        for (int contador = 0; contador < tamanhoMaximo; contador++) {
            String elementoLista1 = contador < tamanhoLista1 ? r1.get(contador) : null;
            String elementoLista2 = contador < tamanhoLista2 ? r2.get(contador) : null;

            log.append(UtilReflection.difference(elementoLista1, elementoLista2));
        }

        return log.toString();

    }

    private String compareOpcoes(List<OpcaoPergunta> opcoes1, List<OpcaoPergunta> opcoes2) {
        StringBuilder log = new StringBuilder();

        int tamanhoLista1 = opcoes1 != null ? opcoes1.size() : 0;
        int tamanhoLista2 = opcoes2 != null ? opcoes2.size() : 0;
        int tamanhoMaximo = Math.max(tamanhoLista1, tamanhoLista2);

        for (int contador = 0; contador < tamanhoMaximo; contador++) {
            OpcaoPergunta elementoLista1 = contador < tamanhoLista1 ? opcoes1.get(contador) : null;
            OpcaoPergunta elementoLista2 = contador < tamanhoLista2 ? opcoes2.get(contador) : null;
            log.append(UtilReflection.difference(elementoLista1, elementoLista2));
        }

        return log.toString();
    }

    public Boolean getParqpadraorj() { return parqpadraorj; }

    public void setParqpadraorj(Boolean parqpadraorj) { this.parqpadraorj = parqpadraorj; }
}


