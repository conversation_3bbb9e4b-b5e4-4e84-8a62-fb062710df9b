package br.com.pacto.bean.anamnese;

import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 05/08/2018.
 */
@ApiModel(description = "Pergunta do questionário PAR-Q para avaliação de prontidão para atividade física")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PerguntaResponseTO {

    @ApiModelProperty(value = "Identificador único da pergunta na anamnese", example = "1")
    private Integer id;

    @ApiModelProperty(value = "Texto da pergunta do questionário PAR-Q", example = "Algum médico já disse que você possui um problema do coração e que só deveria realizar atividade física supervisionada por um profissional de saúde?")
    private String pergunta;

    @ApiModelProperty(value = "Ordem de apresentação da pergunta no questionário", example = "1")
    private Integer ordem;
    @ApiModelProperty(value = "Tipo da resposta da pergunta. <br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - ESCOLHA_MULTIPLA (Escolha múltipla)</li>" +
            "<li>1 - ESCOLHA_UNICA (Escolha Única)</li>" +
            "<li>2 - SIM_NAO (Sim ou não)</li>" +
            "<li>3 - TEXTO (Texto)</li>" +
            "</ul>", example = "SIM_NAO", allowableValues = "ESCOLHA_MULTIPLA,ESCOLHA_UNICA,SIM_NAO,TEXTO")
    private TipoPerguntaDTOEnum tipo;

    @ApiModelProperty(value = "Lista de opções de resposta disponíveis para a pergunta")
    private List<OpcaoPerguntaResponseTO> opcoes = new ArrayList<>();

    @ApiModelProperty(value = "Indica se deve apresentar informações sobre a lei do PAR-Q para esta pergunta", example = "false")
    private Boolean apresentarLeiParq;

    public PerguntaResponseTO(Pergunta pergunta, Integer perguntaAnamneseId) {
        this.id = perguntaAnamneseId;
        this.pergunta = pergunta.getDescricao();
        this.ordem = pergunta.getOrdem();
        this.tipo = pergunta.getTipoPergunta().getTipoPerguntaDTOEnum();
        this.opcoes.clear();
        for (OpcaoPergunta opcao: pergunta.getOpcoes()) {
            this.opcoes.add(new OpcaoPerguntaResponseTO(opcao));
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPergunta() {
        return pergunta;
    }

    public void setPergunta(String pergunta) {
        this.pergunta = pergunta;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public TipoPerguntaDTOEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoPerguntaDTOEnum tipo) {
        this.tipo = tipo;
    }

    public List<OpcaoPerguntaResponseTO> getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(List<OpcaoPerguntaResponseTO> opcoes) {
        this.opcoes = opcoes;
    }

    public Boolean getApresentarLeiParq() {
        return apresentarLeiParq;
    }

    public void setApresentarLeiParq(Boolean apresentarLeiParq) {
        this.apresentarLeiParq = apresentarLeiParq;
    }
}