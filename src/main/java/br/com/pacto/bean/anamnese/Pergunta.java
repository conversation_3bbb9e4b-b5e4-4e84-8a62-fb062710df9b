package br.com.pacto.bean.anamnese;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import br.com.pacto.enumerador.anamnese.TiposPerguntaEnum;

import javax.faces.model.SelectItem;
import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON>ao Alcides on 04/05/2017.
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa uma pergunta utilizada nas anamneses, incluindo seu tipo, opções de resposta e agrupamento para avaliações integradas.")
public class Pergunta {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único da pergunta no banco de dados.", example = "456")
    protected Integer codigo;

    @ApiModelProperty(value = "Ordem de exibição da pergunta na anamnese.", example = "1")
    protected Integer ordem;

    @ApiModelProperty(value = "Texto da pergunta apresentada ao cliente.", example = "Você pratica atividade física regularmente?")
    protected String descricao;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Agrupamento da pergunta para avaliações integradas. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- <strong>GERAL</strong> (Perguntas Gerais)\n" +
            "- <strong>QUALIDADE_VIDA</strong> (Qualidade de Vida)\n" +
            "- <strong>QUALIDADE_MOVIMENTO</strong> (Qualidade de Movimento)", example = "QUALIDADE_VIDA")
    private AgrupamentoAvaliacaoIntegradaEnum agrupamento = null;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Tipo da pergunta que define como será apresentada e respondida. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- <strong>SIM_NAO</strong> (Pergunta Sim/Não)\n" +
            "- <strong>SIMPLES_ESCOLHA</strong> (Escolha Única)\n" +
            "- <strong>MULTIPLA_ESCOLHA</strong> (Múltipla Escolha)\n" +
            "- <strong>TEXTUAL</strong> (Resposta Textual)", example = "SIM_NAO")
    protected TiposPerguntaEnum tipoPergunta;

    @Transient
    @ApiModelProperty(value = "Lista de opções de resposta disponíveis para esta pergunta (campo transiente).")
    private List<OpcaoPergunta> opcoes = new ArrayList<OpcaoPergunta>();

    @Transient
    @ApiModelProperty(value = "Indica se a pergunta foi respondida pelo cliente (campo transiente).", example = "false")
    private Boolean respondida = Boolean.FALSE;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public List<SelectItem> getItens(){
        List<SelectItem> list = new ArrayList<SelectItem>();
        for(OpcaoPergunta o : opcoes){
            list.add(new SelectItem(o.getCodigo(), (o.getPeso() == null ? "" : (o.getPeso().toString().concat(" - "))).concat(o.getOpcao())));
        }
        return list;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TiposPerguntaEnum getTipoPergunta() {
        return tipoPergunta;
    }

    public void setTipoPergunta(TiposPerguntaEnum tipoPergunta) {
        this.tipoPergunta = tipoPergunta;
    }

    public List<OpcaoPergunta> getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(List<OpcaoPergunta> opcoes) {
        this.opcoes = opcoes;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }

    public Boolean getRespondida() {
        return respondida;
    }

    public void setRespondida(Boolean respondida) {
        this.respondida = respondida;
    }

    public AgrupamentoAvaliacaoIntegradaEnum getAgrupamento() {
        return agrupamento;
    }

    public void setAgrupamento(AgrupamentoAvaliacaoIntegradaEnum agrupamento) {
        this.agrupamento = agrupamento;
    }
}
