package br.com.pacto.bean.anamnese;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa a associação entre uma pergunta e uma anamnese, incluindo as respostas fornecidas pelo cliente durante a avaliação.")
public class PerguntaAnamnese {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único da pergunta-anamnese no banco de dados.", example = "789")
    private Integer codigo;

    @ManyToOne
    @ApiModelProperty(value = "Referência à anamnese à qual esta pergunta pertence.")
    private Anamnese anamnese;

    @ManyToOne
    @ApiModelProperty(value = "Referência à pergunta específica desta anamnese.")
    private Pergunta pergunta;

    @Transient
    @ApiModelProperty(value = "Resposta única fornecida pelo cliente (campo transiente).", example = "Sim")
    private String resposta;

    @Transient
    @ApiModelProperty(value = "Lista de respostas múltiplas fornecidas pelo cliente (campo transiente).")
    private List<String> respostas = new ArrayList<String>();

    @Transient
    @ApiModelProperty(value = "Complemento ou observação adicional à resposta (campo transiente).", example = "Apenas aos finais de semana")
    private String complemento;

    public PerguntaAnamnese() {
    }

    public PerguntaAnamnese(PerguntaAnamnese pa) {
        this.codigo = pa.codigo;
        this.anamnese = pa.anamnese;
        this.pergunta = pa.pergunta;
        this.resposta = pa.resposta;
        this.respostas = new ArrayList<String>(pa.respostas);
        this.complemento = pa.complemento;
    }

    public List<String> getRespostas() {
        return respostas;
    }

    public void setRespostas(List<String> respostas) {
        this.respostas = respostas;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Anamnese getAnamnese() {
        return anamnese;
    }

    public void setAnamnese(Anamnese anamnese) {
        this.anamnese = anamnese;
    }

    public Pergunta getPergunta() {
        return pergunta;
    }

    public void setPergunta(Pergunta pergunta) {
        this.pergunta = pergunta;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public int getOrdem(){
        try{
            return getPergunta().getOrdem();
        }catch (Exception ignore){
            return 0;
        }
    }
}
