package br.com.pacto.bean.anamnese;

import br.com.pacto.util.UteisValidacao;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 05/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações de uma pergunta da ficha de anamnese")
public class PerguntaTO {
    @ApiModelProperty(value = "Código único identificador da pergunta", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Pergunta", example = "Você consegue realizar atividades diárias como se vestir, tomar banho e cozinhar sem ajuda?")
    private String pergunta;
    @ApiModelProperty(value = "Tipo da resposta da pergunta. <br/> <strong>Valores disponíveis</strong>" +
            "<ul>" +
            "<li>0 - ESCOLHA_MULTIPLA (Escolha múltipla)</li>" +
            "<li>1 - ESCOLHA_UNICA (Escolha Única)</li>" +
            "<li>2 - SIM_NAO (Sim ou não)</li>" +
            "<li>3 - TEXTO (Texto)</li>" +
            "</ul>", example = "SIM_NAO", allowableValues = "ESCOLHA_MULTIPLA,ESCOLHA_UNICA,SIM_NAO,TEXTO")
    private TipoPerguntaDTOEnum tipo;
    @ApiModelProperty(value = "Lista das opções disponíveis para a resposta (Se ela for do tipo Escolha múltipla, escolha única ou sim ou não.")
    private List<OpcaoPerguntaTO> opcoes;
    @ApiModelProperty(value = "Ordem da pergunta na ficha de anamnese", example = "1")
    private Integer ordem;

    public PerguntaTO() {

    }

    public PerguntaTO(Integer codigo) {
        this.id = codigo;
    }

    public PerguntaTO(Pergunta p) {
        this.id = p.getCodigo();
        this.pergunta = p.getDescricao();
        this.tipo = p.getTipoPergunta().getTipoPerguntaDTOEnum();
        this.ordem = p.getOrdem();
        if (!UteisValidacao.emptyList(p.getOpcoes())) {
            this.opcoes = new ArrayList<OpcaoPerguntaTO>();
            for (OpcaoPergunta op : p.getOpcoes()) {
                OpcaoPerguntaTO opt = new OpcaoPerguntaTO();
                opt.setId(op.getCodigo());
                opt.setNome(op.getOpcao());
                this.opcoes.add(opt);
            }
        }

    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPergunta() {
        return pergunta;
    }

    public void setPergunta(String pergunta) {
        this.pergunta = pergunta;
    }

    public TipoPerguntaDTOEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoPerguntaDTOEnum tipo) {
        this.tipo = tipo;
    }

    public List<OpcaoPerguntaTO> getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(List<OpcaoPerguntaTO> opcoes) {
        this.opcoes = opcoes;
    }

    public Integer getOrdem() {
        return ordem;
    }

    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
