package br.com.pacto.bean.anamnese;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Created by Joao Alcides on 04/05/2017.
 */
@Entity
@Table
@ApiModel(description = "Entidade que representa uma opção de resposta para uma pergunta de anamnese, incluindo o texto da opção e seu peso para cálculos de pontuação.")
public class OpcaoPergunta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único da opção de pergunta no banco de dados.", example = "123")
    private Integer codigo;

    @ManyToOne
    @ApiModelProperty(value = "Referência à pergunta à qual esta opção pertence.")
    private Pergunta pergunta;

    @ApiModelProperty(value = "Texto da opção de resposta apresentada ao cliente.", example = "Sim, pratico regularmente")
    private String opcao;

    @ApiModelProperty(value = "Peso numérico da opção para cálculos de pontuação (pode ser nulo).", example = "3")
    private Integer peso = null;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Pergunta getPergunta() {
        return pergunta;
    }

    public void setPergunta(Pergunta pergunta) {
        this.pergunta = pergunta;
    }

    public String getOpcao() {
        return opcao;
    }

    public void setOpcao(String opcao) {
        this.opcao = opcao;
    }

    public Integer getPeso() {
        return peso;
    }

    public void setPeso(Integer peso) {
        this.peso = peso;
    }
}
