package br.com.pacto.bean.anamnese;

import br.com.pacto.bean.avaliacao.ItemAvaliacaoFisica;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;

@Entity
@Table
@ApiModel(description = "Entidade que representa um movimento 3D avaliado na avaliação integrada, incluindo testes de mobilidade e estabilidade com pontuações bilaterais.")
public class Movimento3D {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "Código identificador único do movimento 3D no banco de dados.", example = "789")
    private Integer codigo;

    @ManyToOne
    @ApiModelProperty(value = "Referência ao item da avaliação física ao qual este movimento pertence.")
    private ItemAvaliacaoFisica item;

    @Enumerated(EnumType.ORDINAL)
    @ApiModelProperty(value = "Tipo de movimento 3D avaliado, dividido entre mobilidade e estabilidade. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- <strong>cadeia_anterior</strong> (Mobilidade - Cadeia Anterior)\n" +
            "- <strong>cadeia_posterior</strong> (Mobilidade - Cadeia Posterior)\n" +
            "- <strong>cadeia_lateral</strong> (Mobilidade - Cadeia Lateral)\n" +
            "- <strong>cadeia_rotacional</strong> (Mobilidade - Cadeia Rotacional)\n" +
            "- <strong>controle</strong> (Estabilidade - Controle)\n" +
            "- <strong>cadeia_fechamento</strong> (Estabilidade - Fechamento)\n" +
            "- <strong>cadeia_abertura</strong> (Estabilidade - Abertura)", example = "cadeia_anterior")
    protected Movimento3DEnum movimento;

    @ApiModelProperty(value = "Pontuação obtida no lado esquerdo do corpo para este movimento (escala de 0 a 3 pontos).", example = "2")
    private Integer esquerda = 0;

    @ApiModelProperty(value = "Pontuação obtida no lado direito do corpo para este movimento (escala de 0 a 3 pontos).", example = "3")
    private Integer direita = 0;

    public Movimento3D() {
    }

    public Movimento3D(Movimento3DEnum m) {
        this.movimento = m;
    }

    public Integer getEsquerda() {
        return esquerda;
    }

    public void setEsquerda(Integer esquerda) {
        this.esquerda = esquerda;
    }

    public Integer getDireita() {
        return direita;
    }

    public void setDireita(Integer direita) {
        this.direita = direita;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ItemAvaliacaoFisica getItem() {
        return item;
    }

    public void setItem(ItemAvaliacaoFisica item) {
        this.item = item;
    }

    public Movimento3DEnum getMovimento() {
        return movimento;
    }

    public void setMovimento(Movimento3DEnum movimento) {
        this.movimento = movimento;
    }
}
