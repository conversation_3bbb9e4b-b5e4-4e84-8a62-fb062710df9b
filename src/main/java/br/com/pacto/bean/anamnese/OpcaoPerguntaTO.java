package br.com.pacto.bean.anamnese;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by ulisses on 06/08/2018.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(description = "Informações das opções de resposta de uma pergunta")
public class OpcaoPerguntaTO {
    @ApiModelProperty(value = "Código único identificador da opção", example = "1")
    private Integer id;
    @ApiModelProperty(value = "Opção da resposta", example = "Sim")
    private String nome;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
