package br.com.pacto.util.json;

import br.com.pacto.bean.cliente.ClienteSintetico;
import br.com.pacto.controller.json.base.SuperJSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;


/**
 * Versão sintética das informações de cliente para integração
 * <AUTHOR> Siqueira
 */
@ApiModel(description = "Versão sintética das informações de cliente para integração com webservices")
public class ClienteSintenticoJson extends SuperJSON {

    @ApiModelProperty(value = "Código identificador único do cliente", example = "1001")
    private Integer codigo;

    @ApiModelProperty(value = "Código da pessoa no sistema ZW (integração)", example = "5001")
    private Integer codigoPessoaZW;

    @ApiModelProperty(value = "Código do cliente no sistema ZW (integração)", example = "3001")
    private Integer codigoClienteZW;
    private Integer codigoContrato;

    @ApiModelProperty(value = "Número da matrícula do cliente", example = "2024001")
    private Integer matricula;

    @ApiModelProperty(value = "Nome completo do cliente", example = "Maria Santos Silva")
    private String nome;

    @ApiModelProperty(value = "Data de nascimento do cliente")
    private Date dataNascimento;

    @ApiModelProperty(value = "Idade do cliente em anos", example = "32")
    private Integer idade;

    @ApiModelProperty(value = "Profissão do cliente", example = "Advogada")
    private String profissao;

    @ApiModelProperty(value = "Colaboradores responsáveis pelo cliente", example = "Ana Costa, Pedro Lima")
    private String colaboradores;

    @ApiModelProperty(value = "Professor responsável pelo cliente", example = "Ana Costa")
    private String professor;

    @ApiModelProperty(value = "Situação atual do cliente. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)", example = "AT")
    private String situacao;

    @ApiModelProperty(value = "Situação do contrato do cliente. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- MA (MATRICULA)\n" +
            "- RE (REMATRICULA)\n" +
            "- RN (RENOVACAO)", example = "RE")
    private String situacaoContrato;

    @ApiModelProperty(value = "Data do último acesso do cliente ao sistema")
    private Date dataUltimoAcesso;

    @ApiModelProperty(value = "Data de início do período de acesso do cliente")
    private Date dataInicioPeriodoAcesso;

    @ApiModelProperty(value = "Data de fim do período de acesso do cliente")
    private Date dataFimPeriodoAcesso;

    @ApiModelProperty(value = "Código da empresa do cliente", example = "1")
    private Integer empresa;

    @ApiModelProperty(value = "Endereço de email do cliente", example = "<EMAIL>")
    private String email = "";
    private String telefones;

    @ApiModelProperty(value = "Andamento do treino do cliente", example = "Em progresso")
    private String andamentoTreino;

    @ApiModelProperty(value = "URL da foto do cliente", example = "https://exemplo.com/foto.jpg")
    private String urlFoto;

    @ApiModelProperty(value = "Nome de usuário do cliente no sistema", example = "maria.santos")
    private String userName;

    @ApiModelProperty(value = "Data de referência para consulta")
    private Date dia;

    @ApiModelProperty(value = "Nível do cliente na academia", example = "Intermediário")
    private String nivel;

    @ApiModelProperty(value = "Nome da empresa/academia", example = "Academia Fitness Plus")
    private String nomeEmpresa;

    @ApiModelProperty(value = "Versão do registro do cliente", example = "2")
    private Integer versao;

    @ApiModelProperty(value = "Código de acesso do cliente", example = "ACC2024001")
    private String codigoAcesso;

    @ApiModelProperty(value = "Data de vencimento do plano do cliente", example = "31/12/2024")
    private String vencPlano;

    @ApiModelProperty(value = "Lista de telefones do cliente")
    private List<String> listaTelefones;

    @ApiModelProperty(value = "Lista de emails do cliente")
    private List<String> listaEmails;

    @ApiModelProperty(value = "Sexo do cliente", example = "F")
    private String sexo;

    @ApiModelProperty(value = "Objetivos do cliente na academia", example = "Perda de peso e fortalecimento muscular")
    private String objetivos;

    @ApiModelProperty(value = "Dados da avaliação física do cliente", example = "IMC: 24.5, BF: 18%")
    private String dadosAvaliacao;

    @ApiModelProperty(value = "Indica se o cliente preencheu o questionário PAR-Q", example = "true")
    private Boolean parQ;

    @ApiModelProperty(value = "Data de vigência do contrato do cliente")
    private Date dataVirgencia;

    public ClienteSintenticoJson() {
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoPessoaZW() {
        return codigoPessoaZW;
    }

    public void setCodigoPessoaZW(Integer codigoPessoaZW) {
        this.codigoPessoaZW = codigoPessoaZW;
    }

    public Integer getCodigoClienteZW() {
        return codigoClienteZW;
    }

    public void setCodigoClienteZW(Integer codigoClienteZW) {
        this.codigoClienteZW = codigoClienteZW;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(String colaboradores) {
        this.colaboradores = colaboradores;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Date getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public Date getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    public void setDataInicioPeriodoAcesso(Date dataInicioPeriodoAcesso) {
        this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
    }

    public Date getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    public void setDataFimPeriodoAcesso(Date dataFimPeriodoAcesso) {
        this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public String getAndamentoTreino() {
        return andamentoTreino;
    }

    public void setAndamentoTreino(String andamentoTreino) {
        this.andamentoTreino = andamentoTreino;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getNivel() {
        return nivel;
    }

    public void setNivel(String nivel) {
        this.nivel = nivel;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Integer getVersao() {
        return versao;
    }

    public void setVersao(Integer versao) {
        this.versao = versao;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }

    public String getVencPlano() {
        return vencPlano;
    }

    public void setVencPlano(String vencPlano) {
        this.vencPlano = vencPlano;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getObjetivos() {
        return objetivos;
    }

    public void setObjetivos(String objetivos) {
        this.objetivos = objetivos;
    }

    public List<String> getListaTelefones() {
        return listaTelefones;
    }

    public void setListaTelefones(List<String> listaTelefones) {
        this.listaTelefones = listaTelefones;
    }

    public List<String> getListaEmails() {
        return listaEmails;
    }

    public void setListaEmails(List<String> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public String getDadosAvaliacao() {
        return dadosAvaliacao;
    }

    public void setDadosAvaliacao(String dadosAvaliacao) {
        this.dadosAvaliacao = dadosAvaliacao;
    }

    public Boolean getParQ() {
        return parQ;
    }

    public void setParQ(Boolean parQ) {
        this.parQ = parQ;
    }

    public Date getDataVirgencia() {
        return dataVirgencia;
    }

    public void setDataVirgencia(Date dataVirgencia) {
        this.dataVirgencia = dataVirgencia;
    }

    public static ClienteSintenticoJson fromClienteSinteticoToClienteSinteticoJson(ClienteSintetico c){
        ClienteSintenticoJson cliente = new ClienteSintenticoJson();
        cliente.setCodigo(c.getCodigo());
        cliente.setCodigoPessoaZW(c.getCodigoPessoa());
        cliente.setCodigoClienteZW(c.getCodigoCliente());
        cliente.setCodigoContrato(c.getCodigoContrato());
        cliente.setMatricula(c.getMatricula());
        cliente.setNome(c.getNome());
        cliente.setDataNascimento(c.getDataNascimento());
        cliente.setIdade(c.getIdade());
        cliente.setProfissao(c.getProfissao());
        cliente.setColaboradores(c.getColaboradores());
        cliente.setProfessor(c.getProfessor());
        cliente.setSituacao(c.getSituacao());
        cliente.setSituacaoContrato(c.getSituacaoContrato());
        cliente.setDataUltimoAcesso(c.getDataUltimoacesso());
        cliente.setDataInicioPeriodoAcesso(c.getDataInicioPeriodoAcesso());
        cliente.setDataFimPeriodoAcesso(c.getDataFimPeriodoAcesso());
        cliente.setEmpresa(c.getEmpresa());
        cliente.setEmail(c.getEmail());
        cliente.setTelefones(c.getTelefones());
        cliente.setAndamentoTreino(c.getAndamentoTreino());
        cliente.setUrlFoto(c.getUrlFoto());
        cliente.setUserName(c.getNome());
        cliente.setDia(c.getDia());
        cliente.setNivel(c.getNivel());
        if(c.getEmpresaTreino() != null){
            cliente.setNomeEmpresa(c.getEmpresaTreino().getNome());
        }
        cliente.setNomeEmpresa("");
        cliente.setVersao(c.getVersao());
        cliente.setCodigoAcesso(c.getCodigoAcesso());
        cliente.setVencPlano(c.getDataVigenciaDeApresentar());
        cliente.setListaTelefones(c.getListaTelefones());
        cliente.setListaEmails(c.getListaEmails());
        cliente.setSexo(c.getSexo());
        cliente.setObjetivos(c.getObjetivos());
        cliente.setDadosAvaliacao(c.getDadosAvaliacao());
        cliente.setParQ(c.getParq());
        cliente.setDataVirgencia(c.getDataVigenciaDe());

        return cliente;
    }
}

