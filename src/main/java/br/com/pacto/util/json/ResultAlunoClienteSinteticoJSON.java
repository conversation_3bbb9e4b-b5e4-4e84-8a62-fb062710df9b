package br.com.pacto.util.json;

import br.com.pacto.controller.json.base.SuperJSON;
import br.com.pacto.util.AgendadoJSON;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * Resultado combinado de consulta de alunos e clientes sintéticos
 * <AUTHOR> Si<PERSON>
 */
@ApiModel(description = "Resultado combinado de consulta de alunos agendados e dados sintéticos de clientes")
public class ResultAlunoClienteSinteticoJSON extends SuperJSON {

    @ApiModelProperty(value = "Lista de clientes com informações sintéticas para integração")
    List<ClienteSintenticoJson> resultCliSintentico;

    @ApiModelProperty(value = "Lista de alunos agendados para a aula consultada")
    List<AgendadoJSON> resultAgendado;

    public ResultAlunoClienteSinteticoJSON() {
    }

    public ResultAlunoClienteSinteticoJSON(List<ClienteSintenticoJson> resultCliSintentico, List<AgendadoJSON> resultAgendado) {
        this.resultCliSintentico = resultCliSintentico;
        this.resultAgendado = resultAgendado;
    }

    public List<ClienteSintenticoJson> getResultCliSintentico() {
        return resultCliSintentico;
    }

    public void setResultCliSintentico(List<ClienteSintenticoJson> resultCliSintentico) {
        this.resultCliSintentico = resultCliSintentico;
    }

    public List<AgendadoJSON> getResultAgendado() {
        return resultAgendado;
    }

    public void setResultAgendado(List<AgendadoJSON> resultAgendado) {
        this.resultAgendado = resultAgendado;
    }
}
