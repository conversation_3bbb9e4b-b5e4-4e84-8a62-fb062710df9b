/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.util;

import br.com.pacto.controller.json.base.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * Representa um aluno agendado para uma aula específica
 * <AUTHOR>
 */
@ApiModel(description = "Informações de um aluno agendado para uma aula específica")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgendadoJSON extends SuperJSON {

    @ApiModelProperty(value = "Código da pessoa no sistema", example = "5001")
    private Integer codigoPessoa;

    @ApiModelProperty(value = "Código do cliente no sistema", example = "3001")
    private Integer codigoCliente;

    @ApiModelProperty(value = "Código do contrato do cliente", example = "2001")
    private Integer codigoContrato;

    @ApiModelProperty(value = "Identificador único do agendamento", example = "AGD_2024_001")
    private String id_agendamento;

    @ApiModelProperty(value = "URL da foto do aluno", example = "https://academia.com/fotos/aluno_123.jpg")
    private String urlFoto;

    @ApiModelProperty(value = "Horário de início da aula no formato HH:mm", example = "08:00")
    private String inicio;

    @ApiModelProperty(value = "Horário de término da aula no formato HH:mm", example = "09:00")
    private String fim;

    @ApiModelProperty(value = "Nome completo do aluno", example = "Pedro Costa Lima")
    private String nome;

    @ApiModelProperty(value = "Número da matrícula do aluno", example = "2024002")
    private String matricula;

    @ApiModelProperty(value = "Números de telefone do aluno", example = "(11) 77777-7777")
    private String telefones;

    @ApiModelProperty(value = "Saldo de créditos de treino disponível", example = "5")
    private Integer saldoCreditoTreino = 0;

    @ApiModelProperty(value = "Indica se o aluno usa saldo de créditos", example = "false")
    private Boolean usaSaldo = false;

    @ApiModelProperty(value = "Indica se o aluno usa sistema de turmas", example = "true")
    private Boolean usaTurma = false;

    @ApiModelProperty(value = "Indica se o aluno confirmou presença na aula", example = "true")
    private Boolean confirmado = false;

    @ApiModelProperty(value = "Situação do contrato do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- MA (MATRICULA)\n" +
            "- RE (REMATRICULA)\n" +
            "- RN (RENOVACAO)", example = "MA")
    private String situacaoContrato;

    @ApiModelProperty(value = "Situação atual do aluno. \n\n" +
            "<strong>Valores disponíveis</strong>\n" +
            "- AT (ATIVO)\n" +
            "- IN (INATIVO)\n" +
            "- VI (VISITANTE)\n" +
            "- TR (TRANCADO)\n" +
            "- AE (ATESTADO)\n" +
            "- CA (CANCELADO)\n" +
            "- CR (CARENCIA)\n" +
            "- DE (DESISTENTE)\n" +
            "- VE (VENCIDO)\n" +
            "- OU (OUTROS)", example = "AT")
    private String situacao;

    @ApiModelProperty(value = "Quantidade de reposições futuras disponíveis para o aluno", example = "3")
    private Integer qtdReposicoesFuturas = 0;

    @ApiModelProperty(value = "Indica se o aluno possui contrato futuro", example = "false")
    private Boolean contratoFuturo;

    @ApiModelProperty(value = "Lista de modalidades do contrato futuro")
    private List<Integer> modalidadesContratoFuturo = new ArrayList<Integer>();

    @ApiModelProperty(value = "Código do contrato futuro", example = "0")
    private Integer codContratoFuturo = 0;

    @ApiModelProperty(value = "Quantidade de créditos do contrato futuro", example = "0")
    private Integer qtdCreditoFuturo = 0;

    @ApiModelProperty(value = "Indica se o aluno utiliza GymPass", example = "false")
    private boolean gymPass = false;

    @ApiModelProperty(value = "Indica se o aluno utiliza TotalPass", example = "false")
    private boolean totalPass = false;

    @ApiModelProperty(value = "Indica se é uma diária avulsa", example = "false")
    private boolean diaria = false;

    @ApiModelProperty(value = "Indica se o aluno é participante fixo da turma", example = "true")
    private boolean fixo = false;

    @ApiModelProperty(value = "Indica se é uma aula experimental", example = "false")
    private boolean experimental = false;

    @ApiModelProperty(value = "Indica se é presença de reposição", example = "false")
    private boolean presencaReposicao = false;

    @ApiModelProperty(value = "Indica se a aula foi desmarcada", example = "false")
    private boolean desmarcado = false;

    @ApiModelProperty(value = "Chave da foto do aluno", example = "foto_123")
    private String fotokey;

    @ApiModelProperty(value = "Indica se é participação em desafio", example = "false")
    private boolean desafio = false;

    @ApiModelProperty(value = "Número do assento Spivi para aulas de spinning", example = "5")
    private int spiviSeat;

    @ApiModelProperty(value = "ID do evento Spivi", example = "1001")
    private int spiviEventID;

    @ApiModelProperty(value = "ID do cliente no sistema Spivi", example = "2001")
    private int spiviClientID;

    @ApiModelProperty(value = "Data de nascimento do aluno", example = "15/03/1990")
    private String dataNascimento;

    @ApiModelProperty(value = "Email do aluno", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "Sexo do aluno", example = "M")
    private String sexo;

    @ApiModelProperty(value = "Cidade do aluno", example = "São Paulo")
    private String cidade;

    @ApiModelProperty(value = "Horário da marcação da aula em timestamp", example = "1642694400000")
    private Long horarioMarcacao;

    @ApiModelProperty(value = "Código do cliente passivo", example = "1001")
    private Integer codigoPassivo;

    @ApiModelProperty(value = "Código do cliente indicado", example = "2001")
    private Integer codigoIndicado;

    @ApiModelProperty(value = "Equipamento reservado para a aula", example = "Esteira 5")
    private String equipamentoReservado;

    @ApiModelProperty(value = "ID do usuário no sistema Selfloops", example = "user_123")
    private String userIdSelfloops;

    @ApiModelProperty(value = "Potência média do aluno nos exercícios", example = "180")
    private Integer averagePower;

    @ApiModelProperty(value = "Total de calorias queimadas pelo aluno", example = "350")
    private Integer calories;

    @ApiModelProperty(value = "Tempo total de aula do aluno em minutos", example = "60")
    private Integer tempoDeAula;

    @ApiModelProperty(value = "Posição do aluno no ranking geral", example = "15")
    private Integer posicaoRankingAluno;

    @ApiModelProperty(value = "Indica se o aluno está na lista de espera", example = "false")
    private boolean espera = false;

    @ApiModelProperty(value = "Indica se o aluno está autorizado para gestão de rede", example = "false")
    private Boolean autorizadoGestaoRede = false;

    @ApiModelProperty(value = "Código de acesso autorizado", example = "")
    private String codAcessoAutorizado = "";

    @ApiModelProperty(value = "Matrícula do aluno autorizado", example = "0")
    private Integer matriculaAutorizado = 0;


    public AgendadoJSON() {
    }

    public AgendadoJSON(Integer codigoPessoa, Integer codigoCliente, String nome, String matricula,
                        Integer codigoContrato, boolean usaSaldo, Integer saldo, Integer qtdReposicoesFuturas) {
        this.codigoPessoa = codigoPessoa;
        this.codigoCliente = codigoCliente;
        this.nome = nome;
        this.matricula = matricula;
        this.codigoContrato = codigoContrato;
        this.saldoCreditoTreino = saldo;
        this.usaSaldo = usaSaldo;
        this.qtdReposicoesFuturas = qtdReposicoesFuturas;
    }

    public Long getHorarioMarcacao() {
        return horarioMarcacao;
    }

    public void setHorarioMarcacao(Long horarioMarcacao) {
        this.horarioMarcacao = horarioMarcacao;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getId_agendamento() {
        return id_agendamento;
    }

    public void setId_agendamento(String id_agendamento) {
        this.id_agendamento = id_agendamento;
    }

    public String getInicio() {
        return inicio;
    }

    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    public String getFim() {
        return fim;
    }

    public void setFim(String fim) {
        this.fim = fim;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getTelefones() {
        return telefones;
    }

    public void setTelefones(String telefones) {
        this.telefones = telefones;
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public Boolean getUsaSaldo() {
        return usaSaldo;
    }

    public void setUsaSaldo(Boolean usaSaldo) {
        this.usaSaldo = usaSaldo;
    }

    public Boolean getUsaTurma() {
        return usaTurma;
    }

    public void setUsaTurma(Boolean usaTurma) {
        this.usaTurma = usaTurma;
    }

    public Boolean getConfirmado() {
        return confirmado;
    }

    public void setConfirmado(Boolean confirmado) {
        this.confirmado = confirmado;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }


    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getQtdReposicoesFuturas() {
        return qtdReposicoesFuturas;
    }

    public void setQtdReposicoesFuturas(Integer qtdReposicoesFuturas) {
        this.qtdReposicoesFuturas = qtdReposicoesFuturas;
    }

    public Boolean getContratoFuturo() {
        return contratoFuturo;
    }

    public void setContratoFuturo(Boolean contratoFuturo) {
        this.contratoFuturo = contratoFuturo;
    }

    public List<Integer> getModalidadesContratoFuturo() {
        return modalidadesContratoFuturo;
    }

    public void setModalidadesContratoFuturo(List<Integer> modalidadesContratoFuturo) {
        this.modalidadesContratoFuturo = modalidadesContratoFuturo;
    }

    public Integer getCodContratoFuturo() {
        return codContratoFuturo;
    }

    public void setCodContratoFuturo(Integer codContratoFuturo) {
        this.codContratoFuturo = codContratoFuturo;
    }

    public Integer getQtdCreditoFuturo() {
        return qtdCreditoFuturo;
    }

    public void setQtdCreditoFuturo(Integer qtdCreditoFuturo) {
        this.qtdCreditoFuturo = qtdCreditoFuturo;
    }

    public boolean isGymPass() {
        return gymPass;
    }

    public void setGymPass(boolean gymPass) {
        this.gymPass = gymPass;
    }

    public boolean isTotalPass() {
        return totalPass;
    }

    public void setTotalPass(boolean totalPass) {
        this.totalPass = totalPass;
    }

    public boolean isDiaria() {
        return diaria;
    }

    public void setDiaria(boolean diaria) {
        this.diaria = diaria;
    }

    public boolean isExperimental() {
        return experimental;
    }

    public void setExperimental(boolean experimental) {
        this.experimental = experimental;
    }

    public boolean isPresencaReposicao() {
        return presencaReposicao;
    }

    public void setPresencaReposicao(boolean presencaReposicao) {
        this.presencaReposicao = presencaReposicao;
    }

    public boolean isDesmarcado() {
        return desmarcado;
    }

    public void setDesmarcado(boolean desmarcado) {
        this.desmarcado = desmarcado;
    }

    public int getSpiviSeat() {
        return spiviSeat;
    }

    public void setSpiviSeat(final int spiviSeat) {
        this.spiviSeat = spiviSeat;
    }

    public int getSpiviEventID() {
        return spiviEventID;
    }

    public void setSpiviEventID(final int spiviEventID) {
        this.spiviEventID = spiviEventID;
    }

    public int getSpiviClientID() {
        return spiviClientID;
    }

    public void setSpiviClientID(final int spiviClientID) {
        this.spiviClientID = spiviClientID;
    }

    public boolean isDesafio() {
        return desafio;
    }

    public void setDesafio(boolean desafio) {
        this.desafio = desafio;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public boolean isFixo() {
        return fixo;
    }

    public void setFixo(boolean fixo) {
        this.fixo = fixo;
    }

    public Integer getCodigoPassivo() {
        return codigoPassivo;
    }

    public void setCodigoPassivo(Integer codigoPassivo) {
        this.codigoPassivo = codigoPassivo;
    }

    public Integer getCodigoIndicado() {
        return codigoIndicado;
    }

    public void setCodigoIndicado(Integer codigoIndicado) {
        this.codigoIndicado = codigoIndicado;
    }

    public boolean isEspera() {
        return espera;
    }

    public void setEspera(boolean espera) {
        this.espera = espera;
    }

    public String getEquipamentoReservado() {
        return equipamentoReservado;
    }

    public void setEquipamentoReservado(String equipamentoReservado) {
        this.equipamentoReservado = equipamentoReservado;
    }

    public String getUserIdSelfloops() {
        return userIdSelfloops;
    }

    public void setUserIdSelfloops(String userIdSelfloops) {
        this.userIdSelfloops = userIdSelfloops;
    }

    public Integer getAveragePower() {
        return averagePower;
    }

    public void setAveragePower(Integer averagePower) {
        this.averagePower = averagePower;
    }

    public Integer getCalories() {
        return calories;
    }

    public void setCalories(Integer calories) {
        this.calories = calories;
    }

    public Integer getTempoDeAula() {
        return tempoDeAula;
    }

    public void setTempoDeAula(Integer tempoDeAula) {
        this.tempoDeAula = tempoDeAula;
    }

    public Integer getPosicaoRankingAluno() {
        return posicaoRankingAluno;
    }

    public void setPosicaoRankingAluno(Integer posicaoRankingAluno) {
        this.posicaoRankingAluno = posicaoRankingAluno;
    }

    public Boolean getAutorizadoGestaoRede() {
        if (autorizadoGestaoRede == null) {
            autorizadoGestaoRede = false;
        }
        return autorizadoGestaoRede;
    }

    public void setAutorizadoGestaoRede(Boolean autorizadoGestaoRede) {
        this.autorizadoGestaoRede = autorizadoGestaoRede;
    }

    public String getCodAcessoAutorizado() {
        return codAcessoAutorizado;
    }

    public void setCodAcessoAutorizado(String codAcessoAutorizado) {
        this.codAcessoAutorizado = codAcessoAutorizado;
    }

    public Integer getMatriculaAutorizado() {
        return matriculaAutorizado;
    }

    public void setMatriculaAutorizado(Integer matriculaAutorizado) {
        this.matriculaAutorizado = matriculaAutorizado;
    }
}
