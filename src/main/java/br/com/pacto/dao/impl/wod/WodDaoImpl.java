package br.com.pacto.dao.impl.wod;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.bean.wod.FiltroWodJSON;
import br.com.pacto.bean.wod.Wod;
import br.com.pacto.dao.impl.base.DaoGenericoImpl;
import br.com.pacto.dao.intf.wod.WodDao;
import br.com.pacto.objeto.Calendario;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.impl.notificacao.excecao.WodExcecoes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON> on 20/07/2016.
 */
@Repository
public class WodDaoImpl extends DaoGenericoImpl<Wod, Integer> implements WodDao {

    private static final int INDICE_COLUNA_DIA = 1;
    private static final int INDICE_PARAMETRO_DATAINICIAL = 1;
    private static final int INDICE_PARAMETRO_DATAFINAL = 2;
    private static final int INDICE_PARAMETRO_EMPRESA = 3;
    @Override
    public List<java.util.Date> consultarDiasWodPeriodo(String contexto, Integer empresa, Long dataInicial, Long dataFinal) throws Exception {
        final String SQL;
        if (empresa == null) {
            SQL = "SELECT DISTINCT dia FROM " + Wod.class.getSimpleName() + " WHERE dia BETWEEN ? AND ? ORDER BY dia";
        } else {
            SQL = "SELECT DISTINCT dia FROM " + Wod.class.getSimpleName() + " WHERE (dia BETWEEN ? AND ?) AND empresa = ? ORDER BY dia";
        }
        final List<java.util.Date> diasWod = new ArrayList<java.util.Date>();

        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;

        try {

            connection = getConnection(contexto);
            preparedStatement = getStatementDiasWodPeriodo(connection, SQL, empresa, dataInicial, dataFinal);

            resultSet = preparedStatement.executeQuery();

            if (resultSet == null) {
                return diasWod;
            }

            while (resultSet.next()) {
                diasWod.add(getData(resultSet));
            }

        } catch (SQLException e) {
            Uteis.logar(e, WodDaoImpl.class);
        } finally {

            if (resultSet != null && !resultSet.isClosed()) {
                try {
                    resultSet.close();
                } catch (Exception e) {
                    Uteis.logar(e, WodDaoImpl.class);
                }
            }

            if (preparedStatement != null && !preparedStatement.isClosed()) {
                try {
                    preparedStatement.close();
                } catch (Exception e) {
                    Uteis.logar(e, WodDaoImpl.class);
                }
            }

            if (connection != null && !connection.isClosed()) {
                try {
                    connection.close();
                } catch (Exception e) {
                    Uteis.logar(e, WodDaoImpl.class);
                }
            }

        }

        return diasWod;
    }

    private static final int MAXIMO_WODS_LISTAR = 50;

    @Override
    public List<Wod> obterListaWods(String ctx, FiltroWodJSON filtros, PaginadorDTO paginadorDTO, Integer empresaId, boolean restringirEmpresas) throws ServiceException {
        StringBuilder hql = new StringBuilder();
        StringBuilder where = new StringBuilder();
        Map<String, Object> param = new HashMap<>();
        int maxResults = MAXIMO_WODS_LISTAR;
        int indiceInicial = 0;

        if (paginadorDTO != null) {
            maxResults = paginadorDTO.getSize() == null ? MAXIMO_WODS_LISTAR : paginadorDTO.getSize().intValue();
            indiceInicial = paginadorDTO.getPage() == null ? indiceInicial : paginadorDTO.getPage().intValue() * maxResults;
        }

        hql.append("SELECT obj FROM Wod obj ");

        boolean temFiltro = false;

        if (filtros.getNome() && !StringUtils.isBlank(filtros.getParametro())) {
            where.append(" WHERE upper(obj.nome) like CONCAT('%',:nome,'%')");
            param.put("nome", filtros.getParametro().toUpperCase());
           temFiltro = true;
        }

        if (restringirEmpresas) {
            if (temFiltro) {
                where.append(" AND ");
            } else {
                where.append(" WHERE ");
                temFiltro = true;
            }
            where.append(" obj.empresa = :empresaId ");
            param.put("empresaId", empresaId);
        }

        if (!filtros.getUnidade().isEmpty() && filtros.getUnidade().stream().noneMatch(Objects::isNull)) {
             if (temFiltro) {
                where.append(" AND ");
            } else {
                where.append(" WHERE ");
                 temFiltro = true;
            }
            where.append(" obj.empresa IN (:unidade) ");
            param.put("unidade", filtros.getUnidade());
        }

        hql.append(where.toString());
        hql.append(paginadorDTO.getSQLOrderByUse());
        try {
            if (paginadorDTO != null) {
                paginadorDTO.setQuantidadeTotalElementos(countWithParam(ctx, "codigo", where, param).longValue());
            }
            paginadorDTO.setSize((long) maxResults);
            paginadorDTO.setPage((long) indiceInicial);
            return findByParam(ctx, hql.toString(), param, maxResults, indiceInicial);
        } catch (Exception e) {
            throw new ServiceException(WodExcecoes.ERRO_BUSCAR_WODS, e);
        }
    }

    @Override
    public List<Wod> listarTodosOsWods(String ctx, java.util.Date dia, Integer empresaId) throws ServiceException {
        return listarTodosOsWods(ctx, dia, empresaId, false);
    }

    @Override
    public List<Wod> listarTodosOsWods(String ctx, java.util.Date dia, Integer empresaId, Boolean todasUnidades) throws ServiceException {
        StringBuilder hql = new StringBuilder();
        hql.append("SELECT obj FROM Wod obj where dia between :inicio and :fim");

        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("inicio", Calendario.getDataComHoraZerada(dia));
        param.put("fim", Calendario.getDataComHora(dia, "23:59"));

        if (todasUnidades != null && todasUnidades) {
            try {
                br.com.pacto.service.intf.empresa.EmpresaService empresaService =
                    (br.com.pacto.service.intf.empresa.EmpresaService) br.com.pacto.util.UtilContext.getBean(
                        br.com.pacto.service.impl.empresa.EmpresaServiceImpl.class);

                List<br.com.pacto.bean.empresa.Empresa> todasEmpresas = empresaService.obterTodos(ctx);
                List<Integer> empresasDaMesmaChave = new ArrayList<>();

                for (br.com.pacto.bean.empresa.Empresa empresa : todasEmpresas) {
                    if (empresa.getCodZW() != null) {
                        empresasDaMesmaChave.add(empresa.getCodZW());
                    }
                }

                if (!empresasDaMesmaChave.isEmpty()) {
                    hql.append(" AND (obj.empresa IN (");
                    for (int i = 0; i < empresasDaMesmaChave.size(); i++) {
                        if (i > 0) hql.append(", ");
                        hql.append(empresasDaMesmaChave.get(i));
                    }
                    hql.append(") OR (obj.idRede IS NOT NULL AND obj.idRede != ''))");
                } else {
                    hql.append(" AND (obj.empresa = :empresaId OR (obj.idRede IS NOT NULL AND obj.idRede != ''))");
                    param.put("empresaId", empresaId);
                }


            } catch (Exception e) {
                hql.append(" AND (obj.empresa = :empresaId OR (obj.idRede IS NOT NULL AND obj.idRede != ''))");
                param.put("empresaId", empresaId);
            }
        } else {
            hql.append(" AND obj.empresa = :empresaId");
            param.put("empresaId", empresaId);
        }

        hql.append(" ORDER BY obj.dia DESC, obj.codigo DESC");

        try {
            return findByParam(ctx, hql.toString(), param);
        } catch (Exception e) {
            throw new ServiceException(WodExcecoes.ERRO_BUSCAR_WODS, e);
        }
    }


    private java.util.Date getData(ResultSet resultSet) throws SQLException {
        return new java.util.Date(resultSet.getDate(INDICE_COLUNA_DIA).getTime());
    }

    private PreparedStatement getStatementDiasWodPeriodo(Connection connection, String SQL, Integer empresa, Long dataInicial,
                                                         Long dataFinal) throws SQLException {
        final PreparedStatement preparedStatement = connection.prepareStatement(SQL);
        preparedStatement.setDate(INDICE_PARAMETRO_DATAINICIAL, new Date(dataInicial));
        preparedStatement.setDate(INDICE_PARAMETRO_DATAFINAL, new Date(dataFinal));
        if (empresa != null) {
            preparedStatement.setInt(INDICE_PARAMETRO_EMPRESA, empresa);
        }
        return preparedStatement;
    }

    @Override
    public Wod obterPorId(String ctx, Integer id) throws ServiceException {
        try {
            getCurrentSession(ctx).clear();
            return findById(ctx, id);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

}
