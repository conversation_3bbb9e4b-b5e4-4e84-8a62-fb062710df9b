package br.com.pacto.base.jpa.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Dados de uma atividade para importação via DTO")
public class TreinoAtividadeDTO {

    @ApiModelProperty(value = "Código único da atividade", example = "123")
    private Integer codigo;

    @ApiModelProperty(value = "Nome da atividade", example = "Supino Reto com Barra")
    private String nome;

    @ApiModelProperty(value = "Nome do aparelho utilizado", example = "Banco horizontal")
    private String nomeAparelho;

    @ApiModelProperty(value = "Código do grupo muscular trabalhado", example = "1")
    private Integer grupoMuscular;

    @ApiModelProperty(value = "Tipo da atividade", example = "Musculação")
    private String tipoAtividade;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getGrupoMuscular() {
        return grupoMuscular;
    }

    public void setGrupoMuscular(Integer grupoMuscular) {
        this.grupoMuscular = grupoMuscular;
    }

    public String getNomeAparelho() {
        return nomeAparelho;
    }

    public void setNomeAparelho(String nomeAparelho) {
        this.nomeAparelho = nomeAparelho;
    }

    public String getTipoAtividade() {
        return tipoAtividade;
    }

    public void setTipoAtividade(String tipoAtividade) {
        this.tipoAtividade = tipoAtividade;
    }
}
