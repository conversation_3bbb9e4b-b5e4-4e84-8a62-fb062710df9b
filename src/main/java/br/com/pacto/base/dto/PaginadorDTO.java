package br.com.pacto.base.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * DTO de paginação
 *
 * <AUTHOR>
 * @since 20/07/2018
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaginadorDTO {

    private static final String SEPARADOR_ORDENACOES = ";";
    private static final String SEPARADOR_ORDENACAO = ",";
    private static final String SEPARADOR_FUNCAO = "\\$";
    private static final int INDEX_CAMPO_ORDENAR = 0;
    private static final int INDEX_DIRECAO_ORDENACAO = 1;

    @ApiModelProperty(value = "Quantidade total de elementos na página", example = "0")
    private Long size;
    @ApiModelProperty(value = "Página da requisição", example = "1")
    private Long page;
    @ApiModelProperty(value = "Ordenação das respostas")
    private String sort;
    private Map<String, String> sortMap;

    @ApiModelProperty(value = "Quantidade total de elementos encontrados", hidden = true)
    private Long quantidadeTotalElementos;

    public PaginadorDTO() {
    }

    public PaginadorDTO(PaginadorDTO copia) {
        this.size = copia.getSize();
        this.page = copia.getPage();
        this.sort = copia.getSort();
        this.sortMap = copia.getSortMap();
        this.quantidadeTotalElementos = copia.getQuantidadeTotalElementos();
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Long getPage() {
        return page;
    }

    public void setPage(Long page) {
        this.page = page;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
        carregarSortMap();
    }

    public Map<String, String> getSortMap() {
        return sortMap;
    }

    public Long getQuantidadeTotalElementos() {
        return quantidadeTotalElementos;
    }

    public void setQuantidadeTotalElementos(Long quantidadeTotalElementos) {
        this.quantidadeTotalElementos = quantidadeTotalElementos;
    }

    public String getSQLOrderBy(){
        StringBuilder orderBy = new StringBuilder();
        if ((getSortMap() != null) && (getSortMap().size() > 0)) {
            for (Map.Entry<String, String> entry : getSortMap().entrySet()){
                if (orderBy.length() >=1){
                    orderBy.append(",");
                }
                orderBy.append(entry.getKey()).append(" ").append(entry.getValue());
            }
        }
        if (orderBy.length() > 0){
            orderBy.insert(0," ORDER BY ");
        }
        return orderBy.toString();
    }

    public String getSQLOrderByUse(){
        StringBuilder orderBy = new StringBuilder();
        if ((getSortMap() != null) && (getSortMap().size() > 0)) {
            for (Map.Entry<String, String> entry : getSortMap().entrySet()){
                if (orderBy.length() >=1){
                    orderBy.append(",");
                }
                orderBy.append(entry.getKey()).append(" ").append(entry.getValue());
            }
        }
        if (orderBy.length() > 0){
            orderBy.insert(0," ORDER BY ");
        }
        return orderBy.toString();
    }

    private void carregarSortMap() {
        if (StringUtils.isBlank(sort)) {
            return;
        }

        final String[] ordenacoes = sort.split(SEPARADOR_ORDENACOES);
        if (ordenacoes.length < 1) {
            return;
        }

        this.sortMap = new HashMap<>();
        for (String ordenacao : ordenacoes) {
            final String[] ordem = ordenacao.split(SEPARADOR_ORDENACAO);
            if (ordem.length < 2) {
                continue;
            }

            this.sortMap.put(ordem[INDEX_CAMPO_ORDENAR].replaceAll(SEPARADOR_FUNCAO,SEPARADOR_ORDENACAO), ordem[INDEX_DIRECAO_ORDENACAO]);
        }
    }
}
